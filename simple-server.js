const express = require('express');

const app = express();

// 基础中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS - 简化配置
app.use((req, res, next) => {
  console.log(`CORS请求: ${req.method} ${req.url}, Origin: ${req.headers.origin}`);

  // 设置CORS头
  res.header('Access-Control-Allow-Origin', 'http://localhost:5174');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Max-Age', '86400');

  // 处理OPTIONS预检请求
  if (req.method === 'OPTIONS') {
    console.log('处理OPTIONS预检请求');
    res.status(204).end();
    return;
  }
  next();
});

// 模拟数据库
const mockData = {
  // 用户预约数据
  bookings: [
    {
      id: 1,
      userId: 1,
      scheduleId: 1,
      courseId: 1,
      courseName: '晨间瑜伽唤醒',
      instructor: '李老师',
      date: '2025-01-20',
      startTime: '08:00',
      endTime: '09:00',
      status: 'confirmed', // pending, confirmed, cancelled, completed
      location: '瑜伽馆A',
      notes: '',
      createdAt: '2025-01-19T10:00:00Z',
      updatedAt: '2025-01-19T10:00:00Z'
    },
    {
      id: 2,
      userId: 1,
      scheduleId: 2,
      courseId: 2,
      courseName: '流瑜伽进阶',
      instructor: '王老师',
      date: '2025-01-21',
      startTime: '14:00',
      endTime: '15:30',
      status: 'pending',
      location: '瑜伽馆B',
      notes: '初次体验',
      createdAt: '2025-01-19T11:00:00Z',
      updatedAt: '2025-01-19T11:00:00Z'
    },
    {
      id: 6,
      userId: 1,
      scheduleId: 7,
      courseId: 7,
      courseName: '办公室瑜伽减压',
      instructor: '王老师',
      date: '2025-07-03',
      startTime: '18:00',
      endTime: '19:00',
      status: 'cancelled',
      location: '瑜伽馆B',
      notes: '通过每日课程表预约',
      createdAt: '2025-07-03T15:03:39.102Z',
      updatedAt: '2025-07-03T15:06:17.943Z',
      cancellationReason: 'Testing cancel functionality',
      cancelledAt: '2025-07-03T15:06:17.943Z'
    },
    {
      id: 5,
      userId: 1,
      scheduleId: 4,
      courseId: 4,
      courseName: '热瑜伽燃脂',
      instructor: '刘老师',
      date: '2025-07-03',
      startTime: '09:00',
      endTime: '10:30',
      status: 'pending',
      location: '瑜伽馆D',
      notes: '通过每日课程表预约',
      createdAt: '2025-07-03T14:48:52.870Z',
      updatedAt: '2025-07-03T14:48:52.870Z'
    },
    {
      id: 4,
      userId: 1,
      scheduleId: 6,
      courseId: 6,
      courseName: '孕妇瑜伽',
      instructor: '赵老师',
      date: '2025-07-03',
      startTime: '15:00',
      endTime: '16:00',
      status: 'pending',
      location: '瑜伽馆A',
      notes: '通过每日课程表预约',
      createdAt: '2025-07-03T14:48:36.419Z',
      updatedAt: '2025-07-03T14:48:36.419Z'
    },
    {
      id: 3,
      userId: 1,
      scheduleId: 5,
      courseId: 5,
      courseName: '空中瑜伽体验',
      instructor: '陈老师',
      date: '2025-07-03',
      startTime: '11:00',
      endTime: '12:00',
      status: 'pending',
      location: '空中瑜伽室',
      notes: '测试预约空中瑜伽',
      createdAt: '2025-07-03T14:42:49.953Z',
      updatedAt: '2025-07-03T14:42:49.953Z'
    },
    {
      id: 7,
      userId: 1,
      scheduleId: 10,
      courseId: 10,
      courseName: '力量瑜伽',
      instructor: '刘老师',
      date: '2025-07-04',
      startTime: '10:00',
      endTime: '11:30',
      status: 'pending',
      location: '瑜伽馆D',
      notes: '测试取消功能',
      createdAt: '2025-07-03T23:00:00.000Z',
      updatedAt: '2025-07-03T23:00:00.000Z'
    }
  ],
  // 课程时间表数据
  schedules: [
    // 现有课程
    {
      id: 1,
      courseId: 1,
      courseName: '晨间瑜伽唤醒',
      instructor: '李老师',
      instructorId: 1,
      date: '2025-01-20',
      startTime: '08:00',
      endTime: '09:00',
      maxCapacity: 15,
      currentBookings: 8,
      status: 'available',
      location: '瑜伽馆A',
      description: '温和的晨间瑜伽练习，唤醒身体活力',
      price: 88
    },
    {
      id: 2,
      courseId: 2,
      courseName: '流瑜伽进阶',
      instructor: '王老师',
      instructorId: 2,
      date: '2025-01-20',
      startTime: '14:00',
      endTime: '15:30',
      maxCapacity: 12,
      currentBookings: 5,
      status: 'available',
      location: '瑜伽馆B',
      description: '动态流畅的瑜伽序列，适合有基础的学员',
      price: 128
    },
    {
      id: 3,
      courseId: 3,
      courseName: '阴瑜伽放松',
      instructor: '张老师',
      instructorId: 3,
      date: '2025-01-20',
      startTime: '19:00',
      endTime: '20:30',
      maxCapacity: 20,
      currentBookings: 15,
      status: 'available',
      location: '瑜伽馆C',
      description: '深度放松的阴瑜伽练习，释放压力',
      price: 98
    },
    // 今天的课程 (当前日期)
    {
      id: 4,
      courseId: 4,
      courseName: '热瑜伽燃脂',
      instructor: '刘老师',
      instructorId: 4,
      date: new Date().toISOString().split('T')[0],
      startTime: '09:00',
      endTime: '10:30',
      maxCapacity: 16,
      currentBookings: 12,
      status: 'available',
      location: '瑜伽馆D',
      description: '高温环境下的瑜伽练习，有效燃烧脂肪，提升体能',
      price: 168
    },
    {
      id: 5,
      courseId: 5,
      courseName: '空中瑜伽体验',
      instructor: '陈老师',
      instructorId: 5,
      date: new Date().toISOString().split('T')[0],
      startTime: '11:00',
      endTime: '12:00',
      maxCapacity: 8,
      currentBookings: 6,
      status: 'available',
      location: '空中瑜伽室',
      description: '利用吊床进行的空中瑜伽练习，挑战重力，增强核心力量',
      price: 198
    },
    {
      id: 6,
      courseId: 6,
      courseName: '孕妇瑜伽',
      instructor: '赵老师',
      instructorId: 6,
      date: new Date().toISOString().split('T')[0],
      startTime: '15:00',
      endTime: '16:00',
      maxCapacity: 10,
      currentBookings: 7,
      status: 'available',
      location: '瑜伽馆A',
      description: '专为孕期妈妈设计的温和瑜伽练习，安全舒适',
      price: 108
    },
    {
      id: 7,
      courseId: 7,
      courseName: '办公室瑜伽减压',
      instructor: '王老师',
      instructorId: 2,
      date: new Date().toISOString().split('T')[0],
      startTime: '18:00',
      endTime: '19:00',
      maxCapacity: 25,
      currentBookings: 18,
      status: 'available',
      location: '瑜伽馆B',
      description: '针对久坐族的瑜伽练习，缓解肩颈腰部疲劳',
      price: 78
    },
    {
      id: 8,
      courseId: 8,
      courseName: '冥想与呼吸',
      instructor: '张老师',
      instructorId: 3,
      date: new Date().toISOString().split('T')[0],
      startTime: '20:00',
      endTime: '21:00',
      maxCapacity: 30,
      currentBookings: 22,
      status: 'available',
      location: '冥想室',
      description: '深度冥想练习，学习正确的呼吸方法，平静心灵',
      price: 68
    },
    // 明天的课程
    {
      id: 9,
      courseId: 9,
      courseName: '哈他瑜伽基础',
      instructor: '李老师',
      instructorId: 1,
      date: new Date(Date.now() + 24*60*60*1000).toISOString().split('T')[0],
      startTime: '07:30',
      endTime: '08:30',
      maxCapacity: 20,
      currentBookings: 3,
      status: 'available',
      location: '瑜伽馆A',
      description: '适合初学者的哈他瑜伽，学习基础体式和呼吸',
      price: 88
    },
    {
      id: 10,
      courseId: 10,
      courseName: '力量瑜伽',
      instructor: '刘老师',
      instructorId: 4,
      date: new Date(Date.now() + 24*60*60*1000).toISOString().split('T')[0],
      startTime: '10:00',
      endTime: '11:30',
      maxCapacity: 14,
      currentBookings: 8,
      status: 'available',
      location: '瑜伽馆D',
      description: '结合力量训练的瑜伽练习，增强肌肉力量和耐力',
      price: 148
    },
    {
      id: 11,
      courseId: 11,
      courseName: '修复瑜伽',
      instructor: '赵老师',
      instructorId: 6,
      date: new Date(Date.now() + 24*60*60*1000).toISOString().split('T')[0],
      startTime: '16:30',
      endTime: '17:30',
      maxCapacity: 12,
      currentBookings: 9,
      status: 'available',
      location: '瑜伽馆A',
      description: '使用瑜伽砖、毛毯等辅具的修复瑜伽，深度放松身心',
      price: 118
    },
    {
      id: 12,
      courseId: 12,
      courseName: '亲子瑜伽',
      instructor: '陈老师',
      instructorId: 5,
      date: new Date(Date.now() + 24*60*60*1000).toISOString().split('T')[0],
      startTime: '09:30',
      endTime: '10:30',
      maxCapacity: 16,
      currentBookings: 10,
      status: 'available',
      location: '瑜伽馆B',
      description: '家长与孩子一起练习的瑜伽课程，增进亲子关系',
      price: 128
    },
    // 后天和更多课程
    {
      id: 13,
      courseId: 13,
      courseName: '普拉提核心',
      instructor: '杨老师',
      instructorId: 7,
      date: new Date(Date.now() + 2*24*60*60*1000).toISOString().split('T')[0],
      startTime: '08:00',
      endTime: '09:00',
      maxCapacity: 15,
      currentBookings: 5,
      status: 'available',
      location: '普拉提教室',
      description: '专注核心肌群训练的普拉提课程，塑造完美身形',
      price: 138
    },
    {
      id: 14,
      courseId: 14,
      courseName: '瑜伽理疗',
      instructor: '周老师',
      instructorId: 8,
      date: new Date(Date.now() + 2*24*60*60*1000).toISOString().split('T')[0],
      startTime: '14:00',
      endTime: '15:30',
      maxCapacity: 8,
      currentBookings: 4,
      status: 'available',
      location: '理疗室',
      description: '针对身体问题的瑜伽理疗课程，专业康复指导',
      price: 208
    },
    {
      id: 15,
      courseId: 15,
      courseName: '舞韵瑜伽',
      instructor: '林老师',
      instructorId: 9,
      date: new Date(Date.now() + 3*24*60*60*1000).toISOString().split('T')[0],
      startTime: '19:00',
      endTime: '20:30',
      maxCapacity: 18,
      currentBookings: 12,
      status: 'available',
      location: '舞蹈教室',
      description: '融合舞蹈元素的瑜伽练习，优雅流畅，提升气质',
      price: 158
    },
    // 一些满员的课程
    {
      id: 16,
      courseId: 16,
      courseName: '网红瑜伽体验',
      instructor: '美美老师',
      instructorId: 10,
      date: new Date(Date.now() + 1*24*60*60*1000).toISOString().split('T')[0],
      startTime: '20:00',
      endTime: '21:00',
      maxCapacity: 10,
      currentBookings: 10,
      status: 'full',
      location: '瑜伽馆C',
      description: '时下最流行的瑜伽体式组合，拍照打卡必备',
      price: 188
    }
  ],
  // 菜单数据
  menus: [
    {
      menuId: 1,
      menuName: '系统管理',
      parentId: 0,
      orderNum: 1,
      path: '/system',
      component: '#',
      isFrame: 1,
      isCache: 0,
      menuType: 'M',
      visible: '0',
      status: '0',
      perms: '',
      icon: 'system',
      children: [
        {
          menuId: 100,
          menuName: '用户管理',
          parentId: 1,
          orderNum: 1,
          path: 'user',
          component: 'system/user/index',
          isFrame: 1,
          isCache: 0,
          menuType: 'C',
          visible: '0',
          status: '0',
          perms: 'system:user:list',
          icon: 'user'
        },
        {
          menuId: 101,
          menuName: '角色管理',
          parentId: 1,
          orderNum: 2,
          path: 'role',
          component: 'system/role/index',
          isFrame: 1,
          isCache: 0,
          menuType: 'C',
          visible: '0',
          status: '0',
          perms: 'system:role:list',
          icon: 'peoples'
        },
        {
          menuId: 102,
          menuName: '菜单管理',
          parentId: 1,
          orderNum: 3,
          path: 'menu',
          component: 'system/menu/index',
          isFrame: 1,
          isCache: 0,
          menuType: 'C',
          visible: '0',
          status: '0',
          perms: 'system:menu:list',
          icon: 'tree-table'
        },
        {
          menuId: 103,
          menuName: '课程表管理',
          parentId: 1,
          orderNum: 4,
          path: 'schedule',
          component: 'system/schedule/index',
          isFrame: 1,
          isCache: 0,
          menuType: 'C',
          visible: '0',
          status: '0',
          perms: 'system:schedule:list',
          icon: 'date'
        }
      ]
    },
    {
      menuId: 2,
      menuName: '瑜伽管理',
      parentId: 0,
      orderNum: 2,
      path: '/yoga',
      component: '#',
      isFrame: 1,
      isCache: 0,
      menuType: 'M',
      visible: '0',
      status: '0',
      perms: '',
      icon: 'tree',
      children: [
        {
          menuId: 200,
          menuName: '课程管理',
          parentId: 2,
          orderNum: 1,
          path: 'course',
          component: 'yoga/course/index',
          isFrame: 1,
          isCache: 0,
          menuType: 'C',
          visible: '0',
          status: '0',
          perms: 'yoga:course:list',
          icon: 'education'
        },
        {
          menuId: 201,
          menuName: '教练管理',
          parentId: 2,
          orderNum: 2,
          path: 'instructor',
          component: 'yoga/instructor/index',
          isFrame: 1,
          isCache: 0,
          menuType: 'C',
          visible: '0',
          status: '0',
          perms: 'yoga:instructor:list',
          icon: 'peoples'
        },
        {
          menuId: 202,
          menuName: '预约管理',
          parentId: 2,
          orderNum: 3,
          path: 'booking',
          component: 'yoga/booking/index',
          isFrame: 1,
          isCache: 0,
          menuType: 'C',
          visible: '0',
          status: '0',
          perms: 'yoga:booking:list',
          icon: 'list'
        }
      ]
    }
  ]
};

// 生成ID的辅助函数
const generateId = (array) => {
  return array.length > 0 ? Math.max(...array.map(item => item.id)) + 1 : 1;
};

// 模拟用户数据库
const userDB = [
  {
    userId: 1,
    userName: 'admin',
    password: 'admin123',
    nickName: '管理员',
    email: '<EMAIL>',
    phonenumber: '',
    sex: '1',
    avatar: '',
    status: '0',
    delFlag: '0',
    role: 'admin',
    loginIp: '127.0.0.1',
    createTime: '2024-01-01'
  },
  {
    userId: 2,
    userName: 'user',
    password: 'user123',
    nickName: '普通用户',
    email: '<EMAIL>',
    phonenumber: '',
    sex: '1',
    avatar: '',
    status: '0',
    delFlag: '0',
    role: 'user',
    loginIp: '127.0.0.1',
    createTime: '2024-01-01'
  }
];

// 登录路由
app.post('/login', (req, res) => {
  console.log('Login request:', req.body);
  const { username, password } = req.body;
  
  // 用户名和密码验证
  if (!username || !password) {
    return res.status(400).json({
      msg: '用户名和密码不能为空',
      code: 400
    });
  }
  
  // 查找用户
  const user = userDB.find(u => u.userName === username);
  
  if (!user) {
    return res.status(401).json({
      msg: '用户名不存在',
      code: 401
    });
  }
  
  // 验证密码
  if (user.password !== password) {
    return res.status(401).json({
      msg: '密码错误',
      code: 401
    });
  }
  
  // 登录成功
  res.json({
    msg: '登录成功',
    code: 200,
    token: `eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjEyMzQ1In0.${username}-token`,
    user: {
      userId: user.userId,
      userName: user.userName,
      nickName: user.nickName,
      email: user.email,
      phonenumber: user.phonenumber,
      sex: user.sex,
      avatar: user.avatar,
      status: user.status,
      delFlag: user.delFlag,
      loginIp: '127.0.0.1',
      loginDate: new Date().toISOString(),
      createTime: user.createTime,
      role: user.role
    }
  });
});

// 用户注册接口
app.post('/register', (req, res) => {
  console.log('Register request:', req.body);
  // 简单实现注册功能
  const { username, email, password } = req.body;
  
  // 这里应该有验证逻辑和数据库操作
  // 为了简化，我们只做基本验证
  if (!username || !email || !password) {
    return res.status(400).json({
      msg: '注册失败，缺少必要信息',
      code: 400
    });
  }
  
  // 检查用户名是否已存在
  if (userDB.some(user => user.userName === username)) {
    return res.status(400).json({
      msg: '用户名已存在',
      code: 400
    });
  }
  
  // 检查邮箱是否已存在
  if (userDB.some(user => user.email === email)) {
    return res.status(400).json({
      msg: '邮箱已被使用',
      code: 400
    });
  }
  
  // 创建新用户
  const newUser = {
    userId: userDB.length > 0 ? Math.max(...userDB.map(u => u.userId)) + 1 : 1,
    userName: username,
    password: password,
    nickName: username,
    email: email,
    phonenumber: '',
    sex: '0',
    avatar: '',
    status: '0',
    delFlag: '0',
    role: 'user',
    loginIp: '127.0.0.1',
    createTime: new Date().toISOString().split('T')[0]
  };
  
  // 添加到用户数据库
  userDB.push(newUser);
  
  // 返回成功
  res.json({
    msg: '注册成功',
    code: 200,
    data: {
      username: newUser.userName,
      email: newUser.email
    }
  });
});

// 忘记密码接口
app.post('/reset-password', (req, res) => {
  console.log('Reset password request:', req.body);
  const { email } = req.body;
  
  if (!email) {
    return res.status(400).json({
      msg: '重置密码失败，请提供邮箱地址',
      code: 400
    });
  }
  
  // 检查邮箱是否存在
  const user = userDB.find(u => u.email === email);
  if (!user) {
    return res.status(404).json({
      msg: '邮箱不存在，请检查输入',
      code: 404
    });
  }
  
  // 模拟发送重置密码邮件
  console.log(`已向 ${email} 发送重置密码邮件`);
  
  // 返回成功
  res.json({
    msg: '重置密码邮件已发送，请查收',
    code: 200
  });
});

// 用户信息路由
app.get('/getInfo', (req, res) => {
  console.log('GetInfo request');
  res.json({
    msg: '操作成功',
    code: 200,
    user: {
      userId: 1,
      userName: 'admin',
      nickName: '管理员',
      email: '<EMAIL>'
    },
    roles: ['admin'],
    permissions: ['*:*:*']
  });
});

// 退出登录
app.post('/logout', (req, res) => {
  console.log('Logout request');
  res.json({
    msg: '操作成功',
    code: 200
  });
});

// 获取验证码图片
app.get('/captchaImage', (req, res) => {
  console.log('CaptchaImage request');
  res.json({
    msg: '操作成功',
    code: 200,
    captchaEnabled: false, // 禁用验证码
    uuid: 'test-uuid-123'
  });
});

// ============ 预约管理接口 ============

// 获取每日课程时间表
app.get('/api/schedules/daily', (req, res) => {
  const { date } = req.query;
  console.log('Get daily schedules request:', date);
  
  // 如果指定了日期，筛选该日期的课程，否则返回今天的
  const targetDate = date || new Date().toISOString().split('T')[0];
  const dailySchedules = mockData.schedules.filter(schedule => schedule.date === targetDate);
  
  // 检查用户是否已预约每个课程
  const schedulesWithBookingStatus = dailySchedules.map(schedule => {
    // 查找用户是否已预约该课程
    const userBooking = mockData.bookings.find(booking => 
      booking.userId === 1 && 
      booking.scheduleId === schedule.id && 
      (booking.status === 'pending' || booking.status === 'confirmed')
    );
    
    return {
      ...schedule,
      isBooked: !!userBooking,
      userBookingId: userBooking ? userBooking.id : null,
      // 根据预约状态调整显示状态
      displayStatus: userBooking ? 'booked' : schedule.status
    };
  });
  
  res.json({
    success: true,
    message: '获取每日课程表成功',
    data: {
      date: targetDate,
      schedules: schedulesWithBookingStatus
    }
  });
});

// ============ 课程管理接口 ============

// 获取课程列表
app.get('/api/courses', (req, res) => {
  const { page = 1, limit = 10, search } = req.query;
  console.log('Get courses request:', { page, limit, search });
  
  // 从schedules中提取独特的课程
  const coursesMap = new Map();
  mockData.schedules.forEach(schedule => {
    if (!coursesMap.has(schedule.courseId)) {
      coursesMap.set(schedule.courseId, {
        id: schedule.courseId,
        name: schedule.courseName,
        instructor: schedule.instructor,
        instructorId: schedule.instructorId,
        description: schedule.description,
        price: schedule.price,
        location: schedule.location,
        duration: calculateDuration(schedule.startTime, schedule.endTime),
        rating: Math.floor(Math.random() * 10 + 40) / 10, // 4.0-4.9
        students: Math.floor(Math.random() * 1000 + 500), // 500-1500
        difficulty: ['初级', '中级', '高级'][Math.floor(Math.random() * 3)],
        categories: getRandomCategories(),
        avatar: getInstructorAvatar(schedule.instructorId)
      });
    }
  });
  
  let courses = Array.from(coursesMap.values());
  
  // 搜索过滤
  if (search) {
    courses = courses.filter(course => 
      course.name.includes(search) || 
      course.instructor.includes(search) ||
      course.description.includes(search)
    );
  }
  
  // 分页
  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const startIndex = (pageNum - 1) * limitNum;
  const endIndex = startIndex + limitNum;
  const paginatedCourses = courses.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    message: '获取课程列表成功',
    data: {
      courses: paginatedCourses,
      pagination: {
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(courses.length / limitNum),
        totalItems: courses.length,
        hasNext: endIndex < courses.length,
        hasPrev: pageNum > 1
      }
    }
  });
});

// 获取课程详情
app.get('/api/courses/:id', (req, res) => {
  const { id } = req.params;
  console.log('Get course detail request:', id);
  
  // 查找课程相关的时间表
  const courseSchedules = mockData.schedules.filter(s => s.courseId === parseInt(id));
  
  if (courseSchedules.length === 0) {
    return res.status(404).json({
      success: false,
      message: '课程不存在'
    });
  }
  
  const firstSchedule = courseSchedules[0];
  const course = {
    id: parseInt(id),
    name: firstSchedule.courseName,
    instructor: firstSchedule.instructor,
    instructorId: firstSchedule.instructorId,
    description: firstSchedule.description,
    price: firstSchedule.price,
    location: firstSchedule.location,
    duration: calculateDuration(firstSchedule.startTime, firstSchedule.endTime),
    rating: Math.floor(Math.random() * 10 + 40) / 10,
    students: Math.floor(Math.random() * 1000 + 500),
    difficulty: ['初级', '中级', '高级'][Math.floor(Math.random() * 3)],
    categories: getRandomCategories(),
    avatar: getInstructorAvatar(firstSchedule.instructorId),
    schedules: courseSchedules,
    totalSchedules: courseSchedules.length,
    availableSchedules: courseSchedules.filter(s => s.status === 'available').length
  };
  
  res.json({
    success: true,
    message: '获取课程详情成功',
    data: course
  });
});

// 辅助函数
function calculateDuration(startTime, endTime) {
  const start = new Date(`2000-01-01T${startTime}:00`);
  const end = new Date(`2000-01-01T${endTime}:00`);
  const diffMs = end - start;
  const diffMins = Math.floor(diffMs / (1000 * 60));
  return `${diffMins}分钟`;
}

function getRandomCategories() {
  const allCategories = ['哈他瑜伽', '流瑜伽', '阴瑜伽', '热瑜伽', '空中瑜伽', '修复瑜伽', '冥想', '普拉提'];
  const numCategories = Math.floor(Math.random() * 3) + 1; // 1-3个类别
  const shuffled = allCategories.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, numCategories);
}

function getInstructorAvatar(instructorId) {
  const avatars = {
    1: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80', // 李老师
    2: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80', // 王老师
    3: 'https://images.unsplash.com/photo-1494790108755-2616b612b1e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80', // 张老师
    4: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80', // 刘老师
    5: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80', // 陈老师
    6: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80', // 赵老师
    7: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80', // 杨老师
    8: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80', // 周老师
    9: 'https://images.unsplash.com/photo-1531123897727-8f129e1688ce?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80', // 林老师
    10: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80' // 美美老师
  };
  return avatars[instructorId] || avatars[1];
}

// ============ 后台管理 - 课程表管理接口 ============

// 获取所有课程表（支持分页和筛选）
app.get('/api/admin/schedules', (req, res) => {
  const { page = 1, limit = 10, date, courseName, instructor, status } = req.query;
  console.log('Get admin schedules request:', { page, limit, date, courseName, instructor, status });
  
  let schedules = [...mockData.schedules];
  
  // 筛选条件
  if (date) {
    schedules = schedules.filter(s => s.date === date);
  }
  if (courseName) {
    schedules = schedules.filter(s => s.courseName.includes(courseName));
  }
  if (instructor) {
    schedules = schedules.filter(s => s.instructor.includes(instructor));
  }
  if (status) {
    schedules = schedules.filter(s => s.status === status);
  }
  
  // 按日期和时间排序
  schedules.sort((a, b) => {
    const dateA = new Date(`${a.date}T${a.startTime}`);
    const dateB = new Date(`${b.date}T${b.startTime}`);
    return dateA - dateB;
  });
  
  // 分页
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedSchedules = schedules.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    message: '获取课程表列表成功',
    data: {
      schedules: paginatedSchedules,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(schedules.length / limit),
        totalItems: schedules.length,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

// 获取单个课程表详情
app.get('/api/admin/schedules/:id', (req, res) => {
  const { id } = req.params;
  console.log('Get schedule detail request:', id);
  
  const schedule = mockData.schedules.find(s => s.id === parseInt(id));
  
  if (!schedule) {
    return res.status(404).json({
      success: false,
      message: '课程表不存在'
    });
  }
  
  res.json({
    success: true,
    message: '获取课程表详情成功',
    data: schedule
  });
});

// 创建新课程表
app.post('/api/admin/schedules', (req, res) => {
  const { courseId, courseName, instructor, instructorId, date, startTime, endTime, maxCapacity, location, description, price } = req.body;
  console.log('Create schedule request:', req.body);
  
  // 验证必填字段
  if (!courseName || !instructor || !date || !startTime || !endTime || !maxCapacity || !location || !price) {
    return res.status(400).json({
      success: false,
      message: '缺少必填字段'
    });
  }
  
  // 检查时间冲突（同一教练、同一时间段）
  const timeConflict = mockData.schedules.find(s => 
    s.instructorId === instructorId && 
    s.date === date && 
    ((startTime >= s.startTime && startTime < s.endTime) || 
     (endTime > s.startTime && endTime <= s.endTime) ||
     (startTime <= s.startTime && endTime >= s.endTime))
  );
  
  if (timeConflict) {
    return res.status(400).json({
      success: false,
      message: '该教练在此时间段已有课程安排'
    });
  }
  
  // 创建新课程表
  const newSchedule = {
    id: generateId(mockData.schedules),
    courseId: courseId || generateId(mockData.schedules), // 如果没有指定courseId，生成一个
    courseName,
    instructor,
    instructorId: instructorId || 1,
    date,
    startTime,
    endTime,
    maxCapacity: parseInt(maxCapacity),
    currentBookings: 0,
    status: 'available',
    location,
    description,
    price: parseFloat(price),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  mockData.schedules.push(newSchedule);
  
  res.json({
    success: true,
    message: '课程表创建成功',
    data: newSchedule
  });
});

// 更新课程表
app.put('/api/admin/schedules/:id', (req, res) => {
  const { id } = req.params;
  const { courseId, courseName, instructor, instructorId, date, startTime, endTime, maxCapacity, location, description, price, status } = req.body;
  console.log('Update schedule request:', { id, ...req.body });
  
  const scheduleIndex = mockData.schedules.findIndex(s => s.id === parseInt(id));
  
  if (scheduleIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '课程表不存在'
    });
  }
  
  const schedule = mockData.schedules[scheduleIndex];
  
  // 如果修改了教练、日期或时间，检查时间冲突
  if ((instructorId && instructorId !== schedule.instructorId) || 
      (date && date !== schedule.date) || 
      (startTime && startTime !== schedule.startTime) || 
      (endTime && endTime !== schedule.endTime)) {
    
    const timeConflict = mockData.schedules.find(s => 
      s.id !== parseInt(id) && // 排除当前记录
      s.instructorId === (instructorId || schedule.instructorId) && 
      s.date === (date || schedule.date) && 
      (((startTime || schedule.startTime) >= s.startTime && (startTime || schedule.startTime) < s.endTime) || 
       ((endTime || schedule.endTime) > s.startTime && (endTime || schedule.endTime) <= s.endTime) ||
       ((startTime || schedule.startTime) <= s.startTime && (endTime || schedule.endTime) >= s.endTime))
    );
    
    if (timeConflict) {
      return res.status(400).json({
        success: false,
        message: '该教练在此时间段已有课程安排'
      });
    }
  }
  
  // 如果减少了最大容量，检查当前预约数
  if (maxCapacity && parseInt(maxCapacity) < schedule.currentBookings) {
    return res.status(400).json({
      success: false,
      message: '最大容量不能小于当前预约数'
    });
  }
  
  // 更新课程表
  mockData.schedules[scheduleIndex] = {
    ...schedule,
    courseId: courseId || schedule.courseId,
    courseName: courseName || schedule.courseName,
    instructor: instructor || schedule.instructor,
    instructorId: instructorId || schedule.instructorId,
    date: date || schedule.date,
    startTime: startTime || schedule.startTime,
    endTime: endTime || schedule.endTime,
    maxCapacity: maxCapacity ? parseInt(maxCapacity) : schedule.maxCapacity,
    location: location || schedule.location,
    description: description || schedule.description,
    price: price ? parseFloat(price) : schedule.price,
    status: status || schedule.status,
    updatedAt: new Date().toISOString()
  };
  
  res.json({
    success: true,
    message: '课程表更新成功',
    data: mockData.schedules[scheduleIndex]
  });
});

// 删除课程表
app.delete('/api/admin/schedules/:id', (req, res) => {
  const { id } = req.params;
  console.log('Delete schedule request:', id);
  
  const scheduleIndex = mockData.schedules.findIndex(s => s.id === parseInt(id));
  
  if (scheduleIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '课程表不存在'
    });
  }
  
  const schedule = mockData.schedules[scheduleIndex];
  
  // 检查是否有活跃的预约
  const activeBookings = mockData.bookings.filter(b => 
    b.scheduleId === parseInt(id) && (b.status === 'pending' || b.status === 'confirmed')
  );
  
  if (activeBookings.length > 0) {
    return res.status(400).json({
      success: false,
      message: '该课程表有活跃预约，无法删除'
    });
  }
  
  // 删除课程表
  mockData.schedules.splice(scheduleIndex, 1);
  
  res.json({
    success: true,
    message: '课程表删除成功'
  });
});

// 批量删除课程表
app.delete('/api/admin/schedules', (req, res) => {
  const { ids } = req.body;
  console.log('Batch delete schedules request:', ids);
  
  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({
      success: false,
      message: '请提供要删除的课程表ID列表'
    });
  }
  
  const deletedSchedules = [];
  const errors = [];
  
  ids.forEach(id => {
    const scheduleIndex = mockData.schedules.findIndex(s => s.id === parseInt(id));
    
    if (scheduleIndex === -1) {
      errors.push(`课程表ID ${id} 不存在`);
      return;
    }
    
    // 检查是否有活跃的预约
    const activeBookings = mockData.bookings.filter(b => 
      b.scheduleId === parseInt(id) && (b.status === 'pending' || b.status === 'confirmed')
    );
    
    if (activeBookings.length > 0) {
      errors.push(`课程表ID ${id} 有活跃预约，无法删除`);
      return;
    }
    
    deletedSchedules.push(mockData.schedules[scheduleIndex]);
    mockData.schedules.splice(scheduleIndex, 1);
  });
  
  res.json({
    success: true,
    message: `成功删除 ${deletedSchedules.length} 个课程表`,
    data: {
      deleted: deletedSchedules,
      errors: errors
    }
  });
});

// 创建预约
app.post('/api/bookings', (req, res) => {
  const { scheduleId, notes } = req.body;
  console.log('Create booking request:', req.body);
  
  // 检查时间段是否存在
  const schedule = mockData.schedules.find(s => s.id === parseInt(scheduleId));
  if (!schedule) {
    return res.status(404).json({
      success: false,
      message: '课程时间段不存在'
    });
  }
  
  // 检查是否已满
  if (schedule.currentBookings >= schedule.maxCapacity) {
    return res.status(400).json({
      success: false,
      message: '课程已满，无法预约'
    });
  }
  
  // 检查用户是否已预约该时间段
  const existingBooking = mockData.bookings.find(b => 
    b.userId === 1 && b.scheduleId === parseInt(scheduleId) && 
    (b.status === 'pending' || b.status === 'confirmed')
  );
  
  if (existingBooking) {
    return res.status(400).json({
      success: false,
      message: '您已预约该课程时间段'
    });
  }
  
  // 创建新预约
  const newBooking = {
    id: generateId(mockData.bookings),
    userId: 1, // 当前登录用户
    scheduleId: parseInt(scheduleId),
    courseId: schedule.courseId,
    courseName: schedule.courseName,
    instructor: schedule.instructor,
    date: schedule.date,
    startTime: schedule.startTime,
    endTime: schedule.endTime,
    status: 'pending',
    location: schedule.location,
    notes: notes || '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  mockData.bookings.push(newBooking);
  
  // 更新时间段的预约数量
  schedule.currentBookings += 1;
  if (schedule.currentBookings >= schedule.maxCapacity) {
    schedule.status = 'full';
  }
  
  res.json({
    success: true,
    message: '预约创建成功',
    data: newBooking
  });
});

// 获取用户预约列表
app.get('/api/bookings', (req, res) => {
  const { status, page = 1, limit = 10 } = req.query;
  console.log('Get user bookings request:', { status, page, limit });
  
  // 获取当前用户的预约
  let userBookings = mockData.bookings.filter(booking => booking.userId === 1);
  
  // 状态筛选
  if (status && status !== 'all') {
    userBookings = userBookings.filter(booking => booking.status === status);
  }
  
  // 按创建时间倒序排序
  userBookings.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  
  // 分页
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedBookings = userBookings.slice(startIndex, endIndex);
  
  res.json({
    success: true,
    message: '获取预约列表成功',
    data: {
      bookings: paginatedBookings,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(userBookings.length / limit),
        totalItems: userBookings.length,
        itemsPerPage: parseInt(limit)
      }
    }
  });
});

// 获取单个预约详情
app.get('/api/bookings/:id', (req, res) => {
  const { id } = req.params;
  console.log('Get booking detail request:', id);
  
  const booking = mockData.bookings.find(b => b.id === parseInt(id) && b.userId === 1);
  
  if (!booking) {
    return res.status(404).json({
      success: false,
      message: '预约不存在'
    });
  }
  
  res.json({
    success: true,
    message: '获取预约详情成功',
    data: booking
  });
});

// 取消预约
app.patch('/api/bookings/:id/cancel', (req, res) => {
  // 直接设置CORS头
  res.header('Access-Control-Allow-Origin', 'http://localhost:5174');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');

  const bookingId = parseInt(req.params.id)
  const { reason } = req.body

  console.log(`收到取消预约请求: ID=${bookingId}, 原因=${reason}`)

  // 查找预约
  const bookingIndex = mockData.bookings.findIndex(b => b.id === bookingId)
  
  if (bookingIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '预约不存在'
    })
  }
  
  // 更新预约状态
  mockData.bookings[bookingIndex].status = 'cancelled'
  mockData.bookings[bookingIndex].cancellationReason = reason || '用户取消预约'
  mockData.bookings[bookingIndex].cancelledAt = new Date().toISOString()
  mockData.bookings[bookingIndex].updatedAt = new Date().toISOString()
  
  res.json({
    success: true,
    message: '预约已取消',
    data: mockData.bookings[bookingIndex]
  })
})

// 添加POST方法的取消预约API
app.post('/api/bookings/:id/cancel', (req, res) => {
  const bookingId = parseInt(req.params.id)
  const { reason } = req.body
  
  // 查找预约
  const bookingIndex = mockData.bookings.findIndex(b => b.id === bookingId)
  
  if (bookingIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '预约不存在'
    })
  }
  
  // 更新预约状态
  mockData.bookings[bookingIndex].status = 'cancelled'
  mockData.bookings[bookingIndex].cancellationReason = reason || '用户取消预约'
  mockData.bookings[bookingIndex].cancelledAt = new Date().toISOString()
  mockData.bookings[bookingIndex].updatedAt = new Date().toISOString()
  
  res.json({
    success: true,
    message: '预约已取消',
    data: mockData.bookings[bookingIndex]
  })
})

// ============ 菜单管理接口 ============

// 获取路由
app.get('/getRouters', (req, res) => {
  console.log('Get routers request');
  
  // 构建菜单树
  const buildMenuTree = (menus, parentId = 0) => {
    return menus
      .filter(menu => menu.parentId === parentId && menu.status === '0')
      .sort((a, b) => a.orderNum - b.orderNum)
      .map(menu => {
        const children = buildMenuTree(menus, menu.menuId);
        return {
          ...menu,
          children: children.length > 0 ? children : undefined
        };
      });
  };
  
  // 扁平化所有菜单（包括子菜单）
  const flattenMenus = (menus) => {
    const result = [];
    menus.forEach(menu => {
      if (menu.children) {
        result.push(...menu.children);
        result.push(...flattenMenus(menu.children));
      } else {
        result.push(menu);
      }
    });
    return result;
  };
  
  const allMenus = [];
  mockData.menus.forEach(menu => {
    allMenus.push(menu);
    if (menu.children) {
      allMenus.push(...menu.children);
    }
  });
  
  const menuTree = buildMenuTree(allMenus);
  
  res.json({
    msg: '操作成功',
    code: 200,
    data: menuTree
  });
});

// 获取菜单列表
app.get('/system/menu/list', (req, res) => {
  console.log('Get menu list request:', req.query);
  
  // 扁平化所有菜单
  const allMenus = [];
  mockData.menus.forEach(menu => {
    allMenus.push(menu);
    if (menu.children) {
      allMenus.push(...menu.children);
    }
  });
  
  res.json({
    msg: '操作成功',
    code: 200,
    data: allMenus
  });
});

// 获取菜单详细
app.get('/system/menu/:menuId', (req, res) => {
  const { menuId } = req.params;
  console.log('Get menu detail request:', menuId);
  
  // 查找菜单
  let targetMenu = null;
  for (const menu of mockData.menus) {
    if (menu.menuId === parseInt(menuId)) {
      targetMenu = menu;
      break;
    }
    if (menu.children) {
      for (const child of menu.children) {
        if (child.menuId === parseInt(menuId)) {
          targetMenu = child;
          break;
        }
      }
    }
  }
  
  if (!targetMenu) {
    return res.status(404).json({
      msg: '菜单不存在',
      code: 500
    });
  }
  
  res.json({
    msg: '操作成功',
    code: 200,
    data: targetMenu
  });
});

// 获取菜单下拉树结构
app.get('/system/menu/treeselect', (req, res) => {
  console.log('Get menu treeselect request');
  
  // 构建菜单树
  const buildMenuTree = (menus, parentId = 0) => {
    return menus
      .filter(menu => menu.parentId === parentId)
      .sort((a, b) => a.orderNum - b.orderNum)
      .map(menu => {
        const children = buildMenuTree(menus, menu.menuId);
        return {
          menuId: menu.menuId,
          menuName: menu.menuName,
          children: children.length > 0 ? children : undefined
        };
      });
  };
  
  const allMenus = [];
  mockData.menus.forEach(menu => {
    allMenus.push(menu);
    if (menu.children) {
      allMenus.push(...menu.children);
    }
  });
  
  const menuTree = buildMenuTree(allMenus);
  
  res.json({
    msg: '操作成功',
    code: 200,
    data: menuTree
  });
});

// 新增菜单
app.post('/system/menu', (req, res) => {
  console.log('Add menu request:', req.body);
  
  const menuData = req.body;
  
  // 生成新的菜单ID
  const allMenus = [];
  mockData.menus.forEach(menu => {
    allMenus.push(menu);
    if (menu.children) {
      allMenus.push(...menu.children);
    }
  });
  
  const newMenuId = allMenus.length > 0 ? Math.max(...allMenus.map(menu => menu.menuId)) + 1 : 1;
  
  const newMenu = {
    menuId: newMenuId,
    ...menuData,
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  };
  
  // 根据父级ID添加到对应位置
  if (menuData.parentId === 0) {
    mockData.menus.push(newMenu);
  } else {
    // 查找父菜单
    for (const menu of mockData.menus) {
      if (menu.menuId === menuData.parentId) {
        if (!menu.children) {
          menu.children = [];
        }
        menu.children.push(newMenu);
        break;
      }
    }
  }
  
  res.json({
    msg: '操作成功',
    code: 200,
    data: newMenu
  });
});

// 修改菜单
app.put('/system/menu', (req, res) => {
  console.log('Update menu request:', req.body);
  
  const menuData = req.body;
  
  // 查找并更新菜单
  let updated = false;
  
  for (const menu of mockData.menus) {
    if (menu.menuId === menuData.menuId) {
      Object.assign(menu, menuData, { updateTime: new Date().toISOString() });
      updated = true;
      break;
    }
    if (menu.children) {
      for (const child of menu.children) {
        if (child.menuId === menuData.menuId) {
          Object.assign(child, menuData, { updateTime: new Date().toISOString() });
          updated = true;
          break;
        }
      }
    }
  }
  
  if (!updated) {
    return res.status(404).json({
      msg: '菜单不存在',
      code: 500
    });
  }
  
  res.json({
    msg: '操作成功',
    code: 200
  });
});

// 删除菜单
app.delete('/system/menu/:menuId', (req, res) => {
  const { menuId } = req.params;
  console.log('Delete menu request:', menuId);
  
  const id = parseInt(menuId);
  let deleted = false;
  
  // 从顶级菜单中删除
  const topIndex = mockData.menus.findIndex(menu => menu.menuId === id);
  if (topIndex !== -1) {
    mockData.menus.splice(topIndex, 1);
    deleted = true;
  } else {
    // 从子菜单中删除
    for (const menu of mockData.menus) {
      if (menu.children) {
        const childIndex = menu.children.findIndex(child => child.menuId === id);
        if (childIndex !== -1) {
          menu.children.splice(childIndex, 1);
          deleted = true;
          break;
        }
      }
    }
  }
  
  if (!deleted) {
    return res.status(404).json({
      msg: '菜单不存在',
      code: 500
    });
  }
  
  res.json({
    msg: '操作成功',
    code: 200
  });
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// 启动服务器
app.listen(3000, () => {
  console.log('Yoga Backend Server running on http://localhost:3000');
  console.log('Available API endpoints:');
  console.log('- POST /login');
  console.log('- GET /getInfo');
  console.log('- POST /logout');
  console.log('- GET /captchaImage');
  console.log('- GET /api/schedules/daily');
  console.log('- POST /api/bookings');
  console.log('- GET /api/bookings');
  console.log('- GET /api/bookings/:id');
  console.log('- PATCH /api/bookings/:id/cancel');
}); 
