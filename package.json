{"name": "yoga-backend", "version": "1.0.0", "description": "安全的瑜伽学习平台后端API服务", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only -r tsconfig-paths/register src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["yoga", "booking", "api", "typescript", "express"], "author": "Yoga Team", "license": "MIT", "engines": {"node": ">=16.0.0"}, "dependencies": {"@types/morgan": "^1.9.10", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^4.21.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "multer": "^2.0.1", "rate-limit": "^0.1.1", "redis": "^5.5.6", "winston": "^3.17.0"}, "devDependencies": {"@tsconfig/node18": "^18.2.4", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "@types/node": "^24.0.8", "@types/redis": "^4.0.10", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.0", "jest": "^30.0.3", "mongodb-memory-server": "^10.1.4", "prettier": "^3.6.2", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}