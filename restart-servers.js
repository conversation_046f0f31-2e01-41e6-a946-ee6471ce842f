const { exec } = require('child_process');

console.log('正在停止现有服务器...');

// 停止现有进程
exec('pkill -f "node.*simple-server" && pkill -f "ts-node-dev"', (error) => {
  if (error) {
    console.log('停止进程时出现错误（这是正常的）:', error.message);
  }
  
  console.log('等待2秒后启动新服务器...');
  setTimeout(() => {
    // 启动新的服务器
    exec('node simple-server.js', { cwd: __dirname }, (error, stdout, stderr) => {
      if (error) {
        console.error('启动服务器失败:', error);
        return;
      }
      console.log('服务器输出:', stdout);
      if (stderr) console.error('服务器错误:', stderr);
    });
    
    console.log('服务器已在后台启动');
  }, 2000);
}); 