import { config } from '../../src/config';

describe('Configuration', () => {
  test('should have required configuration values', () => {
    expect(config.app).toBeDefined();
    expect(config.app.name).toBe('Yoga Backend API');
    expect(config.app.version).toBe('1.0.0');
    expect(config.app.env).toBeDefined();
    expect(config.app.port).toBeGreaterThan(0);
  });

  test('should have database configuration', () => {
    expect(config.database).toBeDefined();
    expect(config.database.mongoUri).toBeDefined();
    expect(config.database.redisUrl).toBeDefined();
  });

  test('should have JWT configuration', () => {
    expect(config.jwt).toBeDefined();
    expect(config.jwt.secret).toBeDefined();
    expect(config.jwt.refreshSecret).toBeDefined();
    expect(config.jwt.expiresIn).toBeDefined();
  });

  test('should have security configuration', () => {
    expect(config.security).toBeDefined();
    expect(config.security.bcryptRounds).toBeGreaterThanOrEqual(10);
    expect(config.security.rateLimitWindowMs).toBeGreaterThan(0);
    expect(config.security.rateLimitMaxRequests).toBeGreaterThan(0);
  });

  test('should have CORS configuration', () => {
    expect(config.cors).toBeDefined();
    expect(config.cors.origin).toBeDefined();
    expect(Array.isArray(config.cors.origin)).toBe(true);
  });
}); 