import request from 'supertest';
import app from '../../src/index';
import { setupTestDatabase, teardownTestDatabase, clearTestDatabase } from '../setup';

describe('Booking System API', () => {
  beforeAll(async () => {
    await setupTestDatabase();
  });

  beforeEach(async () => {
    await clearTestDatabase();
    // 添加延迟避免限流
    await new Promise(resolve => setTimeout(resolve, 100));
  });

  afterAll(async () => {
    await teardownTestDatabase();
  });

  describe('GET /api/v1/schedules/available', () => {
    it('should return available schedules (empty for now)', async () => {
      const response = await request(app)
        .get('/api/v1/schedules/available')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('获取可用时间段成功');
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('GET /api/v1/schedules/list', () => {
    it('should return all schedules (public endpoint)', async () => {
      const response = await request(app)
        .get('/api/v1/schedules/list')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('获取时间段列表成功');
      expect(response.body.data).toBeDefined();
      expect(response.body.pagination).toBeDefined();
    });
  });

  describe('Protected Endpoints', () => {
    it('should require authentication for booking creation', async () => {
      const response = await request(app)
        .post('/api/v1/bookings')
        .send({
          scheduleId: '507f1f77bcf86cd799439011',
          notes: 'Test booking'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('访问令牌缺失或格式错误');
    });

    it('should require authentication for schedule creation', async () => {
      const response = await request(app)
        .post('/api/v1/schedules')
        .send({
          courseId: '507f1f77bcf86cd799439011',
          instructorId: '507f1f77bcf86cd799439011',
          startTime: new Date(),
          endTime: new Date(Date.now() + 3600000),
          maxCapacity: 20,
          location: 'Test Studio'
        })
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('访问令牌缺失或格式错误');
    });
  });

  describe('API Documentation', () => {
    it('should include booking and schedule endpoints in API info', async () => {
      const response = await request(app)
        .get('/api/v1')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.endpoints).toHaveProperty('schedules');
      expect(response.body.endpoints).toHaveProperty('bookings');
      expect(response.body.endpoints.schedules).toBe('/api/v1/schedules');
      expect(response.body.endpoints.bookings).toBe('/api/v1/bookings');
    });
  });
}); 