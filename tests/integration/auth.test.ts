import request from 'supertest';
import app from '../../src/index';
import { setupTestDatabase, teardownTestDatabase, clearTestDatabase } from '../setup';

describe('Authentication API', () => {
  // 声明测试中使用的变量
  let userEmail: string;
  let accessToken: string;
  let refreshToken: string;
  let userId: string;

  beforeAll(async () => {
    // 设置测试数据库
    await setupTestDatabase();
  });

  beforeEach(async () => {
    // 清理数据库
    await clearTestDatabase();
    // 添加延迟避免限流
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 设置测试数据
    userEmail = `test-${Date.now()}@example.com`;
  });

  afterAll(async () => {
    // 清理并断开连接
    await teardownTestDatabase();
  });

  describe('POST /api/v1/auth/register', () => {
    it('should register a new user successfully', async () => {
      const registerData = {
        email: `test-${Date.now()}@example.com`,
        password: 'Test123!@#',
        confirmPassword: 'Test123!@#',
        username: `testuser${Date.now()}`,
        agreeToTerms: true
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(registerData);

      console.log('Registration response:', response.status, response.body);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('accessToken');
      expect(response.body.data).toHaveProperty('refreshToken');
    });

    it('should fail with invalid email', async () => {
      const registerData = {
        email: 'invalid-email',
        password: 'Test123!@#',
        confirmPassword: 'Test123!@#',
        username: 'testuser',
        agreeToTerms: true
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(registerData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should fail without agreeToTerms', async () => {
      const registerData = {
        email: `test-${Date.now()}@example.com`,
        password: 'Test123!@#',
        confirmPassword: 'Test123!@#',
        username: `testuser${Date.now()}`
        // agreeToTerms: false - 故意省略或设为false
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(registerData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should fail with weak password', async () => {
      const registerData = {
        email: userEmail,
        password: '123',
        confirmPassword: '123',
        username: 'testuser',
        agreeToTerms: true
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(registerData)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should fail with duplicate email', async () => {
      const registerData = {
        email: userEmail,
        password: 'Test123!@#',
        confirmPassword: 'Test123!@#',
        username: 'testuser1',
        agreeToTerms: true
      };

      // 第一次注册
      await request(app)
        .post('/api/v1/auth/register')
        .send(registerData)
        .expect(201);

      // 第二次注册相同邮箱
      const duplicateData = {
        ...registerData,
        username: 'testuser2'
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(duplicateData)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('邮箱已被注册');
    });
  });

  describe('POST /api/v1/auth/login', () => {
    let username: string;

    beforeEach(async () => {
      // 先注册一个用户
      username = `testuser${Date.now()}`;
      
      const registerData = {
        email: userEmail,
        password: 'Test123!@#',
        confirmPassword: 'Test123!@#',
        username: username,
        agreeToTerms: true
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(registerData);

      expect(response.status).toBe(201);
    });

    it('should login with email successfully', async () => {
      const loginData = {
        emailOrUsername: userEmail,
        password: 'Test123!@#'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('accessToken');
      expect(response.body.data).toHaveProperty('refreshToken');

      accessToken = response.body.data.accessToken;
      refreshToken = response.body.data.refreshToken;
    });

    it('should login with username successfully', async () => {
      const loginData = {
        emailOrUsername: username,
        password: 'Test123!@#'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should fail with wrong password', async () => {
      const loginData = {
        emailOrUsername: userEmail,
        password: 'WrongPassword'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('should fail with non-existent user', async () => {
      const loginData = {
        emailOrUsername: '<EMAIL>',
        password: 'Test123!@#'
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('用户不存在');
    });
  });

  describe('POST /api/v1/auth/refresh-token', () => {
    beforeEach(async () => {
      // 登录获取tokens
      const registerData = {
        email: userEmail,
        password: 'Test123!@#',
        confirmPassword: 'Test123!@#',
        username: `testuser${Date.now()}`,
        agreeToTerms: true
      };

      await request(app)
        .post('/api/v1/auth/register')
        .send(registerData);

      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          emailOrUsername: userEmail,
          password: 'Test123!@#'
        });

      accessToken = loginResponse.body.data.accessToken;
      refreshToken = loginResponse.body.data.refreshToken;
    });

    it('should refresh token successfully', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh-token')
        .send({ refreshToken })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('accessToken');
      expect(response.body.data).toHaveProperty('refreshToken');
    });

    it('should fail with invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh-token')
        .send({ refreshToken: 'invalid-token' })
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/v1/auth/profile', () => {
    beforeEach(async () => {
      // 注册并登录
      const registerData = {
        email: userEmail,
        password: 'Test123!@#',
        confirmPassword: 'Test123!@#',
        username: `testuser${Date.now()}`,
        agreeToTerms: true
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(registerData);

      accessToken = response.body.data.accessToken;
      userId = response.body.data.user.id;
    });

    it('should get user profile successfully', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userEmail);
      expect(response.body.data.user).not.toHaveProperty('password');
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .expect(401);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/auth/logout', () => {
    beforeEach(async () => {
      // 注册并登录
      const registerData = {
        email: userEmail,
        password: 'Test123!@#',
        confirmPassword: 'Test123!@#',
        username: `testuser${Date.now()}`,
        agreeToTerms: true
      };

      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(registerData);

      accessToken = response.body.data.accessToken;
    });

    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({}) // Send empty body
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('退出登录成功');
    });
  });
}); 