// Jest setup file

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';
process.env.MONGODB_URI = 'mongodb://localhost:27017/yoga_app_test';
process.env.REDIS_URL = 'redis://localhost:6379/1';
process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
process.env.REFRESH_TOKEN_SECRET = 'test_refresh_token_secret_key_for_testing_only';

// 增加测试超时时间
jest.setTimeout(30000);

// 全局测试清理
afterEach(() => {
  jest.clearAllMocks();
});

// 控制台警告过滤
const originalConsoleWarn = console.warn;
console.warn = (...args: any[]) => {
  // 过滤掉一些无关紧要的警告
  if (args[0] && typeof args[0] === 'string') {
    if (args[0].includes('deprecated') || args[0].includes('experimental')) {
      return;
    }
  }
  originalConsoleWarn(...args);
};

import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';

let mongod: MongoMemoryServer;

export const setupTestDatabase = async (): Promise<void> => {
  // 如果已经有连接，先断开
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }

  mongod = await MongoMemoryServer.create();
  const uri = mongod.getUri();
  
  await mongoose.connect(uri, {
    bufferCommands: false,
  });
};

export const teardownTestDatabase = async (): Promise<void> => {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  }
  if (mongod) {
    await mongod.stop();
  }
};

export const clearTestDatabase = async (): Promise<void> => {
  if (mongoose.connection.readyState !== 0) {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
  }
}; 