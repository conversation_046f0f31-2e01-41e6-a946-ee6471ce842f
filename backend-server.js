const express = require('express');
const cors = require('cors');

const app = express();

// CORS配置 - 允许前端访问
app.use(cors({
  origin: ['http://localhost', 'http://localhost:80', 'http://localhost:8080'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
}));

// 基础中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// 健康检查路由
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'API服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: 'development'
  });
});

// 若依管理系统标准API路径 - 去除验证，直接返回成功
app.post('/login', (req, res) => {
  console.log('Login request received:', req.body);
  res.status(200).json({
    msg: '操作成功',
    code: 200,
    token: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjEyMzQ1In0.test-token'
  });
});

app.get('/getInfo', (req, res) => {
  console.log('GetInfo request received');
  res.status(200).json({
    msg: '操作成功',
    code: 200,
    user: {
      userId: 1,
      userName: 'admin',
      nickName: '管理员',
      email: '<EMAIL>',
      phonenumber: '',
      sex: '1',
      avatar: '',
      status: '0',
      delFlag: '0',
      loginIp: '127.0.0.1',
      loginDate: new Date().toISOString(),
      createTime: '2024-01-01',
      remark: '管理员',
      dept: {
        deptId: 1,
        deptName: '系统部门',
        leader: 'admin'
      }
    },
    roles: ['admin'],
    permissions: ['*:*:*']
  });
});

app.post('/logout', (req, res) => {
  console.log('Logout request received');
  res.status(200).json({
    msg: '操作成功',
    code: 200
  });
});

app.get('/captchaImage', (req, res) => {
  console.log('CaptchaImage request received');
  res.status(200).json({
    msg: '操作成功',
    code: 200,
    captchaEnabled: false, // 禁用验证码
    uuid: 'test-uuid-123'
  });
});

// 404处理
app.use('*', (req, res) => {
  console.log('404 - Route not found:', req.method, req.originalUrl);
  res.status(404).json({
    msg: '路由不存在',
    code: 404
  });
});

// 启动服务器
const startServer = () => {
  try {
    console.log('启动瑜伽平台后端服务器...');

    const server = app.listen(8080, 'localhost', () => {
      console.log(`🚀 服务器启动成功 - http://localhost:8080`);
      console.log(`📚 健康检查: http://localhost:8080/health`);
      console.log(`🌍 环境: development`);
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal) => {
      console.log(`收到 ${signal} 信号，开始优雅关闭...`);
      
      server.close(() => {
        console.log('HTTP 服务器已关闭');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        console.error('强制关闭');
        process.exit(1);
      }, 10000);
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
};

// 启动服务器
startServer(); 