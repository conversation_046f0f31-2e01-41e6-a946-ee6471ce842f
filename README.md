# 瑜伽学习平台后端API

一个安全、现代化的瑜伽学习平台后端服务，使用 Node.js + TypeScript + MongoDB 构建。

## 🚀 特性

- ✅ **安全第一**: JWT认证、数据加密、API限流、XSS防护
- ✅ **现代技术栈**: TypeScript + Express + MongoDB + Redis
- ✅ **完整功能**: 用户管理、课程预约、支付集成、管理后台
- ✅ **高质量代码**: ESLint + Prettier + Jest测试
- ✅ **生产就绪**: Docker支持、日志系统、监控集成

## 📋 主要功能

### 用户功能
- ✅ 用户注册/登录/认证
- ✅ 个人信息管理
- ✅ 瑜伽课程浏览
- ✅ **课程预约系统**（新增）
- 🔄 支付功能
- ✅ 课程评价

### 教练员功能
- ✅ 教练员注册和认证
- ✅ **课程时间段管理**（新增）
- ✅ **预约确认和出席管理**（新增）
- ✅ 个人课程统计

### 管理后台
- ✅ 用户管理
- ✅ 课程内容管理
- ✅ 教练管理
- ✅ **预约系统管理**（新增）
- 🔄 订单财务管理
- ✅ 数据统计分析

## 🛠️ 技术栈

- **运行时**: Node.js 16+
- **语言**: TypeScript
- **框架**: Express.js
- **数据库**: MongoDB + Redis
- **认证**: JWT + Refresh Token
- **测试**: Jest + Supertest
- **代码规范**: ESLint + Prettier
- **日志**: Winston
- **部署**: Docker + PM2

## 📦 安装使用

### 环境要求
- Node.js >= 16.0.0
- MongoDB >= 4.4
- Redis >= 6.0

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd yoga-backend
```

2. **安装依赖**
```bash
npm install
```

3. **环境配置**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **启动开发服务器**
```bash
npm run dev
```

5. **构建项目**
```bash
npm run build
```

6. **启动生产服务器**
```bash
npm start
```

## 📁 项目结构

```
src/
├── config/           # 配置文件
│   ├── index.ts     # 主配置
│   └── database.ts  # 数据库配置
├── controllers/      # 控制器层
├── services/        # 业务逻辑层
├── models/          # 数据模型
├── middleware/      # 中间件
├── routes/          # 路由定义
├── utils/           # 工具函数
│   ├── logger.ts   # 日志工具
│   └── types.ts    # 类型定义
└── index.ts         # 应用入口

tests/               # 测试文件
├── unit/           # 单元测试
├── integration/    # 集成测试
└── setup.ts        # 测试配置

docs/               # 文档
logs/               # 日志文件
uploads/            # 上传文件
```

## 🔧 开发命令

```bash
# 开发模式启动
npm run dev

# 构建项目
npm run build

# 启动生产服务器
npm start

# 运行测试
npm test

# 运行测试并监听文件变化
npm run test:watch

# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 🌍 API端点

### 健康检查
- `GET /health` - 服务健康状态

### 认证模块
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新Token

### 用户模块
- `GET /api/users/profile` - 获取个人信息
- `PUT /api/users/profile` - 更新个人信息

### 课程模块
- `GET /api/v1/courses` - 获取课程列表
- `GET /api/v1/courses/:id` - 获取课程详情

### 预约系统（新增）
- `GET /api/v1/schedules/available` - 获取可用时间段
- `POST /api/v1/schedules` - 创建课程时间段
- `POST /api/v1/bookings` - 创建预约
- `PATCH /api/v1/bookings/:id/cancel` - 取消预约

更多API文档请查看：
- 📚 **预约系统文档**: [docs/BOOKING_SYSTEM.md](docs/BOOKING_SYSTEM.md)
- 🔗 **API接口**: `/api/v1`

## 🔐 环境变量

关键环境变量配置：

```env
# 服务器配置
NODE_ENV=development
PORT=3000

# 数据库
MONGODB_URI=mongodb://localhost:27017/yoga_app
REDIS_URL=redis://localhost:6379

# JWT密钥
JWT_SECRET=your_jwt_secret
REFRESH_TOKEN_SECRET=your_refresh_secret

# 其他配置...
```

## 🧪 测试

```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test -- --coverage

# 运行特定测试文件
npm test -- auth.test.ts
```

## 📝 开发规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 代码规范
- 所有API都需要添加测试
- 提交前运行 `npm run lint` 和 `npm test`
- 使用语义化的提交信息

## 🚢 部署

### Docker 部署
```bash
# 构建镜像
docker build -t yoga-backend .

# 运行容器
docker run -p 3000:3000 yoga-backend
```

### PM2 部署
```bash
# 启动应用
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs
```

## 📈 监控和日志

- 日志文件位于 `logs/` 目录
- 使用 Winston 进行结构化日志记录
- 生产环境建议配置日志收集服务

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 提交 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请创建 Issue 或联系开发团队。

---

**注意**: 请确保在生产环境中更改所有默认密钥和配置！ 