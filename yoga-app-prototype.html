<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瑜伽学习平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        * { font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; }
        .device { width: 375px; height: 812px; background: #000; border-radius: 40px; padding: 8px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); }
        .device::before { content: ''; position: absolute; top: 25px; left: 50%; transform: translateX(-50%); width: 134px; height: 5px; background: #000; border-radius: 3px; z-index: 10; }
        .screen { width: 100%; height: 100%; background: #f8f9ff; border-radius: 32px; overflow: hidden; position: relative; }
        .page { position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; transform: translateX(100%); transition: all 0.3s ease; }
        .page.active { opacity: 1; transform: translateX(0); }
        .glass { background: rgba(255,255,255,0.95); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2); }
        .floating { animation: float 3s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-10px); } }
        .status { height: 44px; display: flex; justify-content: space-between; align-items: center; padding: 0 20px; font-size: 17px; font-weight: 600; }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen p-4">
    <div class="device relative">
        <div class="screen">
            <!-- 登录页面 -->
            <div class="page active" id="login">
                <div class="h-full bg-gradient-to-br from-purple-600 via-blue-500 to-indigo-700 relative overflow-hidden">
                    <div class="status text-white">
                        <span>9:41</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>
                    <div class="absolute top-20 right-10 w-32 h-32 bg-white bg-opacity-10 rounded-full floating"></div>
                    <div class="px-8 pt-16 pb-8 h-full flex flex-col">
                        <div class="text-center mb-12">
                            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-3xl mx-auto mb-6 flex items-center justify-center">
                                <i class="fas fa-spa text-4xl text-white"></i>
                            </div>
                            <h1 class="text-3xl font-bold text-white mb-2">瑜伽学习平台</h1>
                            <p class="text-blue-100">发现内心的平静与力量</p>
                        </div>
                        <div class="flex-1 flex flex-col justify-center">
                            <div class="space-y-4 mb-8">
                                <div class="relative">
                                    <i class="fas fa-envelope absolute left-4 top-4 text-gray-400"></i>
                                    <input type="email" placeholder="邮箱地址" class="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border-0 text-gray-800 placeholder-gray-400 focus:ring-2 focus:ring-purple-300 focus:outline-none">
                                </div>
                                <div class="relative">
                                    <i class="fas fa-lock absolute left-4 top-4 text-gray-400"></i>
                                    <input type="password" placeholder="密码" class="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border-0 text-gray-800 placeholder-gray-400 focus:ring-2 focus:ring-purple-300 focus:outline-none">
                                </div>
                            </div>
                            <button onclick="navigateTo('home')" class="w-full py-4 bg-white text-purple-600 rounded-2xl font-semibold mb-4 hover:bg-gray-50 transition-all transform hover:scale-105">登录</button>
                            <div class="text-center space-y-4">
                                <button class="text-white text-sm underline">忘记密码？</button>
                                <div class="flex items-center justify-center space-x-4 my-6">
                                    <div class="h-px bg-white bg-opacity-30 flex-1"></div>
                                    <span class="text-white text-sm">或</span>
                                    <div class="h-px bg-white bg-opacity-30 flex-1"></div>
                                </div>
                                <button onclick="navigateTo('register')" class="text-white font-medium">还没有账户？<span class="underline">立即注册</span></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 注册页面 -->
            <div class="page" id="register">
                <div class="h-full bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600 relative overflow-hidden">
                    <div class="status text-white">
                        <button onclick="navigateTo('login')" class="flex items-center space-x-2">
                            <i class="fas fa-arrow-left"></i><span>返回</span>
                        </button>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>
                    <div class="px-8 pt-8 pb-8 h-full flex flex-col">
                        <div class="text-center mb-8">
                            <h1 class="text-3xl font-bold text-white mb-2">创建账户</h1>
                            <p class="text-emerald-100">开始你的瑜伽之旅</p>
                        </div>
                        <div class="flex-1 flex flex-col justify-center">
                            <div class="space-y-4 mb-8">
                                <div class="relative">
                                    <i class="fas fa-user absolute left-4 top-4 text-gray-400"></i>
                                    <input type="text" placeholder="姓名" class="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border-0 text-gray-800 placeholder-gray-400 focus:ring-2 focus:ring-emerald-300 focus:outline-none">
                                </div>
                                <div class="relative">
                                    <i class="fas fa-envelope absolute left-4 top-4 text-gray-400"></i>
                                    <input type="email" placeholder="邮箱地址" class="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border-0 text-gray-800 placeholder-gray-400 focus:ring-2 focus:ring-emerald-300 focus:outline-none">
                                </div>
                                <div class="relative">
                                    <i class="fas fa-lock absolute left-4 top-4 text-gray-400"></i>
                                    <input type="password" placeholder="密码" class="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border-0 text-gray-800 placeholder-gray-400 focus:ring-2 focus:ring-emerald-300 focus:outline-none">
                                </div>
                                <div class="relative">
                                    <i class="fas fa-lock absolute left-4 top-4 text-gray-400"></i>
                                    <input type="password" placeholder="确认密码" class="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border-0 text-gray-800 placeholder-gray-400 focus:ring-2 focus:ring-emerald-300 focus:outline-none">
                                </div>
                            </div>
                            <button onclick="navigateTo('home')" class="w-full py-4 bg-white text-emerald-600 rounded-2xl font-semibold mb-4 hover:bg-gray-50 transition-all transform hover:scale-105">注册</button>
                            <p class="text-emerald-100 text-sm text-center leading-relaxed">注册即表示同意我们的<a href="#" class="underline">服务条款</a>和<a href="#" class="underline">隐私政策</a></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主页 -->
            <div class="page" id="home">
                <div class="h-full bg-gray-50">
                    <div class="status text-gray-800">
                        <span>9:41</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>
                    <div class="glass mx-4 rounded-2xl p-4 flex justify-between items-center mb-6">
                        <h1 class="text-xl font-bold text-gray-800">瑜伽学习平台</h1>
                        <button onclick="navigateTo('profile')" class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </button>
                    </div>
                    <div class="mx-4 mb-6">
                        <div class="bg-gradient-to-r from-purple-600 to-blue-500 rounded-3xl p-6 text-white relative overflow-hidden" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'); background-size: cover; background-position: center;">
                            <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-500 opacity-80"></div>
                            <div class="relative z-10">
                                <h2 class="text-2xl font-bold mb-2">发现内心的平静</h2>
                                <p class="text-blue-100 mb-4">专业的瑜伽课程，陪伴你的修行之旅</p>
                                <button onclick="navigateTo('courses')" class="bg-white text-purple-600 px-6 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-colors">开始学习</button>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">快捷功能</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div onclick="navigateTo('courses')" class="bg-white rounded-2xl p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mb-3">
                                    <i class="fas fa-play text-white"></i>
                                </div>
                                <h4 class="font-semibold text-gray-800 mb-1">浏览课程</h4>
                                <p class="text-sm text-gray-600">找到适合你的瑜伽课程</p>
                            </div>
                            <div class="bg-white rounded-2xl p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                                <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center mb-3">
                                    <i class="fas fa-calendar text-white"></i>
                                </div>
                                <h4 class="font-semibold text-gray-800 mb-1">我的预约</h4>
                                <p class="text-sm text-gray-600">查看课程安排</p>
                            </div>
                            <div class="bg-white rounded-2xl p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                                <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center mb-3">
                                    <i class="fas fa-target text-white"></i>
                                </div>
                                <h4 class="font-semibold text-gray-800 mb-1">学习计划</h4>
                                <p class="text-sm text-gray-600">制定个人练习计划</p>
                            </div>
                            <div class="bg-white rounded-2xl p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                                <div class="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-xl flex items-center justify-center mb-3">
                                    <i class="fas fa-chart-line text-white"></i>
                                </div>
                                <h4 class="font-semibold text-gray-800 mb-1">进度统计</h4>
                                <p class="text-sm text-gray-600">追踪你的进步</p>
                            </div>
                        </div>
                    </div>
                    <div class="absolute bottom-0 left-0 right-0 glass border-t border-gray-200">
                        <div class="flex justify-around py-3">
                            <div class="tab-item active flex flex-col items-center py-2 px-4">
                                <i class="fas fa-home text-purple-600 mb-1"></i>
                                <span class="text-xs text-purple-600 font-medium">首页</span>
                            </div>
                            <div onclick="navigateTo('courses')" class="tab-item flex flex-col items-center py-2 px-4 cursor-pointer">
                                <i class="fas fa-play text-gray-400 mb-1"></i>
                                <span class="text-xs text-gray-400">课程</span>
                            </div>
                            <div class="tab-item flex flex-col items-center py-2 px-4 cursor-pointer">
                                <i class="fas fa-calendar text-gray-400 mb-1"></i>
                                <span class="text-xs text-gray-400">预约</span>
                            </div>
                            <div onclick="navigateTo('profile')" class="tab-item flex flex-col items-center py-2 px-4 cursor-pointer">
                                <i class="fas fa-user text-gray-400 mb-1"></i>
                                <span class="text-xs text-gray-400">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 课程列表页 -->
            <div class="page" id="courses">
                <div class="h-full bg-gray-50">
                    <div class="status text-gray-800">
                        <span>9:41</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>
                    <div class="glass mx-4 rounded-2xl p-4 flex justify-between items-center mb-6">
                        <button onclick="navigateTo('home')" class="flex items-center space-x-2 text-purple-600">
                            <i class="fas fa-arrow-left"></i><span>返回</span>
                        </button>
                        <h1 class="text-xl font-bold text-gray-800">课程中心</h1>
                        <button class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-search text-gray-600"></i>
                        </button>
                    </div>
                    <div class="mx-4 mb-6">
                        <div class="relative">
                            <i class="fas fa-search absolute left-4 top-4 text-gray-400"></i>
                            <input type="text" placeholder="搜索课程..." class="w-full pl-12 pr-4 py-4 bg-white rounded-2xl border-0 text-gray-800 placeholder-gray-400 focus:ring-2 focus:ring-purple-300 focus:outline-none shadow-sm">
                        </div>
                    </div>
                    <div class="px-4 mb-6">
                        <div class="flex space-x-3 overflow-x-auto">
                            <button class="bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">全部</button>
                            <button class="bg-white text-gray-600 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap shadow-sm">哈他瑜伽</button>
                            <button class="bg-white text-gray-600 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap shadow-sm">流瑜伽</button>
                            <button class="bg-white text-gray-600 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap shadow-sm">阴瑜伽</button>
                        </div>
                    </div>
                    <div class="px-4 space-y-4 pb-24">
                        <div onclick="navigateTo('course-detail')" class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                            <div class="h-32 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                                <div class="h-full bg-gradient-to-t from-black/50 to-transparent flex items-end p-4">
                                    <i class="fas fa-spa text-2xl text-white"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">哈他瑜伽基础课</h3>
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-sm text-gray-600"><i class="fas fa-clock mr-1"></i>60分钟</span>
                                    <span class="bg-purple-100 text-purple-600 px-2 py-1 rounded-lg text-xs font-medium">初级</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-xl font-bold text-gray-800">¥88</span>
                                    <button class="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium">立即预约</button>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                            <div class="h-32 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                                <div class="h-full bg-gradient-to-t from-black/50 to-transparent flex items-end p-4">
                                    <i class="fas fa-leaf text-2xl text-white"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">流瑜伽进阶课</h3>
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-sm text-gray-600"><i class="fas fa-clock mr-1"></i>75分钟</span>
                                    <span class="bg-emerald-100 text-emerald-600 px-2 py-1 rounded-lg text-xs font-medium">中级</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-xl font-bold text-gray-800">¥128</span>
                                    <button class="bg-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-medium">立即预约</button>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                            <div class="h-32 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1545389336-cf090694435e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                                <div class="h-full bg-gradient-to-t from-black/50 to-transparent flex items-end p-4">
                                    <i class="fas fa-moon text-2xl text-white"></i>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-800 mb-2">阴瑜伽放松课</h3>
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-sm text-gray-600"><i class="fas fa-clock mr-1"></i>90分钟</span>
                                    <span class="bg-blue-100 text-blue-600 px-2 py-1 rounded-lg text-xs font-medium">初级</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-xl font-bold text-gray-800">¥98</span>
                                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium">立即预约</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute bottom-0 left-0 right-0 glass border-t border-gray-200">
                        <div class="flex justify-around py-3">
                            <div onclick="navigateTo('home')" class="tab-item flex flex-col items-center py-2 px-4 cursor-pointer">
                                <i class="fas fa-home text-gray-400 mb-1"></i>
                                <span class="text-xs text-gray-400">首页</span>
                            </div>
                            <div class="tab-item active flex flex-col items-center py-2 px-4">
                                <i class="fas fa-play text-purple-600 mb-1"></i>
                                <span class="text-xs text-purple-600 font-medium">课程</span>
                            </div>
                            <div class="tab-item flex flex-col items-center py-2 px-4 cursor-pointer">
                                <i class="fas fa-calendar text-gray-400 mb-1"></i>
                                <span class="text-xs text-gray-400">预约</span>
                            </div>
                            <div onclick="navigateTo('profile')" class="tab-item flex flex-col items-center py-2 px-4 cursor-pointer">
                                <i class="fas fa-user text-gray-400 mb-1"></i>
                                <span class="text-xs text-gray-400">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 课程详情页 -->
            <div class="page" id="course-detail">
                <div class="h-full bg-white">
                    <div class="h-64 bg-cover bg-center relative" style="background-image: url('https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute top-12 left-4 right-4 flex justify-between items-center text-white">
                            <button onclick="navigateTo('courses')" class="w-10 h-10 bg-black bg-opacity-20 rounded-full flex items-center justify-center">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-sm"></i>
                                <i class="fas fa-wifi text-sm"></i>
                                <i class="fas fa-battery-three-quarters text-sm"></i>
                            </div>
                        </div>
                        <div class="absolute bottom-4 left-4">
                            <i class="fas fa-spa text-4xl text-white"></i>
                        </div>
                    </div>
                    <div class="p-6 pb-24">
                        <h1 class="text-2xl font-bold text-gray-800 mb-4">哈他瑜伽基础课</h1>
                        <div class="flex items-center space-x-4 mb-6">
                            <div class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-clock"></i>
                                <span class="text-sm">60分钟</span>
                            </div>
                            <div class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-signal"></i>
                                <span class="text-sm">初级</span>
                            </div>
                            <div class="flex items-center space-x-1 text-gray-600">
                                <i class="fas fa-users"></i>
                                <span class="text-sm">15人</span>
                            </div>
                        </div>
                        <p class="text-gray-600 mb-6 leading-relaxed">哈他瑜伽是最传统的瑜伽练习方式，注重体式的正确性和呼吸的调节。本课程专为初学者设计，通过基础体式的练习，帮助您建立正确的瑜伽基础。</p>
                        <div class="bg-gray-50 rounded-2xl p-4 mb-6">
                            <h3 class="font-semibold text-gray-800 mb-3">课程教练</h3>
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-semibold">李</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">李瑜伽老师</h4>
                                    <p class="text-sm text-gray-600">5年教学经验 • 哈他瑜伽专业认证</p>
                                </div>
                            </div>
                        </div>
                        <button onclick="showBookingSuccess(this)" class="w-full bg-gradient-to-r from-purple-600 to-blue-500 text-white py-4 rounded-2xl font-semibold text-lg transition-all hover:shadow-lg">立即预约 - ¥88</button>
                    </div>
                </div>
            </div>

            <!-- 个人中心页 -->
            <div class="page" id="profile">
                <div class="h-full bg-gray-50">
                    <div class="status text-gray-800">
                        <span>9:41</span>
                        <div class="flex items-center space-x-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-600 to-blue-500 mx-4 rounded-3xl p-6 text-white mb-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                <span class="text-2xl font-bold">张</span>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold">张同学</h2>
                                <p class="text-blue-100"><EMAIL></p>
                            </div>
                        </div>
                        <div class="flex justify-between">
                            <div class="text-center">
                                <div class="text-2xl font-bold">12</div>
                                <div class="text-sm text-blue-100">已完成课程</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold">3</div>
                                <div class="text-sm text-blue-100">即将开始</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold">8</div>
                                <div class="text-sm text-blue-100">连续天数</div>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 space-y-4 pb-24">
                        <div class="bg-white rounded-2xl overflow-hidden shadow-sm">
                            <div class="flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-calendar text-white"></i>
                                    </div>
                                    <span class="font-medium text-gray-800">我的预约</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="border-t border-gray-100 flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-chart-line text-white"></i>
                                    </div>
                                    <span class="font-medium text-gray-800">学习记录</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="border-t border-gray-100 flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-heart text-white"></i>
                                    </div>
                                    <span class="font-medium text-gray-800">我的收藏</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl overflow-hidden shadow-sm">
                            <div class="flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-crown text-white"></i>
                                    </div>
                                    <span class="font-medium text-gray-800">会员中心</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="border-t border-gray-100 flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-wallet text-white"></i>
                                    </div>
                                    <span class="font-medium text-gray-800">我的钱包</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl overflow-hidden shadow-sm">
                            <div class="flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-gray-500 to-gray-600 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-cog text-white"></i>
                                    </div>
                                    <span class="font-medium text-gray-800">设置</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="border-t border-gray-100 flex items-center justify-between p-4 hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center">
                                        <i class="fas fa-sign-out-alt text-white"></i>
                                    </div>
                                    <span class="font-medium text-gray-800">退出登录</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    <div class="absolute bottom-0 left-0 right-0 glass border-t border-gray-200">
                        <div class="flex justify-around py-3">
                            <div onclick="navigateTo('home')" class="tab-item flex flex-col items-center py-2 px-4 cursor-pointer">
                                <i class="fas fa-home text-gray-400 mb-1"></i>
                                <span class="text-xs text-gray-400">首页</span>
                            </div>
                            <div onclick="navigateTo('courses')" class="tab-item flex flex-col items-center py-2 px-4 cursor-pointer">
                                <i class="fas fa-play text-gray-400 mb-1"></i>
                                <span class="text-xs text-gray-400">课程</span>
                            </div>
                            <div class="tab-item flex flex-col items-center py-2 px-4 cursor-pointer">
                                <i class="fas fa-calendar text-gray-400 mb-1"></i>
                                <span class="text-xs text-gray-400">预约</span>
                            </div>
                            <div class="tab-item active flex flex-col items-center py-2 px-4">
                                <i class="fas fa-user text-purple-600 mb-1"></i>
                                <span class="text-xs text-purple-600 font-medium">我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function navigateTo(pageId) {
            const currentPage = document.querySelector('.page.active');
            const targetPage = document.getElementById(pageId);
            if (currentPage === targetPage) return;
            currentPage.classList.remove('active');
            targetPage.classList.add('active');
            updateTabBar(pageId);
        }

        function updateTabBar(activePageId) {
            document.querySelectorAll('.tab-item').forEach(tab => tab.classList.remove('active'));
            const activeTabs = document.querySelectorAll(`[onclick*="${activePageId}"] .tab-item, .tab-item:has([onclick*="${activePageId}"])`);
            activeTabs.forEach(tab => tab.classList.add('active'));
        }

        function showBookingSuccess(button) {
            const originalText = button.textContent;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>预约中...';
            button.disabled = true;
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-check mr-2"></i>预约成功';
                button.className = 'w-full bg-green-500 text-white py-4 rounded-2xl font-semibold text-lg';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.className = 'w-full bg-gradient-to-r from-purple-600 to-blue-500 text-white py-4 rounded-2xl font-semibold text-lg transition-all hover:shadow-lg';
                    button.disabled = false;
                }, 2000);
            }, 1500);
        }

        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', hour12: false });
            document.querySelectorAll('.status span:first-child').forEach(el => el.textContent = timeString);
        }

        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 60000);
            
            // 分类按钮切换
            document.querySelectorAll('.px-4.py-2.rounded-full').forEach(button => {
                button.addEventListener('click', function() {
                    document.querySelectorAll('.px-4.py-2.rounded-full').forEach(btn => {
                        btn.className = 'bg-white text-gray-600 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap shadow-sm';
                    });
                    this.className = 'bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap';
                });
            });
        });
    </script>
</body>
</html>
