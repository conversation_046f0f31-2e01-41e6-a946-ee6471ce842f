const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());

// 基础路由
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'API服务运行正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: 'development'
  });
});

app.get('/api/v1', (req, res) => {
  res.json({
    success: true,
    message: '瑜伽学习平台 API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/v1/auth',
      users: '/api/v1/users',
      instructors: '/api/v1/instructors'
    }
  });
});

// 注册接口
app.post('/api/v1/auth/register', (req, res) => {
  console.log('Register request:', req.body);
  res.status(201).json({
    success: true,
    message: '注册成功',
    data: {
      userId: '123',
      username: req.body.username,
      email: req.body.email
    }
  });
});

// 登录接口
app.post('/api/v1/auth/login', (req, res) => {
  console.log('API Login request:', req.body);
  res.status(200).json({
    success: true,
    message: '登录成功',
    data: {
      accessToken: 'test-access-token-123',
      refreshToken: 'test-refresh-token-456',
      user: {
        id: '123',
        username: req.body.emailOrUsername,
        email: req.body.emailOrUsername,
        role: 'student'
      }
    }
  });
});

// 获取活跃教练员
app.get('/api/v1/instructors/active', (req, res) => {
  console.log('Get active instructors');
  res.json({
    success: true,
    data: [
      {
        id: '1',
        displayName: '张老师',
        bio: '资深瑜伽教练',
        experience: 5,
        specialties: ['哈他瑜伽', '流瑜伽'],
        averageRating: 4.8
      },
      {
        id: '2',
        displayName: '李老师',
        bio: '专业瑜伽导师',
        experience: 3,
        specialties: ['阿斯汤加', '热瑜伽'],
        averageRating: 4.6
      }
    ]
  });
});

// 404处理
app.use('*', (req, res) => {
  console.log('404 - Route not found:', req.method, req.originalUrl);
  res.status(404).json({
    success: false,
    message: '路由不存在',
    code: 404
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 简化服务器启动成功 - http://localhost:${PORT}`);
  console.log(`📚 健康检查: http://localhost:${PORT}/health`);
  console.log(`🌐 API信息: http://localhost:${PORT}/api/v1`);
}); 