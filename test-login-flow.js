const axios = require('axios');

// 配置基础URL
const baseURL = 'http://127.0.0.1:3001';

async function testLoginFlow() {
    console.log('=== 🧪 测试瑜伽平台登录流程 ===\n');
    
    try {
        // 1. 测试验证码接口
        console.log('1. 📸 获取验证码...');
        const captchaResponse = await axios.get(`${baseURL}/captchaImage`);
        console.log('✅ 验证码接口正常:', captchaResponse.data.msg);
        console.log('   验证码启用:', captchaResponse.data.captchaEnabled);
        console.log('   UUID:', captchaResponse.data.uuid);
        
        // 2. 测试登录接口
        console.log('\n2. 🔐 执行登录...');
        const loginData = {
            username: 'admin',
            password: 'admin123',
            code: '',  // 在真实环境中需要输入验证码
            uuid: captchaResponse.data.uuid
        };
        
        const loginResponse = await axios.post(`${baseURL}/login`, loginData);
        console.log('✅ 登录接口正常:', loginResponse.data.msg);
        console.log('   获得Token:', loginResponse.data.token.substring(0, 50) + '...');
        
        // 3. 测试获取用户信息
        console.log('\n3. 👤 获取用户信息...');
        const userInfoResponse = await axios.get(`${baseURL}/getInfo`, {
            headers: {
                'Authorization': `Bearer ${loginResponse.data.token}`
            }
        });
        console.log('✅ 用户信息接口正常:', userInfoResponse.data.msg);
        console.log('   用户名:', userInfoResponse.data.user.userName);
        console.log('   昵称:', userInfoResponse.data.user.nickName);
        console.log('   角色:', userInfoResponse.data.roles.join(', '));
        console.log('   权限:', userInfoResponse.data.permissions.length > 0 ? userInfoResponse.data.permissions[0] : '无');
        
        // 4. 测试退出登录
        console.log('\n4. 🚪 测试退出登录...');
        const logoutResponse = await axios.post(`${baseURL}/logout`);
        console.log('✅ 退出登录接口正常:', logoutResponse.data.msg);
        
        console.log('\n🎉 所有登录流程测试通过！');
        console.log('\n📋 总结:');
        console.log('• 验证码接口: ✅ 正常');
        console.log('• 登录接口: ✅ 正常');
        console.log('• 用户信息接口: ✅ 正常');
        console.log('• 退出登录接口: ✅ 正常');
        console.log('• 前端代理: ✅ 正常工作');
        console.log('• 后端API: ✅ 正常响应');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('   状态码:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testLoginFlow(); 