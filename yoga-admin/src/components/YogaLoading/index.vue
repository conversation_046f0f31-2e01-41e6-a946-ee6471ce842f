<template>
  <div 
    v-if="show" 
    class="yoga-loading-overlay"
    :class="{ 'with-backdrop': useBackdrop }"
  >
    <div class="yoga-loading-container">
      <!-- 瑜伽姿势动画 -->
      <div v-if="type === 'pose'" class="loading-animation pose-animation">
        <div class="yoga-pose">
          <div class="pose-figure"></div>
          <div class="breathing-circle"></div>
        </div>
      </div>
      
      <!-- 莲花动画 -->
      <div v-else-if="type === 'lotus'" class="loading-animation lotus-animation">
        <div class="lotus-flower">
          <div class="petal" v-for="n in 8" :key="n" :style="{ transform: `rotate(${(n-1) * 45}deg)` }"></div>
        </div>
      </div>
      
      <!-- 呼吸球动画 -->
      <div v-else-if="type === 'breathing'" class="loading-animation breathing-animation">
        <div class="breathing-sphere">
          <div class="sphere inner"></div>
          <div class="sphere middle"></div>
          <div class="sphere outer"></div>
        </div>
      </div>
      
      <!-- 禅意波纹动画 -->
      <div v-else-if="type === 'ripple'" class="loading-animation ripple-animation">
        <div class="zen-ripple">
          <div class="ripple" v-for="n in 3" :key="n" :style="{ animationDelay: `${(n-1) * 0.6}s` }"></div>
        </div>
      </div>
      
      <!-- 脉轮旋转动画 -->
      <div v-else-if="type === 'chakra'" class="loading-animation chakra-animation">
        <div class="chakra-wheel">
          <div class="chakra" v-for="(color, index) in chakraColors" :key="index" 
               :style="{ backgroundColor: color, animationDelay: `${index * 0.1}s` }"></div>
        </div>
      </div>
      
      <!-- Element UI风格旋转器 -->
      <div v-else class="loading-animation element-animation">
        <div class="element-spinner">
          <i class="el-icon-loading"></i>
        </div>
      </div>
      
      <!-- 加载文本 -->
      <div v-if="text" class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'YogaLoading',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'element',
      validator: (value) => ['pose', 'lotus', 'breathing', 'ripple', 'chakra', 'element'].includes(value)
    },
    text: {
      type: String,
      default: ''
    },
    useBackdrop: {
      type: Boolean,
      default: true
    },
    duration: {
      type: Number,
      default: 0 // 0表示不自动隐藏
    }
  },
  data() {
    return {
      chakraColors: [
        '#FF0000', // 红色 - 根轮
        '#FF8C00', // 橙色 - 骶轮  
        '#FFD700', // 黄色 - 脐轮
        '#32CD32', // 绿色 - 心轮
        '#87CEEB', // 蓝色 - 喉轮
        '#4169E1', // 靛蓝 - 眉心轮
        '#8A2BE2'  // 紫色 - 顶轮
      ]
    }
  },
  watch: {
    show(newVal) {
      if (newVal && this.duration > 0) {
        setTimeout(() => {
          this.$emit('update:show', false)
        }, this.duration)
      }
    }
  }
}
</script>

<style scoped>
.yoga-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.yoga-loading-overlay.with-backdrop {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

.yoga-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

/* 基础动画容器 */
.loading-animation {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 瑜伽姿势动画 */
.pose-animation {
  position: relative;
}

.yoga-pose {
  position: relative;
  width: 40px;
  height: 50px;
}

.pose-figure {
  width: 20px;
  height: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pose-sway 3s ease-in-out infinite;
}

.breathing-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: breathing-pulse 2s ease-in-out infinite;
}

@keyframes pose-sway {
  0%, 100% { transform: translate(-50%, -50%) rotate(-5deg); }
  50% { transform: translate(-50%, -50%) rotate(5deg); }
}

@keyframes breathing-pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
}

/* 莲花动画 */
.lotus-flower {
  position: relative;
  width: 50px;
  height: 50px;
  animation: lotus-rotate 4s linear infinite;
}

.petal {
  position: absolute;
  width: 8px;
  height: 20px;
  background: linear-gradient(to top, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  border-radius: 50% 10px 50% 10px;
  top: 0;
  left: 50%;
  transform-origin: 50% 100%;
  animation: petal-bloom 2s ease-in-out infinite alternate;
}

@keyframes lotus-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes petal-bloom {
  0% { transform: translateX(-50%) scale(0.8); }
  100% { transform: translateX(-50%) scale(1.1); }
}

/* 呼吸球动画 */
.breathing-sphere {
  position: relative;
  width: 50px;
  height: 50px;
}

.sphere {
  position: absolute;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.sphere.inner {
  width: 16px;
  height: 16px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  animation: sphere-pulse 2s ease-in-out infinite;
}

.sphere.middle {
  width: 32px;
  height: 32px;
  border: 2px solid rgba(102, 126, 234, 0.4);
  animation: sphere-pulse 2s ease-in-out infinite 0.2s;
}

.sphere.outer {
  width: 48px;
  height: 48px;
  border: 2px solid rgba(102, 126, 234, 0.2);
  animation: sphere-pulse 2s ease-in-out infinite 0.4s;
}

@keyframes sphere-pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.4; }
}

/* 禅意波纹动画 */
.zen-ripple {
  position: relative;
  width: 50px;
  height: 50px;
}

.ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border: 2px solid #667eea;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple-expand 2s ease-out infinite;
}

@keyframes ripple-expand {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 50px;
    height: 50px;
    opacity: 0;
  }
}

/* 脉轮旋转动画 */
.chakra-wheel {
  position: relative;
  width: 50px;
  height: 50px;
  animation: chakra-rotate 3s linear infinite;
}

.chakra {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform-origin: 0 0;
}

.chakra:nth-child(1) { transform: translate(-50%, -50%) rotate(0deg) translateX(20px); }
.chakra:nth-child(2) { transform: translate(-50%, -50%) rotate(51deg) translateX(20px); }
.chakra:nth-child(3) { transform: translate(-50%, -50%) rotate(102deg) translateX(20px); }
.chakra:nth-child(4) { transform: translate(-50%, -50%) rotate(153deg) translateX(20px); }
.chakra:nth-child(5) { transform: translate(-50%, -50%) rotate(204deg) translateX(20px); }
.chakra:nth-child(6) { transform: translate(-50%, -50%) rotate(255deg) translateX(20px); }
.chakra:nth-child(7) { transform: translate(-50%, -50%) rotate(306deg) translateX(20px); }

@keyframes chakra-rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Element UI风格旋转器 */
.element-spinner {
  color: #409EFF;
  font-size: 32px;
  animation: element-spin 1s linear infinite;
}

@keyframes element-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 加载文本 */
.loading-text {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  margin-top: 8px;
  animation: text-fade 2s ease-in-out infinite alternate;
}

@keyframes text-fade {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-animation {
    width: 50px;
    height: 50px;
  }
  
  .loading-text {
    font-size: 12px;
  }
}

/* 暗色主题适配 */
.dark .yoga-loading-overlay.with-backdrop {
  background-color: rgba(0, 0, 0, 0.8);
}

.dark .loading-text {
  color: #E4E7ED;
}
</style> 