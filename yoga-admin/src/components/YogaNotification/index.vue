<template>
  <teleport to="body">
    <transition-group name="notification" tag="div" class="yoga-notification-container">
      <div 
        v-for="notification in notifications" 
        :key="notification.id"
        :class="[
          'yoga-notification', 
          `yoga-notification--${notification.type}`,
          { 'yoga-notification--with-action': notification.action }
        ]"
        @click="handleClick(notification)"
      >
        <!-- 图标区域 -->
        <div class="notification-icon">
          <div v-if="notification.type === 'success'" class="icon-wrapper success">
            <i class="el-icon-check"></i>
          </div>
          <div v-else-if="notification.type === 'error'" class="icon-wrapper error">
            <i class="el-icon-close"></i>
          </div>
          <div v-else-if="notification.type === 'warning'" class="icon-wrapper warning">
            <i class="el-icon-warning"></i>
          </div>
          <div v-else-if="notification.type === 'info'" class="icon-wrapper info">
            <i class="el-icon-info"></i>
          </div>
          <div v-else-if="notification.type === 'yoga'" class="icon-wrapper yoga">
            <div class="yoga-icon">
              <div class="lotus-mini">
                <div class="petal-mini" v-for="n in 6" :key="n"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="notification-content">
          <div class="notification-title" v-if="notification.title">
            {{ notification.title }}
          </div>
          <div class="notification-message">
            {{ notification.message }}
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div v-if="notification.action" class="notification-action">
          <el-button 
            size="small" 
            :type="notification.action.type || 'primary'"
            @click.stop="handleAction(notification)"
          >
            {{ notification.action.text }}
          </el-button>
        </div>
        
        <!-- 关闭按钮 -->
        <div class="notification-close" @click.stop="removeNotification(notification.id)">
          <i class="el-icon-close"></i>
        </div>
      </div>
    </transition-group>
  </teleport>
</template>

<script>
export default {
  name: 'YogaNotification',
  data() {
    return {
      notifications: []
    }
  },
  methods: {
    addNotification(notification) {
      const id = Date.now() + Math.random()
      const newNotification = {
        id,
        type: 'info',
        duration: 4000,
        ...notification
      }
      
      this.notifications.push(newNotification)
      
      // 自动移除
      if (newNotification.duration > 0) {
        setTimeout(() => {
          this.removeNotification(id)
        }, newNotification.duration)
      }
      
      return id
    },
    
    removeNotification(id) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    },
    
    clearAll() {
      this.notifications = []
    },
    
    handleClick(notification) {
      if (notification.onClick) {
        notification.onClick()
      }
      if (notification.closeOnClick !== false) {
        this.removeNotification(notification.id)
      }
    },
    
    handleAction(notification) {
      if (notification.action.onClick) {
        notification.action.onClick()
      }
      this.removeNotification(notification.id)
    }
  }
}
</script>

<style scoped>
.yoga-notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
}

.yoga-notification {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 20px;
  margin-bottom: 12px;
  min-width: 320px;
  max-width: 480px;
  background: #ffffff;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  pointer-events: auto;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.yoga-notification:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* 类型样式 */
.yoga-notification--success {
  border-left: 4px solid #67C23A;
}

.yoga-notification--error {
  border-left: 4px solid #F56C6C;
}

.yoga-notification--warning {
  border-left: 4px solid #E6A23C;
}

.yoga-notification--info {
  border-left: 4px solid #409EFF;
}

.yoga-notification--yoga {
  border-left: 4px solid #9C27B0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f3ff 100%);
}

/* 图标区域 */
.notification-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.icon-wrapper {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.icon-wrapper.success {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.icon-wrapper.error {
  background: linear-gradient(135deg, #F56C6C, #F78989);
}

.icon-wrapper.warning {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.icon-wrapper.info {
  background: linear-gradient(135deg, #409EFF, #79BBFF);
}

.icon-wrapper.yoga {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
  animation: yoga-glow 2s ease-in-out infinite alternate;
}

@keyframes yoga-glow {
  0% { box-shadow: 0 0 5px rgba(156, 39, 176, 0.5); }
  100% { box-shadow: 0 0 15px rgba(156, 39, 176, 0.8); }
}

/* 瑜伽图标 */
.yoga-icon {
  position: relative;
  width: 16px;
  height: 16px;
}

.lotus-mini {
  position: relative;
  width: 100%;
  height: 100%;
  animation: lotus-spin 4s linear infinite;
}

.petal-mini {
  position: absolute;
  width: 3px;
  height: 8px;
  background: white;
  border-radius: 50% 10px 50% 10px;
  top: 0;
  left: 50%;
  transform-origin: 50% 100%;
  transform: translateX(-50%);
}

.petal-mini:nth-child(1) { transform: translateX(-50%) rotate(0deg); }
.petal-mini:nth-child(2) { transform: translateX(-50%) rotate(60deg); }
.petal-mini:nth-child(3) { transform: translateX(-50%) rotate(120deg); }
.petal-mini:nth-child(4) { transform: translateX(-50%) rotate(180deg); }
.petal-mini:nth-child(5) { transform: translateX(-50%) rotate(240deg); }
.petal-mini:nth-child(6) { transform: translateX(-50%) rotate(300deg); }

@keyframes lotus-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 内容区域 */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  word-wrap: break-word;
}

/* 操作按钮 */
.notification-action {
  flex-shrink: 0;
  margin-top: 2px;
}

/* 关闭按钮 */
.notification-close {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  color: #C0C4CC;
  font-size: 12px;
  transition: all 0.2s ease;
}

.notification-close:hover {
  color: #606266;
  background: #F5F7FA;
}

/* 动画效果 */
.notification-enter-active {
  transition: all 0.3s ease;
}

.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .yoga-notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .yoga-notification {
    min-width: auto;
    max-width: none;
    margin-bottom: 8px;
  }
}

/* 暗色主题适配 */
.dark .yoga-notification {
  background: #2D2D2D;
  border-color: #4C4D4F;
  color: #E4E7ED;
}

.dark .notification-title {
  color: #E4E7ED;
}

.dark .notification-message {
  color: #C0C4CC;
}

.dark .notification-close:hover {
  background: #3A3A3A;
}
</style> 