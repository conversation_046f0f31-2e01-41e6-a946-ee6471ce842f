import { ElMessage, ElNotification } from 'element-plus'

// 瑜伽主题通知服务类
class YogaNotificationService {
  constructor() {
    this.notificationComponent = null
  }

  // 初始化通知组件
  init(app) {
    // 如果有自定义通知组件，可以在这里注册
    this.app = app
  }

  // 基础通知方法
  notify(options) {
    const defaultOptions = {
      duration: 4000,
      showClose: true,
      position: 'top-right'
    }

    return ElNotification({
      ...defaultOptions,
      ...options
    })
  }

  // 成功提示
  success(message, title = '操作成功', options = {}) {
    return this.notify({
      title,
      message,
      type: 'success',
      iconClass: 'yoga-success-icon',
      customClass: 'yoga-notification-success',
      ...options
    })
  }

  // 错误提示
  error(message, title = '操作失败', options = {}) {
    return this.notify({
      title,
      message,
      type: 'error',
      iconClass: 'yoga-error-icon',
      customClass: 'yoga-notification-error',
      duration: 6000,
      ...options
    })
  }

  // 警告提示
  warning(message, title = '警告', options = {}) {
    return this.notify({
      title,
      message,
      type: 'warning',
      iconClass: 'yoga-warning-icon',
      customClass: 'yoga-notification-warning',
      ...options
    })
  }

  // 信息提示
  info(message, title = '提示', options = {}) {
    return this.notify({
      title,
      message,
      type: 'info',
      iconClass: 'yoga-info-icon',
      customClass: 'yoga-notification-info',
      ...options
    })
  }

  // 瑜伽主题特殊提示
  yoga(message, title = '瑜伽提示', options = {}) {
    return this.notify({
      title,
      message,
      type: 'success',
      iconClass: 'yoga-lotus-icon',
      customClass: 'yoga-notification-yoga',
      duration: 5000,
      ...options
    })
  }

  // 预约成功提示
  bookingSuccess(courseName, options = {}) {
    return this.yoga(
      `您已成功预约"${courseName}"课程，请准时参加练习`,
      '预约成功',
      {
        duration: 6000,
        ...options
      }
    )
  }

  // 登录成功提示
  loginSuccess(username, options = {}) {
    const hour = new Date().getHours()
    let greeting = '早上好'
    if (hour >= 12 && hour < 18) greeting = '下午好'
    else if (hour >= 18) greeting = '晚上好'

    return this.yoga(
      `${greeting}，${username}！欢迎回到瑜伽管理系统`,
      '登录成功',
      {
        duration: 5000,
        ...options
      }
    )
  }

  // 网络错误提示
  networkError(options = {}) {
    return this.error(
      '网络连接失败，请检查您的网络连接后重试',
      '网络错误',
      {
        duration: 8000,
        ...options
      }
    )
  }

  // 简单消息提示（使用 ElMessage）
  message(message, type = 'info', options = {}) {
    const defaultOptions = {
      duration: 3000,
      showClose: true
    }

    return ElMessage({
      message,
      type,
      customClass: `yoga-message-${type}`,
      ...defaultOptions,
      ...options
    })
  }

  // 快捷消息方法
  msgSuccess(message, options = {}) {
    return this.message(message, 'success', options)
  }

  msgError(message, options = {}) {
    return this.message(message, 'error', options)
  }

  msgWarning(message, options = {}) {
    return this.message(message, 'warning', options)
  }

  msgInfo(message, options = {}) {
    return this.message(message, 'info', options)
  }

  // 操作成功通知（常用于CRUD操作）
  operationSuccess(operation, entity = '') {
    const messages = {
      'create': '创建成功',
      'update': '更新成功', 
      'delete': '删除成功',
      'save': '保存成功',
      'submit': '提交成功',
      'cancel': '取消成功',
      'enable': '启用成功',
      'disable': '禁用成功'
    }
    
    const message = entity 
      ? `${entity}${messages[operation] || '操作成功'}`
      : messages[operation] || '操作成功'
    
    return this.success(message)
  }

  // 操作失败通知
  operationError(operation, entity = '', error = '') {
    const messages = {
      'create': '创建失败',
      'update': '更新失败',
      'delete': '删除失败', 
      'save': '保存失败',
      'submit': '提交失败',
      'cancel': '取消失败',
      'enable': '启用失败',
      'disable': '禁用失败'
    }
    
    let message = entity
      ? `${entity}${messages[operation] || '操作失败'}`
      : messages[operation] || '操作失败'
    
    if (error) {
      message += `：${error}`
    }
    
    return this.error(message)
  }

  // 加载提示（配合loading使用）
  loading(message = '加载中...', options = {}) {
    return this.info(message, '请稍候', {
      duration: 0, // 不自动关闭
      showClose: false,
      ...options
    })
  }

  // 关闭所有通知
  closeAll() {
    ElNotification.closeAll()
  }
}

// 创建单例实例
const yogaNotification = new YogaNotificationService()

// 导出实例和类
export { YogaNotificationService }
export default yogaNotification 