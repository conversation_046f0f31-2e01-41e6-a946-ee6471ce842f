<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="日期" prop="date">
        <el-date-picker
          v-model="queryParams.date"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="课程名称" prop="courseName">
        <el-input
          v-model="queryParams.courseName"
          placeholder="请输入课程名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="教练" prop="instructor">
        <el-input
          v-model="queryParams.instructor"
          placeholder="请输入教练姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="课程状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
    </el-row>

    <!-- 课程表列表 -->
    <el-table v-loading="loading" :data="scheduleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="id" width="60" />
      <el-table-column label="课程名称" align="center" prop="courseName" :show-overflow-tooltip="true" />
      <el-table-column label="教练" align="center" prop="instructor" width="100" />
      <el-table-column label="日期" align="center" prop="date" width="120" />
      <el-table-column label="时间" align="center" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.startTime }} - {{ scope.row.endTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="容量" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.currentBookings }}/{{ scope.row.maxCapacity }}</span>
        </template>
      </el-table-column>
      <el-table-column label="地点" align="center" prop="location" width="100" />
      <el-table-column label="价格" align="center" prop="price" width="80">
        <template slot-scope="scope">
          <span>¥{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.status === 'available' ? 'success' : 
                   scope.row.status === 'full' ? 'warning' : 'danger'"
          >
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-show="total>0"
        :total="total"
        :page-size="queryParams.pageSize"
        :current-page="queryParams.pageNum"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 添加或修改课程表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="课程名称" prop="courseName">
              <el-select v-model="form.courseName" placeholder="请选择课程">
                <el-option
                  v-for="option in courseNameOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="教练" prop="instructorId">
              <el-select v-model="form.instructorId" placeholder="请选择教练" @change="handleInstructorChange">
                <el-option
                  v-for="option in instructorOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="日期" prop="date">
              <el-date-picker
                v-model="form.date"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大容量" prop="maxCapacity">
              <el-input-number v-model="form.maxCapacity" :min="1" :max="50" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                v-model="form.startTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择开始时间"
                style="width: 100%">
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                v-model="form.endTime"
                format="HH:mm"
                value-format="HH:mm"
                placeholder="选择结束时间"
                style="width: 100%">
              </el-time-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="地点" prop="location">
              <el-select v-model="form.location" placeholder="请选择地点">
                <el-option
                  v-for="option in locationOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="价格" prop="price">
              <el-input-number v-model="form.price" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="课程描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入课程描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSchedule, getSchedule, delSchedule, delSchedules, addSchedule, updateSchedule } from "@/api/system/schedule";
import { getInstructorOptions, getCourseNameOptions, getLocationOptions, getStatusOptions } from "@/api/system/options";
import yogaNotification from "@/utils/yogaNotification";

export default {
  name: "Schedule",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 课程表表格数据
      scheduleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        date: null,
        courseName: null,
        instructor: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        courseName: [
          { required: true, message: "课程名称不能为空", trigger: "change" }
        ],
        instructorId: [
          { required: true, message: "教练不能为空", trigger: "change" }
        ],
        date: [
          { required: true, message: "日期不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "blur" }
        ],
        maxCapacity: [
          { required: true, message: "最大容量不能为空", trigger: "blur" }
        ],
        location: [
          { required: true, message: "地点不能为空", trigger: "change" }
        ],
        price: [
          { required: true, message: "价格不能为空", trigger: "blur" }
        ]
      },
      // 选项数据
      instructorOptions: [],
      courseNameOptions: [],
      locationOptions: [],
      statusOptions: []
    };
  },
  created() {
    this.getList();
    this.loadOptions();
  },
  methods: {
    /** 查询课程表列表 */
    getList() {
      this.loading = true;
      const params = {
        page: this.queryParams.pageNum,
        limit: this.queryParams.pageSize,
        ...this.queryParams
      };
      listSchedule(params).then(response => {
        if (response.success) {
          this.scheduleList = response.data.schedules;
          this.total = response.data.pagination.totalItems;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 加载选项数据
    loadOptions() {
      this.instructorOptions = getInstructorOptions();
      this.courseNameOptions = getCourseNameOptions();
      this.locationOptions = getLocationOptions();
      this.statusOptions = getStatusOptions();
    },
    // 获取状态标签
    getStatusLabel(status) {
      const option = this.statusOptions.find(item => item.value === status);
      return option ? option.label : status;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        courseId: null,
        courseName: null,
        instructor: null,
        instructorId: null,
        date: null,
        startTime: null,
        endTime: null,
        maxCapacity: 15,
        location: null,
        description: null,
        price: null,
        status: "available"
      };
      this.resetForm("form");
    },
    // 重置表单
    resetForm(refName) {
      if (this.$refs[refName]) {
        this.$refs[refName].resetFields();
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加课程表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const scheduleId = row.id || this.ids
      getSchedule(scheduleId).then(response => {
        if (response.success) {
          this.form = response.data;
          this.open = true;
          this.title = "修改课程表";
        }
      });
    },
    // 处理教练选择变化
    handleInstructorChange(instructorId) {
      const instructor = this.instructorOptions.find(item => item.value === instructorId);
      if (instructor) {
        this.form.instructor = instructor.name;
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateSchedule(this.form).then(response => {
              if (response.success) {
                yogaNotification.operationSuccess('update', '课程表');
                this.open = false;
                this.getList();
              } else {
                yogaNotification.operationError('update', '课程表', response.message);
              }
            });
          } else {
            addSchedule(this.form).then(response => {
              if (response.success) {
                yogaNotification.operationSuccess('create', '课程表');
                this.open = false;
                this.getList();
              } else {
                yogaNotification.operationError('create', '课程表', response.message);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const scheduleIds = row.id || this.ids;
      this.$confirm('是否确认删除课程表编号为"' + scheduleIds + '"的数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (Array.isArray(scheduleIds)) {
          return delSchedules(scheduleIds);
        } else {
          return delSchedule(scheduleIds);
        }
      }).then(response => {
        if (response.success) {
          this.getList();
          yogaNotification.operationSuccess('delete', '课程表');
        } else {
          yogaNotification.operationError('delete', '课程表', response.message);
        }
      }).catch(() => {});
    },
    // 分页相关
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val;
      this.getList();
    }
  }
};
</script>

<style scoped>
.pagination-container {
  padding: 32px 16px;
}
.mb8 {
  margin-bottom: 8px;
}
</style> 