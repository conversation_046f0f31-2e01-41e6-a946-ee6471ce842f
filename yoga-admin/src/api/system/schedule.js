import request from '@/utils/request'

// 查询课程表列表
export function listSchedule(query) {
  return request({
    url: '/api/admin/schedules',
    method: 'get',
    params: query
  })
}

// 查询课程表详细
export function getSchedule(scheduleId) {
  return request({
    url: '/api/admin/schedules/' + scheduleId,
    method: 'get'
  })
}

// 新增课程表
export function addSchedule(data) {
  return request({
    url: '/api/admin/schedules',
    method: 'post',
    data: data
  })
}

// 修改课程表
export function updateSchedule(data) {
  return request({
    url: '/api/admin/schedules/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除课程表
export function delSchedule(scheduleId) {
  return request({
    url: '/api/admin/schedules/' + scheduleId,
    method: 'delete'
  })
}

// 批量删除课程表
export function delSchedules(scheduleIds) {
  return request({
    url: '/api/admin/schedules',
    method: 'delete',
    data: { ids: scheduleIds }
  })
}

// 课程表状态修改
export function changeScheduleStatus(scheduleId, status) {
  const data = {
    status
  }
  return request({
    url: '/api/admin/schedules/' + scheduleId,
    method: 'put',
    data: data
  })
}

// 获取教练选项
export function getInstructorOptions() {
  return [
    { value: 1, label: '李老师', name: '李老师' },
    { value: 2, label: '王老师', name: '王老师' },
    { value: 3, label: '张老师', name: '张老师' },
    { value: 4, label: '陈老师', name: '陈老师' },
    { value: 5, label: '刘老师', name: '刘老师' },
    { value: 6, label: '赵老师', name: '赵老师' }
  ]
}

// 获取课程类型选项
export function getCourseNameOptions() {
  return [
    { value: '晨间瑜伽唤醒', label: '晨间瑜伽唤醒' },
    { value: '流瑜伽进阶', label: '流瑜伽进阶' },
    { value: '阴瑜伽放松', label: '阴瑜伽放松' },
    { value: '哈他瑜伽基础', label: '哈他瑜伽基础' },
    { value: '阿斯汤加练习', label: '阿斯汤加练习' },
    { value: '热瑜伽体验', label: '热瑜伽体验' },
    { value: '空中瑜伽', label: '空中瑜伽' },
    { value: '办公室减压瑜伽', label: '办公室减压瑜伽' },
    { value: '力量瑜伽挑战', label: '力量瑜伽挑战' },
    { value: '孕妇瑜伽', label: '孕妇瑜伽' }
  ]
}

// 获取场地选项
export function getLocationOptions() {
  return [
    { value: '瑜伽馆A', label: '瑜伽馆A' },
    { value: '瑜伽馆B', label: '瑜伽馆B' },
    { value: '瑜伽馆C', label: '瑜伽馆C' },
    { value: '瑜伽馆D', label: '瑜伽馆D' },
    { value: '户外平台', label: '户外平台' },
    { value: '私教室1', label: '私教室1' },
    { value: '私教室2', label: '私教室2' }
  ]
}

// 获取状态选项
export function getStatusOptions() {
  return [
    { value: 'available', label: '可预约' },
    { value: 'full', label: '已满员' },
    { value: 'cancelled', label: '已取消' }
  ]
}

// 获取每日课程表（前端使用）
export function getDailySchedules(date) {
  return request({
    url: '/api/schedules/daily',
    method: 'get',
    params: { date }
  })
} 