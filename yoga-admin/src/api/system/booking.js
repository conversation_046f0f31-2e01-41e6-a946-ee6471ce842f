import request from '@/utils/request'

// 查询预约列表
export function listBooking(query) {
  return request({
    url: '/system/booking/list',
    method: 'get',
    params: query
  })
}

// 查询预约详细
export function getBooking(bookingId) {
  return request({
    url: '/system/booking/' + bookingId,
    method: 'get'
  })
}

// 新增预约
export function addBooking(data) {
  return request({
    url: '/system/booking',
    method: 'post',
    data: data
  })
}

// 修改预约
export function updateBooking(data) {
  return request({
    url: '/system/booking',
    method: 'put',
    data: data
  })
}

// 删除预约
export function delBooking(bookingId) {
  return request({
    url: '/system/booking/' + bookingId,
    method: 'delete'
  })
}

// 确认预约
export function confirmBooking(bookingId) {
  return request({
    url: '/system/booking/confirm/' + bookingId,
    method: 'put'
  })
}

// 取消预约
export function cancelBooking(bookingId) {
  return request({
    url: '/system/booking/cancel/' + bookingId,
    method: 'put'
  })
}

// 获取预约统计
export function getBookingStats() {
  return request({
    url: '/system/booking/statistics',
    method: 'get'
  })
}

// 预约状态修改
export function changeBookingStatus(bookingId, status) {
  const data = {
    status
  }
  return request({
    url: '/system/booking/' + bookingId + '/status',
    method: 'put',
    data: data
  })
}

// 获取预约统计信息
export function getBookingStatistics() {
  return request({
    url: '/system/booking/statistics',
    method: 'get'
  })
}

// 获取预约状态选项
export function getBookingStatusOptions() {
  return [
    { value: 'pending', label: '待确认', type: 'warning' },
    { value: 'confirmed', label: '已确认', type: 'success' },
    { value: 'cancelled', label: '已取消', type: 'danger' },
    { value: 'completed', label: '已完成', type: 'info' }
  ]
}

// 获取支付状态选项
export function getPaymentStatusOptions() {
  return [
    { value: 'unpaid', label: '未支付', type: 'warning' },
    { value: 'paid', label: '已支付', type: 'success' },
    { value: 'refunded', label: '已退款', type: 'info' }
  ]
} 