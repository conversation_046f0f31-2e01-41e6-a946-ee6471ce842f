import request from '@/utils/request'

// 查询课程列表
export function listCourse(query) {
  return request({
    url: '/system/course/list',
    method: 'get',
    params: query
  })
}

// 查询课程详细
export function getCourse(courseId) {
  return request({
    url: '/system/course/' + courseId,
    method: 'get'
  })
}

// 新增课程
export function addCourse(data) {
  return request({
    url: '/system/course',
    method: 'post',
    data: data
  })
}

// 修改课程
export function updateCourse(data) {
  return request({
    url: '/system/course',
    method: 'put',
    data: data
  })
}

// 删除课程
export function delCourse(courseId) {
  return request({
    url: '/system/course/' + courseId,
    method: 'delete'
  })
}

// 课程状态修改
export function changeCourseStatus(courseId, status) {
  const data = {
    courseId,
    status
  }
  return request({
    url: '/system/course/changeStatus',
    method: 'put',
    data: data
  })
}

// 获取课程类型选项
export function getCourseTypeOptions() {
  return [
    { value: '哈他瑜伽', label: '哈他瑜伽' },
    { value: '阿斯汤加', label: '阿斯汤加' },
    { value: '流瑜伽', label: '流瑜伽' },
    { value: '阴瑜伽', label: '阴瑜伽' },
    { value: '热瑜伽', label: '热瑜伽' },
    { value: '空中瑜伽', label: '空中瑜伽' }
  ]
}

// 获取难度等级选项
export function getDifficultyOptions() {
  return [
    { value: '初级', label: '初级' },
    { value: '中级', label: '中级' },
    { value: '高级', label: '高级' }
  ]
} 