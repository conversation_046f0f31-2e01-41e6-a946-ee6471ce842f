// 瑜伽主题通知样式

// 通知图标样式
.yoga-success-icon,
.yoga-error-icon,
.yoga-warning-icon,
.yoga-info-icon,
.yoga-lotus-icon {
  &::before {
    font-size: 20px;
  }
}

// 莲花图标
.yoga-lotus-icon {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    transform: translate(-50%, -50%);
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23fff" stroke-width="2"><path d="M12 2L8 7h8l-4-5z"/><path d="M12 22l4-5H8l4 5z"/><path d="M2 12l5-4v8l-5-4z"/><path d="M22 12l-5-4v8l5-4z"/><path d="M8 8l-3-3 3 3z"/><path d="M16 8l3-3-3 3z"/><path d="M8 16l-3 3 3-3z"/><path d="M16 16l3 3-3-3z"/></svg>') no-repeat center;
    background-size: contain;
    animation: lotus-rotate 4s linear infinite;
  }
}

@keyframes lotus-rotate {
  from { transform: translate(-50%, -50%) rotate(0deg); }
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

// 通知框样式
.yoga-notification-success {
  border-left: 4px solid #67C23A;
  
  .el-notification__icon {
    color: #67C23A;
  }
}

.yoga-notification-error {
  border-left: 4px solid #F56C6C;
  
  .el-notification__icon {
    color: #F56C6C;
  }
}

.yoga-notification-warning {
  border-left: 4px solid #E6A23C;
  
  .el-notification__icon {
    color: #E6A23C;
  }
}

.yoga-notification-info {
  border-left: 4px solid #409EFF;
  
  .el-notification__icon {
    color: #409EFF;
  }
}

.yoga-notification-yoga {
  border-left: 4px solid #9C27B0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f3ff 100%);
  
  .el-notification__icon {
    color: #9C27B0;
    animation: yoga-icon-glow 2s ease-in-out infinite alternate;
  }
  
  .el-notification__title {
    color: #9C27B0;
    font-weight: 600;
  }
}

@keyframes yoga-icon-glow {
  0% { text-shadow: 0 0 5px rgba(156, 39, 176, 0.3); }
  100% { text-shadow: 0 0 15px rgba(156, 39, 176, 0.6); }
}

// 消息提示样式
.yoga-message-success {
  background-color: #f0f9ff;
  border-color: #67C23A;
  color: #67C23A;
}

.yoga-message-error {
  background-color: #fef0f0;
  border-color: #F56C6C;
  color: #F56C6C;
}

.yoga-message-warning {
  background-color: #fdf6ec;
  border-color: #E6A23C;
  color: #E6A23C;
}

.yoga-message-info {
  background-color: #f4f4f5;
  border-color: #409EFF;
  color: #409EFF;
}

// 悬浮效果增强
.el-notification {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

// 瑜伽主题通知的特殊动画
.yoga-notification-yoga {
  .el-notification__content {
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      background: linear-gradient(45deg, transparent, rgba(156, 39, 176, 0.1), transparent);
      border-radius: inherit;
      z-index: -1;
      animation: yoga-aura 3s ease-in-out infinite;
    }
  }
}

@keyframes yoga-aura {
  0%, 100% { opacity: 0; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

// 响应式设计
@media (max-width: 768px) {
  .el-notification {
    width: calc(100vw - 40px);
    max-width: none;
    left: 20px !important;
    right: 20px !important;
    margin-left: 0 !important;
  }
}

// 暗色主题适配
.dark {
  .yoga-notification-yoga {
    background: linear-gradient(135deg, #2d2d2d 0%, #3a2f4a 100%);
    
    .el-notification__title {
      color: #BA68C8;
    }
    
    .el-notification__content {
      color: #E4E7ED;
    }
  }
  
  .yoga-message-success,
  .yoga-message-error,
  .yoga-message-warning,
  .yoga-message-info {
    background-color: #2d2d2d;
    color: #E4E7ED;
  }
} 