import { defineConfig, loadEnv } from 'vite'
import path from 'path'
import createVitePlugins from './vite/plugins'

const baseUrl = 'http://localhost:3000' // 后端接口

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_ENV } = env
  
  // 设置默认环境变量
  process.env.VITE_APP_TITLE = env.VITE_APP_TITLE || '瑜伽管理系统'
  process.env.VITE_APP_BASE_API = env.VITE_APP_BASE_API || '/api'
  
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // 打包配置
    build: {
      // https://vite.dev/config/build-options.html
      sourcemap: command === 'build' ? false : 'inline',
      outDir: 'dist',
      assetsDir: 'assets',
      chunkSizeWarningLimit: 2000,
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]'
        }
      }
    },
    // 定义全局常量
    define: {
      __APP_INFO__: JSON.stringify({
        pkg: { name: "ruoyi-ui", version: "3.8.8" },
        lastBuildTime: new Date().toISOString()
      }),
      // 在客户端代码中可用的环境变量
      'import.meta.env.VITE_APP_TITLE': JSON.stringify(process.env.VITE_APP_TITLE || '瑜伽管理系统'),
      'import.meta.env.VITE_APP_BASE_API': JSON.stringify(process.env.VITE_APP_BASE_API || '/api')
    },
    // vite 相关配置
    server: {
      host: '0.0.0.0',
      port: 3001,
      open: false,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        '/api/v1': {
          target: baseUrl,
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/api\/v1/, '')
        },
        // 只代理真正的API路径，避免与前端路由冲突
        '/api': {
          target: baseUrl,
          changeOrigin: true,
          rewrite: (p) => p.replace(/^\/api/, '')
        }
      }
    },
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove()
                }
              }
            }
          }
        ]
      }
    }
  }
})
