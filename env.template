# 服务器配置
NODE_ENV=development
PORT=3000

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/yoga-platform
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
REFRESH_TOKEN_SECRET=your-super-secret-refresh-token-key-change-in-production
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# 加密密钥（用于数据加密）
ENCRYPTION_KEY=your-32-character-encryption-key-here

# CORS配置
CORS_ORIGIN=http://localhost:8080

# 邮件配置（可选，用于邮箱验证和密码重置）
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM_NAME=瑜伽平台
EMAIL_FROM_ADDRESS=<EMAIL>

# 文件上传配置
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX=5
RATE_LIMIT_REGISTER_MAX=3

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/application.log

# 会话配置
SESSION_TIMEOUT=86400000

# API配置
API_VERSION=v1
API_PREFIX=/api 