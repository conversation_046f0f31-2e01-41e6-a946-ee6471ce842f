# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 上传文件
uploads/*
!uploads/.gitkeep

# 临时文件
.tmp/
.cache/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 测试覆盖率
coverage/
.nyc_output/

# 运行时
*.pid
*.seed

# 数据库
*.sqlite
*.db

# Redis dump
dump.rdb 