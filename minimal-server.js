const http = require('http');
const url = require('url');

// 简单的JSON响应函数
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
  });
  res.end(JSON.stringify(data));
}

// 处理请求体
function getRequestBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const data = body ? JSON.parse(body) : {};
      callback(null, data);
    } catch (err) {
      callback(err, null);
    }
  });
}

// 模拟数据库
const mockData = {
  users: [
    {
      userId: 1,
      userName: 'admin',
      nickName: '管理员',
      email: '<EMAIL>',
      phonenumber: '13888888888',
      sex: '1',
      avatar: '',
      status: '0',
      delFlag: '0',
      loginIp: '127.0.0.1',
      loginDate: '2024-01-01',
      createTime: '2024-01-01',
      remark: '管理员',
      deptId: 1,
      roleIds: [1]
    },
    {
      userId: 2,
      userName: 'user001',
      nickName: '张三',
      email: '<EMAIL>',
      phonenumber: '13999999999',
      sex: '0',
      avatar: '',
      status: '0',
      delFlag: '0',
      loginIp: '127.0.0.1',
      loginDate: '2024-01-02',
      createTime: '2024-01-02',
      remark: '普通用户',
      deptId: 2,
      roleIds: [2]
    }
  ],
  courses: [
    {
      courseId: 1,
      courseName: '初级瑜伽',
      courseType: '哈他瑜伽',
      difficulty: '初级',
      duration: 60,
      price: 88.00,
      maxCapacity: 15,
      description: '适合瑜伽初学者的基础课程',
      status: '1',
      createTime: '2024-01-01',
      updateTime: '2024-01-01'
    },
    {
      courseId: 2,
      courseName: '高级瑜伽',
      courseType: '阿斯汤加',
      difficulty: '高级',
      duration: 90,
      price: 128.00,
      maxCapacity: 10,
      description: '适合有一定基础的学员',
      status: '1',
      createTime: '2024-01-01',
      updateTime: '2024-01-01'
    }
  ],
  bookings: [
    {
      bookingId: 1,
      userId: 2,
      courseId: 1,
      courseName: '初级瑜伽',
      userName: '张三',
      bookingDate: '2024-01-10',
      scheduleTime: '09:00-10:00',
      status: 'confirmed',
      paymentStatus: 'paid',
      createTime: '2024-01-08',
      notes: '无特殊要求'
    },
    {
      bookingId: 2,
      userId: 2,
      courseId: 2,
      courseName: '高级瑜伽',
      userName: '张三',
      bookingDate: '2024-01-12',
      scheduleTime: '14:00-15:30',
      status: 'pending',
      paymentStatus: 'unpaid',
      createTime: '2024-01-10',
      notes: '第一次参加高级课程'
    }
  ]
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;
  const query = parsedUrl.query;

  console.log(`${method} ${path}`);

  // OPTIONS请求处理（CORS预检）
  if (method === 'OPTIONS') {
    sendJSON(res, { success: true });
    return;
  }

  // ============ 系统接口 ============

  // 健康检查
  if (method === 'GET' && path === '/health') {
    sendJSON(res, {
      success: true,
      message: 'API服务运行正常',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
    return;
  }

  // 登录接口
  if (method === 'POST' && path === '/login') {
    getRequestBody(req, (err, body) => {
      if (err) {
        sendJSON(res, { msg: '请求格式错误', code: 400 }, 400);
        return;
      }
      
      console.log('Login request:', body);
      sendJSON(res, {
        msg: '操作成功',
        code: 200,
        token: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjEyMzQ1In0.test-token'
      });
    });
    return;
  }

  // 获取用户信息接口
  if (method === 'GET' && path === '/getInfo') {
    console.log('GetInfo request');
    sendJSON(res, {
      msg: '操作成功',
      code: 200,
      user: {
        userId: 1,
        userName: 'admin',
        nickName: '管理员',
        email: '<EMAIL>',
        phonenumber: '',
        sex: '1',
        avatar: '',
        status: '0',
        delFlag: '0',
        loginIp: '127.0.0.1',
        loginDate: new Date().toISOString(),
        createTime: '2024-01-01',
        remark: '管理员',
        dept: {
          deptId: 1,
          deptName: '瑜伽平台管理部',
          leader: 'admin'
        }
      },
      roles: ['admin'],
      permissions: ['*:*:*']
    });
    return;
  }

  // 退出登录接口
  if (method === 'POST' && path === '/logout') {
    console.log('Logout request');
    sendJSON(res, {
      msg: '操作成功',
      code: 200
    });
    return;
  }

  // 验证码接口
  if (method === 'GET' && path === '/captchaImage') {
    console.log('CaptchaImage request');
    sendJSON(res, {
      msg: '操作成功',
      code: 200,
      captchaEnabled: false, // 禁用验证码
      uuid: 'test-uuid-123'
    });
    return;
  }

  // 获取路由菜单接口
  if (method === 'GET' && path === '/getRouters') {
    console.log('GetRouters request');
    sendJSON(res, {
      msg: '操作成功',
      code: 200,
      data: [
        {
          name: 'System',
          path: '/system',
          hidden: false,
          redirect: 'noRedirect',
          component: 'Layout',
          alwaysShow: true,
          meta: {
            title: '系统管理',
            icon: 'system',
            noCache: false,
            link: null
          },
          children: [
            {
              name: 'User',
              path: 'user',
              hidden: false,
              component: 'system/user/index',
              meta: {
                title: '用户管理',
                icon: 'user',
                noCache: false,
                link: null
              }
            },
            {
              name: 'Course',
              path: 'course',
              hidden: false,
              component: 'system/course/index',
              meta: {
                title: '课程管理',
                icon: 'education',
                noCache: false,
                link: null
              }
            },
            {
              name: 'Booking',
              path: 'booking',
              hidden: false,
              component: 'system/booking/index',
              meta: {
                title: '预约管理',
                icon: 'date',
                noCache: false,
                link: null
              }
            }
          ]
        },
        {
          name: 'Monitor',
          path: '/monitor',
          hidden: false,
          redirect: 'noRedirect',
          component: 'Layout',
          alwaysShow: true,
          meta: {
            title: '系统监控',
            icon: 'monitor',
            noCache: false,
            link: null
          },
          children: [
            {
              name: 'Druid',
              path: 'druid',
              hidden: false,
              component: 'monitor/druid/index',
              meta: {
                title: '数据监控',
                icon: 'druid',
                noCache: false,
                link: null
              }
            }
          ]
        }
      ]
    });
    return;
  }

  // ============ 用户管理接口 ============

  // 获取用户列表
  if (method === 'GET' && path === '/system/user/list') {
    const { pageNum = 1, pageSize = 10, userName, phonenumber, status } = query;
    
    let filteredUsers = [...mockData.users];
    
    // 搜索过滤
    if (userName) {
      filteredUsers = filteredUsers.filter(user => 
        user.userName.includes(userName) || user.nickName.includes(userName)
      );
    }
    if (phonenumber) {
      filteredUsers = filteredUsers.filter(user => 
        user.phonenumber && user.phonenumber.includes(phonenumber)
      );
    }
    if (status) {
      filteredUsers = filteredUsers.filter(user => user.status === status);
    }

    const total = filteredUsers.length;
    const start = (pageNum - 1) * pageSize;
    const end = start + parseInt(pageSize);
    const rows = filteredUsers.slice(start, end);

    sendJSON(res, {
      msg: '查询成功',
      code: 200,
      total: total,
      rows: rows
    });
    return;
  }

  // 获取用户详情
  if (method === 'GET' && path.match(/^\/system\/user\/(\d+)$/)) {
    const userId = parseInt(path.match(/^\/system\/user\/(\d+)$/)[1]);
    const user = mockData.users.find(u => u.userId === userId);
    
    if (user) {
      sendJSON(res, {
        msg: '查询成功',
        code: 200,
        data: user
      });
    } else {
      sendJSON(res, {
        msg: '用户不存在',
        code: 404
      }, 404);
    }
    return;
  }

  // 新增用户
  if (method === 'POST' && path === '/system/user') {
    getRequestBody(req, (err, body) => {
      if (err) {
        sendJSON(res, { msg: '请求格式错误', code: 400 }, 400);
        return;
      }

      const newUser = {
        userId: mockData.users.length + 1,
        ...body,
        status: '0',
        delFlag: '0',
        createTime: new Date().toISOString(),
        loginDate: new Date().toISOString()
      };

      mockData.users.push(newUser);

      sendJSON(res, {
        msg: '新增成功',
        code: 200
      });
    });
    return;
  }

  // 修改用户
  if (method === 'PUT' && path === '/system/user') {
    getRequestBody(req, (err, body) => {
      if (err) {
        sendJSON(res, { msg: '请求格式错误', code: 400 }, 400);
        return;
      }

      const userIndex = mockData.users.findIndex(u => u.userId === body.userId);
      if (userIndex !== -1) {
        mockData.users[userIndex] = { ...mockData.users[userIndex], ...body };
        sendJSON(res, {
          msg: '修改成功',
          code: 200
        });
      } else {
        sendJSON(res, {
          msg: '用户不存在',
          code: 404
        }, 404);
      }
    });
    return;
  }

  // 删除用户
  if (method === 'DELETE' && path.match(/^\/system\/user\/(.+)$/)) {
    const userIds = path.match(/^\/system\/user\/(.+)$/)[1].split(',').map(id => parseInt(id));
    
    userIds.forEach(userId => {
      const userIndex = mockData.users.findIndex(u => u.userId === userId);
      if (userIndex !== -1) {
        mockData.users[userIndex].delFlag = '2'; // 软删除
      }
    });

    sendJSON(res, {
      msg: '删除成功',
      code: 200
    });
    return;
  }

  // ============ 课程管理接口 ============

  // 获取课程列表
  if (method === 'GET' && path === '/system/course/list') {
    const { pageNum = 1, pageSize = 10, courseName, status } = query;
    
    let filteredCourses = [...mockData.courses];
    
    // 搜索过滤
    if (courseName) {
      filteredCourses = filteredCourses.filter(course => 
        course.courseName.includes(courseName)
      );
    }
    if (status) {
      filteredCourses = filteredCourses.filter(course => course.status === status);
    }

    const total = filteredCourses.length;
    const start = (pageNum - 1) * pageSize;
    const end = start + parseInt(pageSize);
    const rows = filteredCourses.slice(start, end);

    sendJSON(res, {
      msg: '查询成功',
      code: 200,
      total: total,
      rows: rows
    });
    return;
  }

  // 新增课程
  if (method === 'POST' && path === '/system/course') {
    getRequestBody(req, (err, body) => {
      if (err) {
        sendJSON(res, { msg: '请求格式错误', code: 400 }, 400);
        return;
      }

      const newCourse = {
        courseId: mockData.courses.length + 1,
        ...body,
        status: '1',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      };

      mockData.courses.push(newCourse);

      sendJSON(res, {
        msg: '新增成功',
        code: 200
      });
    });
    return;
  }

  // 修改课程
  if (method === 'PUT' && path === '/system/course') {
    getRequestBody(req, (err, body) => {
      if (err) {
        sendJSON(res, { msg: '请求格式错误', code: 400 }, 400);
        return;
      }

      const courseIndex = mockData.courses.findIndex(c => c.courseId === body.courseId);
      if (courseIndex !== -1) {
        mockData.courses[courseIndex] = { 
          ...mockData.courses[courseIndex], 
          ...body,
          updateTime: new Date().toISOString()
        };
        sendJSON(res, {
          msg: '修改成功',
          code: 200
        });
      } else {
        sendJSON(res, {
          msg: '课程不存在',
          code: 404
        }, 404);
      }
    });
    return;
  }

  // 删除课程
  if (method === 'DELETE' && path.match(/^\/system\/course\/(.+)$/)) {
    const courseIds = path.match(/^\/system\/course\/(.+)$/)[1].split(',').map(id => parseInt(id));
    
    courseIds.forEach(courseId => {
      const courseIndex = mockData.courses.findIndex(c => c.courseId === courseId);
      if (courseIndex !== -1) {
        mockData.courses.splice(courseIndex, 1); // 物理删除
      }
    });

    sendJSON(res, {
      msg: '删除成功',
      code: 200
    });
    return;
  }

  // ============ 预约管理接口 ============

  // 获取预约列表
  if (method === 'GET' && path === '/system/booking/list') {
    const { pageNum = 1, pageSize = 10, userName, courseName, status } = query;
    
    let filteredBookings = [...mockData.bookings];
    
    // 搜索过滤
    if (userName) {
      filteredBookings = filteredBookings.filter(booking => 
        booking.userName.includes(userName)
      );
    }
    if (courseName) {
      filteredBookings = filteredBookings.filter(booking => 
        booking.courseName.includes(courseName)
      );
    }
    if (status) {
      filteredBookings = filteredBookings.filter(booking => booking.status === status);
    }

    const total = filteredBookings.length;
    const start = (pageNum - 1) * pageSize;
    const end = start + parseInt(pageSize);
    const rows = filteredBookings.slice(start, end);

    sendJSON(res, {
      msg: '查询成功',
      code: 200,
      total: total,
      rows: rows
    });
    return;
  }

  // 获取预约详情
  if (method === 'GET' && path.match(/^\/system\/booking\/(\d+)$/)) {
    const bookingId = parseInt(path.match(/^\/system\/booking\/(\d+)$/)[1]);
    const booking = mockData.bookings.find(b => b.bookingId === bookingId);
    
    if (booking) {
      sendJSON(res, {
        msg: '查询成功',
        code: 200,
        data: booking
      });
    } else {
      sendJSON(res, {
        msg: '预约不存在',
        code: 404
      }, 404);
    }
    return;
  }

  // 更新预约状态
  if (method === 'PUT' && path.match(/^\/system\/booking\/(\d+)\/status$/)) {
    const bookingId = parseInt(path.match(/^\/system\/booking\/(\d+)\/status$/)[1]);
    
    getRequestBody(req, (err, body) => {
      if (err) {
        sendJSON(res, { msg: '请求格式错误', code: 400 }, 400);
        return;
      }

      const bookingIndex = mockData.bookings.findIndex(b => b.bookingId === bookingId);
      if (bookingIndex !== -1) {
        mockData.bookings[bookingIndex].status = body.status;
        sendJSON(res, {
          msg: '状态更新成功',
          code: 200
        });
      } else {
        sendJSON(res, {
          msg: '预约不存在',
          code: 404
        }, 404);
      }
    });
    return;
  }

  // 预约统计
  if (method === 'GET' && path === '/system/booking/statistics') {
    const totalBookings = mockData.bookings.length;
    const confirmedBookings = mockData.bookings.filter(b => b.status === 'confirmed').length;
    const pendingBookings = mockData.bookings.filter(b => b.status === 'pending').length;
    const cancelledBookings = mockData.bookings.filter(b => b.status === 'cancelled').length;

    sendJSON(res, {
      msg: '查询成功',
      code: 200,
      data: {
        total: totalBookings,
        confirmed: confirmedBookings,
        pending: pendingBookings,
        cancelled: cancelledBookings,
        totalUsers: mockData.users.length,
        totalCourses: mockData.courses.length
      }
    });
    return;
  }

  // 404处理
  console.log('404 - Route not found:', method, path);
  sendJSON(res, {
    msg: '路由不存在',
    code: 404
  }, 404);
});

// 启动服务器
const PORT = 8080;
const HOST = 'localhost';

server.listen(PORT, HOST, () => {
  console.log(`🚀 瑜伽平台后端服务器启动成功`);
  console.log(`📍 地址: http://${HOST}:${PORT}`);
  console.log(`📚 健康检查: http://${HOST}:${PORT}/health`);
  console.log(`🌍 环境: development`);
  console.log(`\n📋 可用接口:`);
  console.log(`   登录: POST /login`);
  console.log(`   用户信息: GET /getInfo`);
  console.log(`   路由菜单: GET /getRouters`);
  console.log(`   用户管理: GET /system/user/list`);
  console.log(`   课程管理: GET /system/course/list`);
  console.log(`   预约管理: GET /system/booking/list`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
}); 