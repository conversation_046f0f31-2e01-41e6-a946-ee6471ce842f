# 瑜伽学习平台 - 快速开始指南

## 🎯 项目概述

这是一个基于 Node.js + TypeScript 的瑜伽学习平台后端API，提供完整的用户认证和管理功能。

## ✅ 已完成的功能

### 🔐 认证系统
- ✅ 用户注册（邮箱/用户名/密码）
- ✅ 用户登录（支持邮箱或用户名）
- ✅ JWT访问令牌和刷新令牌
- ✅ 密码加密（bcrypt）
- ✅ 邮箱验证（token机制）
- ✅ 密码重置功能
- ✅ 用户信息管理
- ✅ 账户安全（登录限制、账户锁定）

### 🛡️ 安全特性
- ✅ API限流（防止暴力攻击）
- ✅ 输入验证（Joi schema）
- ✅ 错误处理中间件
- ✅ 请求日志记录
- ✅ CORS配置
- ✅ 安全头（Helmet）

### 🗄️ 数据库
- ✅ MongoDB集成（Mongoose）
- ✅ Redis缓存（可选）
- ✅ 用户模型（完整的瑜伽相关字段）
- ✅ 数据验证和索引

### 🏗️ 系统架构
- ✅ 模块化架构（Controllers, Services, Models, Middleware）
- ✅ TypeScript类型安全
- ✅ 环境配置管理
- ✅ 结构化日志
- ✅ 单元测试和集成测试

## 🚀 快速启动

### 1. 环境要求
- Node.js 18+
- MongoDB 4.4+
- Redis 6+ (可选)

### 2. 安装依赖
\`\`\`bash
npm install
\`\`\`

### 3. 环境配置
复制 `env.template` 到 `.env` 并填写必要的配置：

\`\`\`bash
cp env.template .env
\`\`\`

必需的环境变量：
- `MONGODB_URI`: MongoDB连接字符串
- `JWT_SECRET`: JWT密钥
- `REFRESH_TOKEN_SECRET`: 刷新令牌密钥

### 4. 启动开发服务器
\`\`\`bash
# 开发模式（热重载）
npm run dev

# 或构建并启动
npm run build
npm start
\`\`\`

### 5. 运行测试
\`\`\`bash
npm test
\`\`\`

## 📚 API文档

### 认证端点

| 方法 | 端点 | 描述 | 认证 |
|------|------|------|------|
| POST | `/api/v1/auth/register` | 用户注册 | ❌ |
| POST | `/api/v1/auth/login` | 用户登录 | ❌ |
| POST | `/api/v1/auth/refresh-token` | 刷新访问令牌 | ❌ |
| GET | `/api/v1/auth/verify-email/:token` | 验证邮箱 | ❌ |
| POST | `/api/v1/auth/forgot-password` | 发送重置密码邮件 | ❌ |
| POST | `/api/v1/auth/reset-password/:token` | 重置密码 | ❌ |
| POST | `/api/v1/auth/logout` | 退出登录 | ✅ |
| POST | `/api/v1/auth/change-password` | 修改密码 | ✅ |
| GET | `/api/v1/auth/profile` | 获取用户信息 | ✅ |
| PUT | `/api/v1/auth/profile` | 更新用户信息 | ✅ |
| DELETE | `/api/v1/auth/account` | 删除账户 | ✅ |

### 管理员端点

| 方法 | 端点 | 描述 | 权限 |
|------|------|------|------|
| GET | `/api/v1/auth/admin/stats` | 用户统计信息 | Admin |

### 系统端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/health` | 健康检查 |
| GET | `/api/v1` | API信息 |

## 🧪 测试API

### 注册新用户
\`\`\`bash
curl -X POST http://localhost:3000/api/v1/auth/register \\
  -H "Content-Type: application/json" \\
  -d '{
    "email": "<EMAIL>",
    "password": "Test123!@#",
    "username": "testuser",
    "displayName": "Test User"
  }'
\`\`\`

### 用户登录
\`\`\`bash
curl -X POST http://localhost:3000/api/v1/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{
    "emailOrUsername": "<EMAIL>",
    "password": "Test123!@#"
  }'
\`\`\`

### 获取用户信息
\`\`\`bash
curl -X GET http://localhost:3000/api/v1/auth/profile \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
\`\`\`

## 📁 项目结构

\`\`\`
src/
├── config/           # 配置文件
├── controllers/      # 控制器
├── middleware/       # 中间件
├── models/          # 数据模型
├── routes/          # 路由定义
├── services/        # 业务逻辑服务
├── utils/           # 工具函数
└── index.ts         # 应用入口

tests/
├── unit/            # 单元测试
└── integration/     # 集成测试
\`\`\`

## 🔜 下一步开发计划

1. **课程管理模块**
   - 课程CRUD操作
   - 课程分类和标签
   - 课程难度等级

2. **预约系统**
   - 课程时间安排
   - 用户预约管理
   - 预约状态跟踪

3. **支付系统**
   - 支付宝/微信支付集成
   - 订单管理
   - 会员系统

4. **管理后台**
   - 用户管理
   - 课程管理
   - 数据统计

5. **通知系统**
   - 邮件通知
   - 短信提醒
   - 推送通知

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

此项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。 