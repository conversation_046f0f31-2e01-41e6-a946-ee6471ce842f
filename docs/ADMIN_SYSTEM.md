# 瑜伽平台管理后台系统

## 📋 项目概述

基于若依(RuoYi-Vue3)框架搭建的瑜伽平台管理后台系统，用于管理用户、课程、教练员、预约等业务功能。

## 🏗️ 架构设计

### 技术栈
- **前端**: Vue 3 + Element Plus + Vite
- **后端**: Node.js + TypeScript + Express + MongoDB (复用现有API)
- **框架**: 若依(RuoYi-Vue3) 前端管理框架

### 项目结构
```
demo/
├── hhhh/                    # 瑜伽平台Node.js后端
│   ├── src/
│   ├── docs/
│   └── package.json
└── yoga-admin/              # 若依管理后台前端
    ├── src/
    ├── vite.config.js
    └── package.json
```

## 🔧 配置修改

### 1. 环境变量配置
**文件**: `.env.development`
```env
# 页面标题
VITE_APP_TITLE = 瑜伽平台管理系统

# 开发环境配置
VITE_APP_ENV = 'development'

# 瑜伽平台管理系统/开发环境 - 通过代理访问Node.js后端
VITE_APP_BASE_API = '/api/v1'
```

### 2. Vite代理配置
**文件**: `vite.config.js`
```javascript
const baseUrl = 'http://localhost:3000' // 瑜伽平台Node.js后端接口

server: {
  port: 80,
  host: true,
  open: true,
  proxy: {
    '/api/v1': {
      target: baseUrl,
      changeOrigin: true,
      rewrite: (p) => p.replace(/^\/api\/v1/, '/api/v1')
    }
  }
}
```

### 3. API适配层

#### 登录API适配 (`src/api/login.js`)
```javascript
// 登录方法 - 适配瑜伽平台Node.js后端
export function login(emailOrUsername, password) {
  const data = { emailOrUsername, password }
  return request({
    url: '/auth/login',
    headers: { isToken: false, repeatSubmit: false },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/auth/profile',
    method: 'get'
  })
}
```

#### 业务API接口
- **课程管理**: `src/api/yoga/courses.js`
- **预约管理**: `src/api/yoga/bookings.js`
- **时间段管理**: `src/api/yoga/schedules.js`

### 4. 请求拦截器适配 (`src/utils/request.js`)
- 适配Node.js后端的响应格式
- 统一错误处理机制
- Bearer Token认证方式
- 响应数据格式转换

## 🚀 启动方式

### 后端服务 (端口: 3000)
```bash
cd hhhh
npm run dev
```

### 前端管理后台 (端口: 80)
```bash
cd yoga-admin
npm run dev
```

## 📱 功能模块

### 已集成模块
1. **用户认证**
   - 登录/登出
   - Token管理
   - 权限验证

2. **API适配**
   - 瑜伽平台后端API对接
   - 数据格式转换
   - 错误处理统一

### 待开发模块
1. **系统管理**
   - 用户管理界面
   - 角色权限管理
   - 菜单配置

2. **业务管理**
   - 课程管理界面
   - 教练员管理界面
   - 预约管理界面
   - 时间段管理界面

3. **数据统计**
   - 用户统计dashboard
   - 课程预约统计
   - 收入分析报表

## 🔗 API对接状态

| 模块 | 后端API | 状态 | 前端页面 |
|------|---------|------|----------|
| 认证 | /auth/* | ✅ 已对接 | 🟡 待开发 |
| 用户 | /users/* | ✅ 可用 | 🟡 待开发 |
| 课程 | /courses/* | ✅ 可用 | 🟡 待开发 |
| 教练员 | /instructors/* | ✅ 可用 | 🟡 待开发 |
| 预约 | /bookings/* | ✅ 可用 | 🟡 待开发 |
| 时间段 | /schedules/* | ✅ 可用 | 🟡 待开发 |

## 🎯 访问地址

- **管理后台**: http://localhost (端口80)
- **后端API**: http://localhost:3000/api/v1
- **API文档**: 通过后端根路径查看端点信息

## 📝 下一步开发计划

1. **自定义登录页面** - 适配瑜伽平台业务
2. **创建业务管理页面** - 基于若依组件开发CRUD界面
3. **数据统计dashboard** - 展示业务关键指标
4. **权限管理** - 区分管理员、教练员、普通用户
5. **系统监控** - 服务器状态、API监控

## 🔧 开发注意事项

1. **数据格式**: 后端返回`{success, message, data}`，已在request.js中适配
2. **认证方式**: 使用Bearer Token，与若依原生JWT格式兼容
3. **权限控制**: 基于后端用户角色(admin, instructor, user)
4. **错误处理**: 统一在request拦截器中处理
5. **代理配置**: 开发环境使用Vite代理解决跨域问题

## 📊 当前状态

✅ **已完成**:
- 若依框架部署
- 后端API对接配置
- 基础认证流程适配
- 开发环境启动

🟡 **进行中**:
- 业务页面开发
- 菜单配置定制

❌ **待开始**:
- 生产环境部署
- 性能优化
- 安全加固 