# 瑜伽平台简化版后台管理系统 - 任务规划

## 🎯 项目目标

开发一个专注于核心业务功能的后台管理系统，包含：
- 管理员登录 ✅
- 用户管理
- 会员管理  
- 课程管理（添加课程）
- 预约管理（预约课程和预约信息）

## 📋 接口开发状态

### ✅ 已完成接口 (10个)

| 功能模块 | 接口 | 方法 | 路径 | 状态 |
|----------|------|------|------|------|
| **系统功能** | | | | |
| 管理员登录 | POST | `/login` | ✅ 完成 |
| 获取用户信息 | GET | `/getInfo` | ✅ 完成 |
| 退出登录 | POST | `/logout` | ✅ 完成 |
| 验证码接口 | GET | `/captchaImage` | ✅ 完成 |
| **用户管理** | | | | |
| 用户列表 | GET | `/system/user/list` | ✅ 完成 |
| 用户详情 | GET | `/system/user/:id` | ✅ 完成 |
| 新增用户 | POST | `/system/user` | ✅ 完成 |
| 修改用户 | PUT | `/system/user` | ✅ 完成 |
| 删除用户 | DELETE | `/system/user/:ids` | ✅ 完成 |
| **课程管理** | | | | |
| 课程列表 | GET | `/system/course/list` | ✅ 完成 |
| 新增课程 | POST | `/system/course` | ✅ 完成 |
| 修改课程 | PUT | `/system/course` | ✅ 完成 |
| 删除课程 | DELETE | `/system/course/:ids` | ✅ 完成 |
| **预约管理** | | | | |
| 预约列表 | GET | `/system/booking/list` | ✅ 完成 |
| 预约详情 | GET | `/system/booking/:id` | ✅ 完成 |
| 更新预约状态 | PUT | `/system/booking/:id/status` | ✅ 完成 |
| 预约统计 | GET | `/system/booking/statistics` | ✅ 完成 |

## 🚧 待开发任务

### 阶段一：前端页面开发 (1-2周)

#### 1. 用户管理页面
**优先级：🔥 高**
- 📄 用户列表页面
  - 搜索功能（用户名、手机号）
  - 分页展示
  - 状态筛选
  - 批量操作

- 📋 用户详情/编辑表单
  - 用户基本信息
  - 状态管理
  - 角色分配

**技术要点**:
- 使用Element Plus表格组件
- 分页器配置
- 表单验证
- 状态管理

#### 2. 课程管理页面  
**优先级：🔥 高**
- 📄 课程列表页面
  - 课程搜索
  - 状态筛选
  - 价格排序

- 📋 添加/编辑课程表单
  - 课程基本信息
  - 价格设置
  - 容量限制
  - 课程描述

**技术要点**:
- 富文本编辑器（课程描述）
- 图片上传（课程封面）
- 表单验证
- 价格格式化

#### 3. 预约管理页面
**优先级：🔥 高**  
- 📄 预约信息列表
  - 用户搜索
  - 课程筛选
  - 状态管理
  - 时间筛选

- 📊 预约统计Dashboard
  - 预约数量统计
  - 状态分布图表
  - 用户/课程统计

**技术要点**:
- 日期选择器
- 状态标签
- 统计图表（ECharts）
- 导出功能

#### 4. 会员管理功能扩展
**优先级：🟡 中**
- 会员信息展示
- 会员充值记录
- 会员等级管理
- 消费统计

### 阶段二：功能优化 (1周)

#### 5. 系统优化
**优先级：🟢 低**
- 操作日志记录
- 数据导出功能
- 批量操作优化
- 响应速度优化

## 📊 数据模型

### 用户 (User)
```javascript
{
  userId: Number,           // 用户ID
  userName: String,         // 用户名
  nickName: String,         // 昵称
  email: String,            // 邮箱
  phonenumber: String,      // 手机号
  sex: String,              // 性别 (0女 1男)
  avatar: String,           // 头像
  status: String,           // 状态 (0正常 1停用)
  delFlag: String,          // 删除标志 (0存在 2删除)
  loginIp: String,          // 最后登录IP
  loginDate: String,        // 最后登录时间
  createTime: String,       // 创建时间
  remark: String            // 备注
}
```

### 课程 (Course)
```javascript
{
  courseId: Number,         // 课程ID
  courseName: String,       // 课程名称
  courseType: String,       // 课程类型
  difficulty: String,       // 难度等级
  duration: Number,         // 课程时长(分钟)
  price: Number,            // 课程价格
  maxCapacity: Number,      // 最大容量
  description: String,      // 课程描述
  status: String,           // 状态 (0停用 1启用)
  createTime: String,       // 创建时间
  updateTime: String        // 更新时间
}
```

### 预约 (Booking)
```javascript
{
  bookingId: Number,        // 预约ID
  userId: Number,           // 用户ID
  courseId: Number,         // 课程ID
  courseName: String,       // 课程名称
  userName: String,         // 用户名称
  bookingDate: String,      // 预约日期
  scheduleTime: String,     // 时间段
  status: String,           // 状态 (pending/confirmed/cancelled/completed)
  paymentStatus: String,    // 支付状态 (unpaid/paid/refunded)
  createTime: String,       // 创建时间
  notes: String             // 备注
}
```

## 🛠️ 技术实现方案

### 前端技术栈
- **框架**: Vue 3 + Composition API
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **构建工具**: Vite

### 页面文件结构
```
yoga-admin/src/
├── views/
│   ├── system/
│   │   ├── user/
│   │   │   ├── index.vue          # 用户列表
│   │   │   └── form.vue           # 用户表单
│   │   ├── course/
│   │   │   ├── index.vue          # 课程列表
│   │   │   └── form.vue           # 课程表单
│   │   └── booking/
│   │       ├── index.vue          # 预约列表
│   │       └── statistics.vue     # 预约统计
├── api/
│   ├── system/
│   │   ├── user.js                # 用户API
│   │   ├── course.js              # 课程API
│   │   └── booking.js             # 预约API
└── components/
    ├── UserForm/                  # 用户表单组件
    ├── CourseForm/                # 课程表单组件
    └── BookingChart/              # 预约统计图表
```

## 🎯 开发时间预估

| 阶段 | 任务 | 预估时间 | 负责人 |
|------|------|----------|--------|
| 1 | 用户管理页面 | 3天 | 前端开发 |
| 2 | 课程管理页面 | 3天 | 前端开发 |
| 3 | 预约管理页面 | 4天 | 前端开发 |
| 4 | 会员管理扩展 | 2天 | 后端+前端 |
| 5 | 系统优化 | 2天 | 全栈优化 |

**总计**: 约 2-3 周完成

## 📋 验收标准

### 功能验收
- [x] 管理员可以正常登录系统
- [ ] 可以查看、添加、编辑、删除用户
- [ ] 可以查看、添加、编辑、删除课程
- [ ] 可以查看、管理预约信息
- [ ] 可以查看预约统计数据
- [ ] 支持搜索和筛选功能

### 性能验收
- [ ] 页面加载时间 < 2秒
- [ ] 列表查询响应时间 < 500ms
- [ ] 支持分页，每页显示10-50条记录

### 用户体验验收
- [ ] 界面美观，符合现代管理后台设计规范
- [ ] 操作流程简单直观
- [ ] 错误提示清晰明确
- [ ] 支持移动端适配

## 🚀 部署方案

### 开发环境
- **前端**: http://localhost:80
- **后端**: http://localhost:8080
- **代理**: 前端`/dev-api/*` → 后端

### 生产环境计划
- **前端**: Nginx静态文件托管
- **后端**: PM2进程管理
- **域名**: yoga-admin.example.com
- **HTTPS**: Let's Encrypt证书

## 📞 联系信息

- **项目负责人**: 待定
- **技术负责人**: 待定
- **UI设计**: 基于若依Vue3框架
- **测试负责人**: 待定

---

**项目状态**: 🟡 接口开发完成，前端页面开发中
**最后更新**: 2025-01-02
**版本**: v1.0.0 