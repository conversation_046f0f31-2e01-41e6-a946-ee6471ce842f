# 瑜伽平台预约系统使用指南

## 概述

预约系统是瑜伽学习平台的核心功能之一，允许用户预约课程时间段、教练员管理课程安排。系统包含两个主要组件：

- **课程时间段管理** (`/api/v1/schedules`)
- **预约管理** (`/api/v1/bookings`)

## 功能特性

### 课程时间段管理
- ✅ 创建和管理课程时间段
- ✅ 支持循环课程（按周重复）
- ✅ 容量控制和预约计数
- ✅ 状态管理（scheduled, in_progress, completed, cancelled）
- ✅ 教练员权限验证

### 预约管理
- ✅ 用户预约创建和取消
- ✅ 预约状态管理（pending, confirmed, cancelled, completed, no_show）
- ✅ 时间冲突检测
- ✅ 预约限制（每用户最多10个同时预约）
- ✅ 课程评价系统
- ✅ 出席管理
- ✅ 教练员确认功能

## API 端点

### 课程时间段 API

#### 公开端点（无需认证）
```http
GET /api/v1/schedules/available     # 获取可用时间段
GET /api/v1/schedules/list          # 获取时间段列表（分页）
GET /api/v1/schedules/:id           # 获取单个时间段详情
```

#### 保护端点（需要认证）
```http
POST /api/v1/schedules              # 创建时间段（教练员/管理员）
PUT /api/v1/schedules/:id           # 更新时间段（教练员/管理员）
DELETE /api/v1/schedules/:id        # 删除时间段（教练员/管理员）
GET /api/v1/schedules/instructor/:id # 获取教练员时间段
GET /api/v1/schedules/stats/overview # 获取统计信息
```

### 预约 API

#### 用户预约
```http
POST /api/v1/bookings               # 创建预约
GET /api/v1/bookings                # 获取预约列表
GET /api/v1/bookings/:id            # 获取预约详情
PUT /api/v1/bookings/:id            # 更新预约
PATCH /api/v1/bookings/:id/cancel   # 取消预约
POST /api/v1/bookings/:id/rating    # 添加评价
```

#### 教练员功能
```http
PATCH /api/v1/bookings/:id/confirm  # 确认预约
PATCH /api/v1/bookings/:id/attendance # 标记出席
GET /api/v1/bookings/instructor     # 获取教练员预约
```

#### 管理功能
```http
GET /api/v1/bookings/stats/overview # 获取统计信息
PATCH /api/v1/bookings/batch/status # 批量更新状态（管理员）
```

## 数据模型

### CourseSchedule（课程时间段）
```javascript
{
  courseId: ObjectId,           // 课程ID
  instructorId: ObjectId,       // 教练员ID
  startTime: Date,              // 开始时间
  endTime: Date,                // 结束时间
  maxCapacity: Number,          // 最大容量
  currentBookings: Number,      // 当前预约数
  location: String,             // 地点
  status: String,               // 状态
  isRecurring: Boolean,         // 是否循环
  description: String           // 描述
}
```

### Booking（预约）
```javascript
{
  userId: ObjectId,             // 用户ID
  scheduleId: ObjectId,         // 时间段ID
  courseId: ObjectId,           // 课程ID
  instructorId: ObjectId,       // 教练员ID
  status: String,               // 预约状态
  notes: String,                // 备注
  specialRequests: String,      // 特殊要求
  rating: {                     // 评价
    score: Number,
    comment: String,
    ratedAt: Date
  },
  paymentStatus: String,        // 支付状态
  attendanceMarked: Boolean     // 是否标记出席
}
```

## 业务规则

### 预约限制
1. 每个用户最多同时有10个有效预约
2. 不能预约时间冲突的课程
3. 只能预约未来的时间段
4. 课程容量限制

### 权限控制
- **用户**: 可以创建、查看、取消自己的预约
- **教练员**: 可以管理自己的课程时间段和预约
- **管理员**: 拥有所有权限

### 状态流转
#### 时间段状态
```
scheduled → in_progress → completed
    ↓
cancelled
```

#### 预约状态
```
pending → confirmed → completed
   ↓          ↓         ↓
cancelled   no_show   cancelled
```

## 使用示例

### 1. 创建课程时间段
```bash
curl -X POST http://localhost:3000/api/v1/schedules \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "courseId": "507f1f77bcf86cd799439011",
    "instructorId": "507f1f77bcf86cd799439012",
    "startTime": "2025-07-03T10:00:00Z",
    "endTime": "2025-07-03T11:00:00Z",
    "maxCapacity": 20,
    "location": "瑜伽教室A"
  }'
```

### 2. 创建预约
```bash
curl -X POST http://localhost:3000/api/v1/bookings \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "scheduleId": "507f1f77bcf86cd799439013",
    "notes": "初学者，请多关照"
  }'
```

### 3. 获取可用时间段
```bash
curl http://localhost:3000/api/v1/schedules/available?startDate=2025-07-03
```

## 测试状态

✅ **24个测试全部通过**
- 5个预约系统测试
- 19个原有功能测试

当前测试覆盖：
- API端点可访问性
- 认证和权限验证
- 数据验证
- 错误处理

## 后续开发

预约系统的基础架构已完成，可进一步扩展：

1. **支付集成**: 在线支付和退款
2. **通知系统**: 预约提醒、状态变更通知
3. **高级功能**: 候补列表、自动确认
4. **移动端支持**: API优化和推送通知
5. **数据分析**: 预约趋势、用户行为分析

---

**文档版本**: v1.0  
**最后更新**: 2025-07-02  
**API版本**: v1 