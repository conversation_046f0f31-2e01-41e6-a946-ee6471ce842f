# 瑜伽学习平台 API 接口总结

## 接口概览

总计：**33个API接口**

| 模块 | 接口数量 | 描述 |
|------|----------|------|
| 系统接口 | 2 | 健康检查、API信息 |
| 认证模块 | 12 | 注册、登录、权限管理 |
| 用户管理 | 8 | 用户CRUD、批量操作 |
| 教练员管理 | 11 | 教练员CRUD、统计 |

## 详细接口列表

| # | 方法 | 端点 | 描述 | 认证 | 权限 | 限流 |
|---|------|------|------|------|------|------|
| **系统接口** |
| 1 | GET | `/health` | 健康检查 | ❌ | - | ❌ |
| 2 | GET | `/api/v1` | API信息 | ❌ | - | ✅ |
| **认证模块** |
| 3 | POST | `/api/v1/auth/register` | 用户注册 | ❌ | - | 🔥 |
| 4 | POST | `/api/v1/auth/login` | 用户登录 | ❌ | - | 🔥 |
| 5 | POST | `/api/v1/auth/refresh-token` | 刷新令牌 | ✅ | - | ✅ |
| 6 | GET | `/api/v1/auth/verify-email/:token` | 验证邮箱 | ❌ | - | ✅ |
| 7 | POST | `/api/v1/auth/forgot-password` | 忘记密码 | ❌ | - | 🔥 |
| 8 | POST | `/api/v1/auth/reset-password/:token` | 重置密码 | ❌ | - | 🔥 |
| 9 | POST | `/api/v1/auth/logout` | 退出登录 | ✅ | - | ✅ |
| 10 | POST | `/api/v1/auth/change-password` | 修改密码 | ✅ | - | ✅ |
| 11 | GET | `/api/v1/auth/profile` | 获取用户信息 | ✅ | - | ✅ |
| 12 | PUT | `/api/v1/auth/profile` | 更新用户信息 | ✅ | - | ✅ |
| 13 | DELETE | `/api/v1/auth/account` | 删除账户 | ✅ | - | ✅ |
| 14 | GET | `/api/v1/auth/admin/stats` | 用户统计信息 | ✅ | 👑 | ✅ |
| **用户管理模块** |
| 15 | GET | `/api/v1/users` | 获取用户列表 | ✅ | 👑 | ✅ |
| 16 | GET | `/api/v1/users/:userId` | 获取用户详情 | ✅ | 👑 | ✅ |
| 17 | PUT | `/api/v1/users/:userId` | 更新用户信息 | ✅ | 👑 | ✅ |
| 18 | PATCH | `/api/v1/users/:userId/status` | 更新用户状态 | ✅ | 👑 | ✅ |
| 19 | PATCH | `/api/v1/users/:userId/role` | 更新用户角色 | ✅ | 🔱 | ✅ |
| 20 | DELETE | `/api/v1/users/:userId` | 删除用户 | ✅ | 🔱 | ✅ |
| 21 | POST | `/api/v1/users/batch` | 批量操作用户 | ✅ | 👑 | ✅ |
| 22 | GET | `/api/v1/users/statistics/overview` | 用户统计信息 | ✅ | 👑 | ✅ |
| **教练员管理模块** |
| 23 | GET | `/api/v1/instructors/active` | 获取活跃教练员 | ❌ | - | ❌ |
| 24 | GET | `/api/v1/instructors/user/:userId` | 根据用户ID获取教练员 | ✅ | - | ✅ |
| 25 | GET | `/api/v1/instructors/stats` | 获取教练员统计 | ✅ | 👑 | ✅ |
| 26 | GET | `/api/v1/instructors` | 获取教练员列表 | ✅ | 👑 | ✅ |
| 27 | GET | `/api/v1/instructors/:id` | 获取教练员详情 | ✅ | 👑 | ✅ |
| 28 | POST | `/api/v1/instructors` | 创建教练员 | ✅ | 👑 | ✅ |
| 29 | POST | `/api/v1/instructors/batch` | 批量操作教练员 | ✅ | 👑 | ✅ |
| 30 | PUT | `/api/v1/instructors/:id` | 更新教练员信息 | ✅ | 👑 | ✅ |
| 31 | PATCH | `/api/v1/instructors/:id/status` | 更新教练员状态 | ✅ | 👑 | ✅ |
| 32 | PATCH | `/api/v1/instructors/:id/stats` | 更新教练员统计 | ✅ | 👑 | ✅ |
| 33 | DELETE | `/api/v1/instructors/:id` | 删除教练员 | ✅ | 🔱 | ✅ |

## 图例说明

### 认证状态
- ❌ 无需认证
- ✅ 需要JWT令牌

### 权限等级
- `-` 无特殊权限要求
- 👑 管理员权限 (admin/super_admin)
- 🔱 超级管理员权限 (super_admin)

### 限流等级
- ❌ 无限流
- ✅ 一般限流 (每分钟100次)
- 🔥 严格限流 (注册/登录/重置密码)

## 权限角色

| 角色 | 代码 | 描述 | 权限范围 |
|------|------|------|----------|
| 学员 | `student` | 普通用户 | 基础功能 |
| 教练 | `instructor` | 瑜伽教练 | 课程管理 |
| 管理员 | `admin` | 系统管理员 | 用户、教练管理 |
| 超级管理员 | `super_admin` | 超级管理员 | 所有权限 |

## 限流策略

| 类型 | 限制 | 适用接口 |
|------|------|----------|
| 一般请求 | 100次/分钟 | 大部分接口 |
| 认证请求 | 5次/15分钟 | 登录 |
| 注册请求 | 3次/小时 | 注册 |
| 密码重置 | 3次/小时 | 忘记密码、重置密码 |

## 响应状态码

| 状态码 | 说明 | 场景 |
|--------|------|------|
| 200 | 请求成功 | 一般请求 |
| 201 | 创建成功 | 注册、创建资源 |
| 400 | 请求参数错误 | 参数验证失败 |
| 401 | 未认证 | 缺少或无效Token |
| 403 | 权限不足 | 权限验证失败 |
| 404 | 资源不存在 | 用户、教练员不存在 |
| 422 | 验证错误 | 数据格式错误 |
| 429 | 请求频率超限 | 触发限流 |
| 500 | 服务器内部错误 | 系统异常 |

## API使用示例

### 1. 用户注册
```bash
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "fullName": "测试用户"
  }'
```

### 2. 用户登录
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 3. 获取用户信息
```bash
curl -X GET http://localhost:3000/api/v1/auth/profile \
  -H "Authorization: Bearer {access_token}"
```

### 4. 获取活跃教练员（公开接口）
```bash
curl -X GET http://localhost:3000/api/v1/instructors/active
```

## 开发工具推荐

- **API测试**: Postman, Insomnia
- **文档查看**: Swagger UI, Redoc
- **自动化测试**: Jest, Supertest
- **API监控**: Postman Monitor, New Relic

---

**文档版本**: 1.0.0  
**最后更新**: 2024-01-01  
**API版本**: v1 