# 瑜伽学习平台 API 文档

## 基本信息

- **基础URL**: `http://localhost:3000`
- **API版本**: `v1`
- **API前缀**: `/api/v1`
- **认证方式**: JWT Bearer Token

## 接口列表

### 系统接口

#### 1. 健康检查
- **GET** `/health`
- **描述**: 检查API服务状态
- **认证**: 无需认证
- **响应**: 
```json
{
  "success": true,
  "message": "API服务运行正常",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "environment": "development"
}
```

#### 2. API信息
- **GET** `/api/v1`
- **描述**: 获取API基本信息和端点列表
- **认证**: 无需认证
- **响应**:
```json
{
  "success": true,
  "message": "瑜伽学习平台 API",
  "version": "1.0.0",
  "endpoints": {
    "auth": "/api/v1/auth",
    "users": "/api/v1/users",
    "instructors": "/api/v1/instructors"
  }
}
```

### 认证模块 (/api/v1/auth)

#### 3. 用户注册
- **POST** `/api/v1/auth/register`
- **描述**: 用户注册
- **认证**: 无需认证
- **限流**: 每小时3次
- **请求体**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>", 
  "password": "password123",
  "fullName": "测试用户"
}
```

#### 4. 用户登录
- **POST** `/api/v1/auth/login`
- **描述**: 用户登录
- **认证**: 无需认证
- **限流**: 每15分钟5次
- **请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 5. 刷新令牌
- **POST** `/api/v1/auth/refresh-token`
- **描述**: 刷新访问令牌
- **认证**: Bearer Token (refresh token)

#### 6. 验证邮箱
- **GET** `/api/v1/auth/verify-email/:token`
- **描述**: 验证用户邮箱
- **认证**: 无需认证

#### 7. 忘记密码
- **POST** `/api/v1/auth/forgot-password`
- **描述**: 发送密码重置邮件
- **认证**: 无需认证
- **限流**: 每小时3次

#### 8. 重置密码
- **POST** `/api/v1/auth/reset-password/:token`
- **描述**: 重置用户密码
- **认证**: 无需认证
- **限流**: 每小时3次

#### 9. 退出登录
- **POST** `/api/v1/auth/logout`
- **描述**: 用户退出登录
- **认证**: Bearer Token

#### 10. 修改密码
- **POST** `/api/v1/auth/change-password`
- **描述**: 修改用户密码
- **认证**: Bearer Token

#### 11. 获取用户信息
- **GET** `/api/v1/auth/profile`
- **描述**: 获取当前用户信息
- **认证**: Bearer Token

#### 12. 更新用户信息
- **PUT** `/api/v1/auth/profile`
- **描述**: 更新当前用户信息
- **认证**: Bearer Token

#### 13. 删除账户
- **DELETE** `/api/v1/auth/account`
- **描述**: 删除用户账户
- **认证**: Bearer Token

#### 14. 用户统计信息（管理员）
- **GET** `/api/v1/auth/admin/stats`
- **描述**: 获取用户统计信息
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

### 用户管理模块 (/api/v1/users)

*所有用户管理接口都需要管理员权限*

#### 15. 获取用户列表
- **GET** `/api/v1/users`
- **描述**: 获取用户列表（分页、搜索、筛选）
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员
- **查询参数**: page, limit, search, role, status, isEmailVerified, sortBy, sortOrder

#### 16. 获取用户详情
- **GET** `/api/v1/users/:userId`
- **描述**: 获取指定用户详情
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 17. 更新用户信息（管理员）
- **PUT** `/api/v1/users/:userId`
- **描述**: 更新指定用户信息
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 18. 更新用户状态
- **PATCH** `/api/v1/users/:userId/status`
- **描述**: 更新用户状态（激活/禁用、邮箱验证）
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 19. 更新用户角色
- **PATCH** `/api/v1/users/:userId/role`
- **描述**: 更新用户角色
- **认证**: Bearer Token
- **权限**: 超级管理员

#### 20. 删除用户
- **DELETE** `/api/v1/users/:userId`
- **描述**: 删除用户（软删除）
- **认证**: Bearer Token
- **权限**: 超级管理员

#### 21. 批量操作用户
- **POST** `/api/v1/users/batch`
- **描述**: 批量操作用户
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 22. 用户统计信息
- **GET** `/api/v1/users/statistics/overview`
- **描述**: 获取用户统计信息
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

### 教练员管理模块 (/api/v1/instructors)

#### 23. 获取活跃教练员列表（公开）
- **GET** `/api/v1/instructors/active`
- **描述**: 获取活跃教练员列表
- **认证**: 无需认证

#### 24. 根据用户ID获取教练员信息
- **GET** `/api/v1/instructors/user/:userId`
- **描述**: 根据用户ID获取教练员信息
- **认证**: Bearer Token

#### 25. 获取教练员统计信息（管理员）
- **GET** `/api/v1/instructors/stats`
- **描述**: 获取教练员统计信息
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 26. 获取教练员列表（管理员）
- **GET** `/api/v1/instructors`
- **描述**: 获取教练员列表
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 27. 获取教练员详情（管理员）
- **GET** `/api/v1/instructors/:id`
- **描述**: 获取指定教练员详情
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 28. 创建教练员（管理员）
- **POST** `/api/v1/instructors`
- **描述**: 创建新教练员
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 29. 批量操作教练员（管理员）
- **POST** `/api/v1/instructors/batch`
- **描述**: 批量操作教练员
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 30. 更新教练员信息（管理员）
- **PUT** `/api/v1/instructors/:id`
- **描述**: 更新教练员信息
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 31. 更新教练员状态（管理员）
- **PATCH** `/api/v1/instructors/:id/status`
- **描述**: 更新教练员状态
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 32. 更新教练员统计信息（管理员）
- **PATCH** `/api/v1/instructors/:id/stats`
- **描述**: 更新教练员统计信息
- **认证**: Bearer Token
- **权限**: 管理员或超级管理员

#### 33. 删除教练员（超级管理员）
- **DELETE** `/api/v1/instructors/:id`
- **描述**: 删除教练员
- **认证**: Bearer Token
- **权限**: 超级管理员

## 权限说明

- **无需认证**: 任何人都可以访问
- **Bearer Token**: 需要有效的JWT访问令牌
- **管理员**: 需要admin或super_admin角色
- **超级管理员**: 只有super_admin角色可以访问

## 限流说明

- **一般请求**: 每分钟100次
- **认证请求**: 每15分钟5次
- **注册请求**: 每小时3次
- **密码重置**: 每小时3次

---

总计：**33个API接口**
- 系统接口：2个
- 认证模块：12个
- 用户管理模块：8个
- 教练员管理模块：11个 