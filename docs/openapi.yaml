openapi: 3.1.0
info:
  title: 瑜伽学习平台 API
  description: 瑜伽学习平台的RESTful API文档
  version: 1.0.0
  contact:
    name: API支持
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3000
    description: 开发环境
  - url: https://api.yoga-platform.com
    description: 生产环境

security:
  - BearerAuth: []

paths:
  /health:
    get:
      summary: 健康检查
      description: 检查API服务状态
      security: []
      responses:
        '200':
          description: 服务正常
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthResponse'

  /api/v1:
    get:
      summary: API信息
      description: 获取API基本信息和端点列表
      security: []
      responses:
        '200':
          description: API信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiInfoResponse'

  /api/v1/auth/register:
    post:
      summary: 用户注册
      description: 用户注册接口
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: 注册成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '422':
          $ref: '#/components/responses/ValidationError'

  /api/v1/auth/login:
    post:
      summary: 用户登录
      description: 用户登录接口
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /api/v1/auth/refresh-token:
    post:
      summary: 刷新令牌
      description: 刷新访问令牌
      responses:
        '200':
          description: 令牌刷新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshTokenResponse'

  /api/v1/auth/profile:
    get:
      summary: 获取用户信息
      description: 获取当前用户信息
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileResponse'
    put:
      summary: 更新用户信息
      description: 更新当前用户信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProfileRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'

  /api/v1/users:
    get:
      summary: 获取用户列表
      description: 获取用户列表（分页、搜索、筛选）
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 10
            maximum: 100
        - name: search
          in: query
          schema:
            type: string
        - name: role
          in: query
          schema:
            $ref: '#/components/schemas/UserRole'
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListResponse'

  /api/v1/users/{userId}:
    get:
      summary: 获取用户详情
      description: 获取指定用户详情
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetailResponse'

  /api/v1/instructors/active:
    get:
      summary: 获取活跃教练员列表
      description: 获取活跃教练员列表（公开接口）
      security: []
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActiveInstructorsResponse'

  /api/v1/instructors:
    get:
      summary: 获取教练员列表
      description: 获取教练员列表（管理员）
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InstructorListResponse'
    post:
      summary: 创建教练员
      description: 创建新教练员（管理员）
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInstructorRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InstructorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    UserRole:
      type: string
      enum:
        - student
        - instructor
        - admin
        - super_admin

    UserStatus:
      type: string
      enum:
        - active
        - inactive
        - suspended

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
        timestamp:
          type: string
          format: date-time

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
        error:
          type: string
        timestamp:
          type: string
          format: date-time

    HealthResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            version:
              type: string
              example: "1.0.0"
            environment:
              type: string
              example: "development"

    ApiInfoResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            version:
              type: string
              example: "1.0.0"
            endpoints:
              type: object
              properties:
                auth:
                  type: string
                  example: "/api/v1/auth"
                users:
                  type: string
                  example: "/api/v1/users"
                instructors:
                  type: string
                  example: "/api/v1/instructors"

    RegisterRequest:
      type: object
      required:
        - username
        - email
        - password
        - fullName
      properties:
        username:
          type: string
          minLength: 3
          maxLength: 30
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
        fullName:
          type: string
          minLength: 2
          maxLength: 50
        phoneNumber:
          type: string

    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
        password:
          type: string

    User:
      type: object
      properties:
        id:
          type: string
        username:
          type: string
        email:
          type: string
        fullName:
          type: string
        phoneNumber:
          type: string
        role:
          $ref: '#/components/schemas/UserRole'
        status:
          $ref: '#/components/schemas/UserStatus'
        isEmailVerified:
          type: boolean
        profile:
          type: object
          properties:
            avatar:
              type: string
            bio:
              type: string
        createdAt:
          type: string
          format: date-time
        lastLoginAt:
          type: string
          format: date-time

    RegisterResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                user:
                  $ref: '#/components/schemas/User'

    LoginResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                user:
                  $ref: '#/components/schemas/User'
                tokens:
                  type: object
                  properties:
                    accessToken:
                      type: string
                    refreshToken:
                      type: string
                    expiresIn:
                      type: integer

    RefreshTokenResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                accessToken:
                  type: string
                expiresIn:
                  type: integer

    UserProfileResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                user:
                  $ref: '#/components/schemas/User'

    UpdateProfileRequest:
      type: object
      properties:
        fullName:
          type: string
        phoneNumber:
          type: string
        profile:
          type: object
          properties:
            avatar:
              type: string
            bio:
              type: string

    UserListResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                users:
                  type: array
                  items:
                    $ref: '#/components/schemas/User'
                pagination:
                  type: object
                  properties:
                    currentPage:
                      type: integer
                    totalPages:
                      type: integer
                    totalItems:
                      type: integer
                    itemsPerPage:
                      type: integer
                    hasNextPage:
                      type: boolean
                    hasPrevPage:
                      type: boolean

    UserDetailResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                user:
                  $ref: '#/components/schemas/User'

    Instructor:
      type: object
      properties:
        id:
          type: string
        userId:
          type: string
        user:
          $ref: '#/components/schemas/User'
        specialties:
          type: array
          items:
            type: string
        experience:
          type: integer
        certification:
          type: array
          items:
            type: string
        bio:
          type: string
        hourlyRate:
          type: number
        isActive:
          type: boolean
        stats:
          type: object
          properties:
            totalStudents:
              type: integer
            totalClasses:
              type: integer
            rating:
              type: number
            completionRate:
              type: number
        createdAt:
          type: string
          format: date-time

    ActiveInstructorsResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                instructors:
                  type: array
                  items:
                    $ref: '#/components/schemas/Instructor'

    InstructorListResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                instructors:
                  type: array
                  items:
                    $ref: '#/components/schemas/Instructor'
                pagination:
                  type: object
                  properties:
                    currentPage:
                      type: integer
                    totalPages:
                      type: integer
                    totalItems:
                      type: integer

    CreateInstructorRequest:
      type: object
      required:
        - userId
        - specialties
        - experience
      properties:
        userId:
          type: string
        specialties:
          type: array
          items:
            type: string
        experience:
          type: integer
          minimum: 0
        certification:
          type: array
          items:
            type: string
        bio:
          type: string
        hourlyRate:
          type: number
          minimum: 0

    InstructorResponse:
      allOf:
        - $ref: '#/components/schemas/SuccessResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                instructor:
                  $ref: '#/components/schemas/Instructor'

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Unauthorized:
      description: 未授权访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    Forbidden:
      description: 权限不足
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    ValidationError:
      description: 数据验证失败
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

    RateLimitExceeded:
      description: 请求频率超限
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
              - type: object
                properties:
                  retryAfter:
                    type: integer

tags:
  - name: System
    description: 系统相关接口
  - name: Authentication
    description: 认证相关接口
  - name: Users
    description: 用户管理接口
  - name: Instructors
    description: 教练员管理接口 