{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/controllers/*": ["controllers/*"], "@/services/*": ["services/*"], "@/models/*": ["models/*"], "@/middleware/*": ["middleware/*"], "@/routes/*": ["routes/*"], "@/utils/*": ["utils/*"], "@/config/*": ["config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}