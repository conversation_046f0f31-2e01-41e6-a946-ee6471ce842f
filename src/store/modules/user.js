import router from '@/router'
import { ElMessageBox, ElMessage } from 'element-plus'
import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { isHttp, isEmpty } from "@/utils/validate"
import defAva from '@/assets/images/profile.jpg'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      id: '',
      name: '',
      nickName: '',
      avatar: '',
      email: '',
      role: '',
      roles: [],
      permissions: [],
      userInfo: null
    }),
    actions: {
      // 登录 - 适配瑜伽平台API
      login(userInfo) {
        const emailOrUsername = userInfo.emailOrUsername?.trim() || userInfo.username?.trim()
        const password = userInfo.password
        
        return new Promise((resolve, reject) => {
          login(emailOrUsername, password).then(res => {
            // 瑜伽平台API响应格式: { success: true, data: { user, accessToken, refreshToken } }
            const responseData = res.data || res
            const token = responseData.accessToken || responseData.token
            const user = responseData.user || responseData
            
            if (token) {
              setToken(token)
              this.token = token
              
              // 设置用户基本信息
              if (user) {
                this.setUserInfo(user)
              }
              
              resolve(res)
            } else {
              reject(new Error('登录响应中缺少token'))
            }
          }).catch(error => {
            reject(error)
          })
        })
      },
      
      // 设置用户信息 - 适配瑜伽平台用户数据结构
      setUserInfo(user) {
        this.userInfo = user
        this.id = user._id || user.userId || user.id || ''
        this.name = user.username || user.userName || user.name || ''
        this.nickName = user.nickname || user.nickName || user.username || ''
        this.email = user.email || ''
        this.role = user.role || 'user'
        
        // 处理头像
        let avatar = user.avatar || user.avatarUrl || ""
        if (!isHttp(avatar)) {
          avatar = (isEmpty(avatar)) ? defAva : import.meta.env.VITE_APP_BASE_API + avatar
        }
        this.avatar = avatar
        
        // 根据用户角色设置权限
        switch (user.role) {
          case 'admin':
            this.roles = ['admin', 'instructor', 'user']
            this.permissions = [
              'system:user:view', 'system:user:edit', 'system:user:delete',
              'business:course:view', 'business:course:edit', 'business:course:delete',
              'business:instructor:view', 'business:instructor:edit', 'business:instructor:delete',
              'business:booking:view', 'business:booking:edit', 'business:booking:delete',
              'business:schedule:view', 'business:schedule:edit', 'business:schedule:delete',
              'statistics:view'
            ]
            break
          case 'instructor':
            this.roles = ['instructor', 'user']
            this.permissions = [
              'business:course:view', 'business:course:edit',
              'business:booking:view', 'business:booking:edit',
              'business:schedule:view', 'business:schedule:edit',
              'profile:view', 'profile:edit'
            ]
            break
          default:
            this.roles = ['user']
            this.permissions = [
              'business:course:view',
              'business:booking:view',
              'profile:view', 'profile:edit'
            ]
        }
      },
      
      // 获取用户信息 - 适配瑜伽平台API
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            // 瑜伽平台API响应格式
            const responseData = res.data || res
            const user = responseData.user || responseData
            
            if (user) {
              this.setUserInfo(user)
              
              // 检查是否需要修改密码（如果后端有相关字段）
              if (user.isDefaultPassword) {
                ElMessageBox.confirm('您的密码还是初始密码，请修改密码！', '安全提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  router.push({ name: 'Profile', params: { activeTab: 'resetPwd' } })
                }).catch(() => {})
              }
              
              resolve(res)
            } else {
              reject(new Error('获取用户信息失败'))
            }
          }).catch(error => {
            reject(error)
          })
        })
      },
      
      // 退出系统 - 适配瑜伽平台API
      logOut() {
        return new Promise((resolve, reject) => {
          logout().then(() => {
            this.token = ''
            this.roles = []
            this.permissions = []
            this.userInfo = null
            this.id = ''
            this.name = ''
            this.nickName = ''
            this.email = ''
            this.role = ''
            this.avatar = ''
            removeToken()
            resolve()
          }).catch(error => {
            // 即使退出接口失败，也要清除本地数据
            this.token = ''
            this.roles = []
            this.permissions = []
            this.userInfo = null
            this.id = ''
            this.name = ''
            this.nickName = ''
            this.email = ''
            this.role = ''
            this.avatar = ''
            removeToken()
            resolve()
          })
        })
      },
      
      // 刷新Token
      refreshToken() {
        return new Promise((resolve, reject) => {
          // 这里可以添加刷新token的逻辑
          // 暂时返回当前token
          resolve(this.token)
        })
      },
      
      // 清除用户信息
      clearUserInfo() {
        this.token = ''
        this.roles = []
        this.permissions = []
        this.userInfo = null
        this.id = ''
        this.name = ''
        this.nickName = ''
        this.email = ''
        this.role = ''
        this.avatar = ''
        removeToken()
      }
    }
  })

export default useUserStore 