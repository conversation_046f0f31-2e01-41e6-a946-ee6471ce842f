import express from 'express';
import cors from 'cors';
import { config } from './config';
import routes from './routes/index';

// 创建Express应用
const app = express();

console.log('🔥 Express应用创建完成，加载新的路由定义...');

// CORS配置 - 允许前端访问
app.use(cors({
  origin: [
    'http://localhost',
    'http://localhost:80', 
    'http://localhost:8080',
    'http://localhost:3001',  // 前端开发服务器
    'http://localhost:3002'   // 前端备用端口
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
}));

// 基础中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, _res, next) => {
  console.log(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// === 预约API路由已移动到 routes/index.ts ===

// API路由 - 同时支持 /api 前缀和直接路径
app.use('/api', routes);  // 处理 /api/login 等请求
app.use('/', routes);     // 处理 /login 等请求

// 404处理
app.use('*', (req, res) => {
  console.log('404 - Route not found:', req.method, req.originalUrl);
  res.status(404).json({
    msg: '路由不存在',
    code: 404,
    path: req.originalUrl
  });
});

// 启动服务器
const startServer = async (): Promise<void> => {
  try {
    console.log('跳过数据库连接，启动简化服务器...');

    // 启动HTTP服务器
    const server = app.listen(config.app.port, config.app.host, () => {
      console.log(`🚀 服务器启动成功 - http://${config.app.host}:${config.app.port}`);
      console.log(`📚 健康检查: http://${config.app.host}:${config.app.port}/health`);
      console.log(`🌍 环境: ${config.app.env}`);
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal: string) => {
      console.log(`收到 ${signal} 信号，开始优雅关闭...`);
      
      server.close(() => {
        console.log('HTTP 服务器已关闭');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        console.error('强制关闭');
        process.exit(1);
      }, 10000);
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
};

// 启动服务器
startServer(); 