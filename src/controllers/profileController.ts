import { Request, Response, NextFunction } from 'express';
import { UserService, ProfileUpdateData, PreferencesUpdateData, PasswordChangeData } from '@/services/userService';
import { ActivityType } from '@/models/UserActivity';
import { ApiResponse } from '@/utils/types';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// 文件上传配置
const storage = multer.diskStorage({
  destination: (_req, _file, cb) => {
    const uploadDir = 'uploads/avatars';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const userId = req.user?.id;
    const ext = path.extname(file.originalname);
    cb(null, `avatar-${userId}-${Date.now()}${ext}`);
  }
});

const fileFilter = (_req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传 JPEG、JPG、PNG、GIF 格式的图片'));
  }
};

export const avatarUpload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  }
});

export class ProfileController {
  /**
   * 获取个人资料
   */
  static async getProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      
      const profile = await UserService.getProfile(userId);

      const response: ApiResponse = {
        success: true,
        data: { profile },
        message: '个人资料获取成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新个人资料
   */
  static async updateProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const updateData: ProfileUpdateData = req.body;
      const ipAddress = req.ip;
      const userAgent = req.get('User-Agent');

      const profile = await UserService.updateProfile(userId, updateData, ipAddress, userAgent);

      const response: ApiResponse = {
        success: true,
        data: { profile },
        message: '个人资料更新成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新偏好设置
   */
  static async updatePreferences(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const updateData: PreferencesUpdateData = req.body;
      const ipAddress = req.ip;
      const userAgent = req.get('User-Agent');

      const profile = await UserService.updatePreferences(userId, updateData, ipAddress, userAgent);

      const response: ApiResponse = {
        success: true,
        data: { profile },
        message: '偏好设置更新成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更改密码
   */
  static async changePassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const passwordData: PasswordChangeData = req.body;
      const ipAddress = req.ip;
      const userAgent = req.get('User-Agent');

      await UserService.changePassword(userId, passwordData, ipAddress, userAgent);

      const response: ApiResponse = {
        success: true,
        message: '密码修改成功，请重新登录',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 上传头像
   */
  static async uploadAvatar(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const file = req.file;
      const ipAddress = req.ip;
      const userAgent = req.get('User-Agent');

      if (!file) {
        const response: ApiResponse = {
          success: false,
          message: '请选择要上传的头像文件',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      // 构建头像URL
      const avatarUrl = `/uploads/avatars/${file.filename}`;

      const profile = await UserService.uploadAvatar(userId, avatarUrl, ipAddress, userAgent);

      const response: ApiResponse = {
        success: true,
        data: { 
          profile,
          avatarUrl
        },
        message: '头像上传成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户活动历史
   */
  static async getActivities(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      
      // 分页参数
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 50); // 最大50条
      
      // 筛选参数
      const type = req.query.type as ActivityType;
      const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
      const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;

      const result = await UserService.getUserActivities(userId, {
        type,
        page,
        limit,
        startDate,
        endDate
      });

      const response: ApiResponse = {
        success: true,
        data: result,
        message: '活动历史获取成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除个人账户
   */
  static async deleteAccount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      const { password } = req.body;
      const ipAddress = req.ip;
      const userAgent = req.get('User-Agent');

      if (!password) {
        const response: ApiResponse = {
          success: false,
          message: '请输入密码以确认删除账户',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      await UserService.deleteAccount(userId, password, ipAddress, userAgent);

      const response: ApiResponse = {
        success: true,
        message: '账户删除成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取个人统计信息
   */
  static async getStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user!.id;
      
      // 获取基本统计信息
      const profile = await UserService.getProfile(userId);
      
      // 获取近期活动统计
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentActivities = await UserService.getUserActivities(userId, {
        startDate: thirtyDaysAgo,
        page: 1,
        limit: 1000 // 获取所有记录用于统计
      });

      // 统计活动类型
      const activityStats = recentActivities.data.reduce((stats: Record<string, number>, activity: any) => {
        stats[activity.type] = (stats[activity.type] || 0) + 1;
        return stats;
      }, {});

      const stats = {
        profile: {
          joinDate: profile.createdAt,
          isEmailVerified: profile.isEmailVerified,
          isPhoneVerified: profile.isPhoneVerified,
          yogaExperience: profile.yogaExperience,
          preferredStylesCount: profile.preferredStyles?.length || 0,
          lastLoginAt: profile.lastLoginAt
        },
        activities: {
          totalActivities: recentActivities.pagination.total,
          last30DaysActivities: recentActivities.data.length,
          activityTypeStats: activityStats
        }
      };

      const response: ApiResponse = {
        success: true,
        data: { stats },
        message: '个人统计信息获取成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }
} 