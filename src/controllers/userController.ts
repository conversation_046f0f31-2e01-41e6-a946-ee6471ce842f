import { Request, Response, NextFunction } from 'express';
import { UserService, UserListQuery, UpdateUserData, BatchUserOperation } from '@/services/userService';
import { ApiResponse, PaginationParams, UserRole } from '@/utils/types';

export class UserController {
  /**
   * 获取用户列表（分页、搜索、筛选）
   */
  static async getUserList(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 分页参数
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100); // 最大100条
      const sortBy = req.query.sortBy as string || 'createdAt';
      const sortOrder = req.query.sortOrder as 'asc' | 'desc' || 'desc';

      const pagination: PaginationParams = {
        page,
        limit,
        sortBy,
        sortOrder
      };

      // 查询参数
      const query: UserListQuery = {
        search: req.query.search as string,
        role: req.query.role as UserRole,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        isEmailVerified: req.query.isEmailVerified ? req.query.isEmailVerified === 'true' : undefined,
        dateFrom: req.query.dateFrom ? new Date(req.query.dateFrom as string) : undefined,
        dateTo: req.query.dateTo ? new Date(req.query.dateTo as string) : undefined
      };

      const result = await UserService.getUserList(query, pagination);

      const response: ApiResponse = {
        success: true,
        data: result,
        message: '用户列表获取成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户详情
   */
  static async getUserById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userId } = req.params;
      
      const user = await UserService.getUserById(userId);

      const response: ApiResponse = {
        success: true,
        data: { user },
        message: '用户详情获取成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新用户信息（管理员）
   */
  static async updateUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userId } = req.params;
      const updateData: UpdateUserData = req.body;

      const user = await UserService.updateUser(userId, updateData);

      const response: ApiResponse = {
        success: true,
        data: { user },
        message: '用户信息更新成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新用户状态
   */
  static async updateUserStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userId } = req.params;
      const { isActive, isEmailVerified } = req.body;

      const status: { isActive?: boolean; isEmailVerified?: boolean } = {};
      if (isActive !== undefined) status.isActive = isActive;
      if (isEmailVerified !== undefined) status.isEmailVerified = isEmailVerified;

      if (Object.keys(status).length === 0) {
        const response: ApiResponse = {
          success: false,
          message: '请提供要更新的状态信息',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      const user = await UserService.updateUserStatus(userId, status);

      const response: ApiResponse = {
        success: true,
        data: { user },
        message: '用户状态更新成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新用户角色
   */
  static async updateUserRole(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userId } = req.params;
      const { role } = req.body;

      if (!role || !Object.values(UserRole).includes(role)) {
        const response: ApiResponse = {
          success: false,
          message: '请提供有效的用户角色',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      const user = await UserService.updateUserRole(userId, role);

      const response: ApiResponse = {
        success: true,
        data: { user },
        message: '用户角色更新成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除用户（软删除）
   */
  static async deleteUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userId } = req.params;

      await UserService.deleteUser(userId);

      const response: ApiResponse = {
        success: true,
        message: '用户删除成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 批量操作用户
   */
  static async batchUserOperation(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const operation: BatchUserOperation = req.body;

      if (!operation.action || !operation.userIds || !Array.isArray(operation.userIds)) {
        const response: ApiResponse = {
          success: false,
          message: '请提供有效的批量操作参数',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      if (!['activate', 'deactivate', 'delete', 'verifyEmail'].includes(operation.action)) {
        const response: ApiResponse = {
          success: false,
          message: '不支持的批量操作类型',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      if (operation.userIds.length === 0) {
        const response: ApiResponse = {
          success: false,
          message: '请提供要操作的用户ID列表',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      if (operation.userIds.length > 100) {
        const response: ApiResponse = {
          success: false,
          message: '批量操作最多支持100个用户',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      const result = await UserService.batchUserOperation(operation);

      const response: ApiResponse = {
        success: true,
        data: result,
        message: '批量操作完成',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStatistics(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const statistics = await UserService.getUserStatistics();

      const response: ApiResponse = {
        success: true,
        data: statistics,
        message: '用户统计信息获取成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);

    } catch (error) {
      next(error);
    }
  }
} 