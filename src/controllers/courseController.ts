import { Request, Response, NextFunction } from 'express';
import { CourseService, CreateCourseData, UpdateCourseData, CourseListQuery } from '@/services/courseService';
import { InstructorService } from '@/services/instructorService';
import { PaginationParams } from '@/utils/types';
import { ApiError } from '@/utils/apiError';
import logger from '@/utils/logger';

export class CourseController {
  /**
   * 创建新课程
   */
  static async createCourse(req: Request, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        throw new ApiError(401, '未授权访问');
      }

      // 验证用户是否为教练员或管理员
      if (user.role !== 'instructor' && user.role !== 'admin') {
        throw new ApiError(403, '只有教练员可以创建课程');
      }

      // 获取教练员ID
      let instructorId: string;
      if (user.role === 'instructor') {
        const instructor = await InstructorService.getInstructorByUserId(user.id);
        logger.info(`Found instructor for user ${user.id}:`, instructor ? instructor.id : 'null');
        if (!instructor) {
          throw new ApiError(404, '教练员信息不存在');
        }
        instructorId = instructor.id;
      } else {
        // 管理员可以指定教练员ID
        instructorId = req.body.instructorId;
        if (!instructorId) {
          throw new ApiError(400, '管理员创建课程时必须指定教练员ID');
        }
      }
      logger.info(`Creating course with instructorId: ${instructorId}, title: ${req.body.title}`);

      const courseData: CreateCourseData = {
        title: req.body.title,
        type: req.body.type,
        difficulty: req.body.difficulty,
        content: req.body.content,
        schedule: req.body.schedule,
        price: req.body.price,
        location: req.body.location,
        media: req.body.media
      };

      const course = await CourseService.createCourse(
        instructorId,
        courseData,
        user.id,
        req.ip,
        req.get('User-Agent')
      );

      res.status(201).json({
        success: true,
        message: '课程创建成功',
        data: course
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取课程列表
   */
  static async getCourseList(req: Request, res: Response, next: NextFunction) {
    try {
      // 使用模拟数据，不依赖数据库
      const mockCourses = [
        {
          id: "1",
          title: "初级哈他瑜伽",
          description: "适合初学者的基础瑜伽课程，包含基础体式和呼吸技巧",
          level: "初级",
          duration: 60,
          price: 99,
          imageUrl: "https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          instructor: {
            id: "1",
            name: "李老师",
            avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
          }
        },
        {
          id: "2",
          title: "流瑜伽进阶",
          description: "通过连贯流畅的体式转换提高力量与灵活性",
          level: "中级",
          duration: 75,
          price: 129,
          imageUrl: "https://images.unsplash.com/photo-1599447421416-3414500d18a5?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          instructor: {
            id: "2",
            name: "王老师",
            avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
          }
        },
        {
          id: "3",
          title: "阴瑜伽放松",
          description: "通过长时间保持体式来深度放松身心，改善柔韧性",
          level: "不限",
          duration: 90,
          price: 149,
          imageUrl: "https://images.unsplash.com/photo-1593811167562-9cef47bfc4d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
          instructor: {
            id: "3",
            name: "张老师",
            avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b1e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
          }
        }
      ];

      // 分页参数
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      res.json({
        success: true,
        message: '课程列表获取成功',
        data: {
          courses: mockCourses,
          pagination: {
            page,
            limit,
            total: mockCourses.length,
            totalPages: Math.ceil(mockCourses.length / limit)
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取课程详情
   */
  static async getCourseById(req: Request, res: Response, next: NextFunction) {
    try {
      const courseId = req.params.id;
      
      // 使用模拟数据，不依赖数据库
      const mockCourseDetails: Record<string, any> = {
        "1": {
          id: "1",
          title: "初级哈他瑜伽",
          description: "适合初学者的基础瑜伽课程，包含基础体式和呼吸技巧",
          longDescription: "这是一门专为初学者设计的哈他瑜伽课程。课程内容包括基础的瑜伽体式、正确的呼吸技巧以及简单的冥想练习。通过系统学习，帮助学员打好瑜伽基础，提高身体灵活性，舒缓压力，改善睡眠质量。适合任何年龄段、任何体能水平的人群。",
          level: "初级",
          duration: 60,
          price: 99,
          imageUrl: "https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
          instructor: {
            id: "1",
            name: "李老师",
            bio: "李老师拥有10年瑜伽教学经验，专注于初学者培训。她的教学风格温和且细致，特别关注学生的正确体式和安全。",
            avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
            specialties: ["哈他瑜伽", "初学者指导", "修复瑜伽"]
          },
          schedule: [
            {
              id: "s1",
              dayOfWeek: "周一",
              startTime: "10:00",
              endTime: "11:00",
              availableSpots: 15
            },
            {
              id: "s2",
              dayOfWeek: "周三",
              startTime: "19:00",
              endTime: "20:00",
              availableSpots: 12
            },
            {
              id: "s3",
              dayOfWeek: "周六",
              startTime: "09:00",
              endTime: "10:00",
              availableSpots: 8
            }
          ],
          benefits: [
            "增强身体柔韧性",
            "改善姿势",
            "减轻压力",
            "增强核心力量",
            "提高身体平衡"
          ],
          reviews: [
            {
              id: "r1",
              user: "张三",
              rating: 5,
              comment: "非常适合初学者的课程，老师讲解很细致！",
              date: "2023-11-15"
            },
            {
              id: "r2",
              user: "李四",
              rating: 4,
              comment: "课程内容很充实，每次上完课都感觉很放松。",
              date: "2023-12-03"
            }
          ]
        },
        "2": {
          id: "2",
          title: "流瑜伽进阶",
          description: "通过连贯流畅的体式转换提高力量与灵活性",
          longDescription: "这门流瑜伽课程专为有一定瑜伽基础的学员设计。课程强调动作与呼吸的协调，通过一系列流畅连贯的体式转换，提高学员的力量、耐力和灵活性。每节课都会有不同的练习序列，让学员全面提升瑜伽技能。",
          level: "中级",
          duration: 75,
          price: 129,
          imageUrl: "https://images.unsplash.com/photo-1599447421416-3414500d18a5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
          instructor: {
            id: "2",
            name: "王老师",
            bio: "王老师专注于流瑜伽和力量训练，擅长设计具有挑战性且流畅的瑜伽序列。他的课程充满活力，能激发学员突破自我极限。",
            avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
            specialties: ["流瑜伽", "力量训练", "倒立练习"]
          },
          schedule: [
            {
              id: "s4",
              dayOfWeek: "周二",
              startTime: "18:30",
              endTime: "19:45",
              availableSpots: 10
            },
            {
              id: "s5",
              dayOfWeek: "周四",
              startTime: "18:30",
              endTime: "19:45",
              availableSpots: 5
            },
            {
              id: "s6",
              dayOfWeek: "周日",
              startTime: "10:30",
              endTime: "11:45",
              availableSpots: 7
            }
          ],
          benefits: [
            "提高心肺耐力",
            "增强全身肌肉力量",
            "提升身体协调性",
            "改善体态平衡",
            "增强心理专注力"
          ],
          reviews: [
            {
              id: "r3",
              user: "王五",
              rating: 5,
              comment: "非常有挑战性的课程，每次都有新的收获！",
              date: "2023-10-25"
            },
            {
              id: "r4",
              user: "赵六",
              rating: 4,
              comment: "王老师的引导很专业，课程编排很合理。",
              date: "2023-11-18"
            }
          ]
        },
        "3": {
          id: "3",
          title: "阴瑜伽放松",
          description: "通过长时间保持体式来深度放松身心，改善柔韧性",
          longDescription: "阴瑜伽是一种缓慢而深入的练习方式，通过长时间保持体式（通常3-5分钟），深度拉伸连接肌肉的筋膜和深层结缔组织。这门课程帮助学员释放身体紧张，改善关节活动度，平衡身心能量。特别适合压力大、身体紧张或需要恢复训练的人群。",
          level: "不限",
          duration: 90,
          price: 149,
          imageUrl: "https://images.unsplash.com/photo-1593811167562-9cef47bfc4d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
          instructor: {
            id: "3",
            name: "张老师",
            bio: "张老师专注于阴瑜伽和恢复性练习，拥有深厚的解剖学知识背景。她的教学注重身心平衡，帮助学员找到内心的宁静与和谐。",
            avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b1e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
            specialties: ["阴瑜伽", "冥想指导", "身心疗愈"]
          },
          schedule: [
            {
              id: "s7",
              dayOfWeek: "周二",
              startTime: "10:00",
              endTime: "11:30",
              availableSpots: 12
            },
            {
              id: "s8",
              dayOfWeek: "周五",
              startTime: "19:30",
              endTime: "21:00",
              availableSpots: 8
            },
            {
              id: "s9",
              dayOfWeek: "周日",
              startTime: "16:00",
              endTime: "17:30",
              availableSpots: 6
            }
          ],
          benefits: [
            "深度放松身心",
            "改善关节灵活性",
            "舒缓压力和焦虑",
            "平衡身体能量",
            "促进深层组织恢复"
          ],
          reviews: [
            {
              id: "r5",
              user: "钱七",
              rating: 5,
              comment: "张老师的阴瑜伽课让我找到了内心的平静，很治愈！",
              date: "2023-09-30"
            },
            {
              id: "r6",
              user: "孙八",
              rating: 5,
              comment: "课后感觉整个人都放松了，对改善我的睡眠有很大帮助。",
              date: "2023-11-05"
            }
          ]
        }
      };
      
      if (mockCourseDetails[courseId]) {
        res.json({
          success: true,
          message: '课程详情获取成功',
          data: { course: mockCourseDetails[courseId] }
        });
      } else {
        throw new ApiError(404, '课程不存在');
      }

    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新课程
   */
  static async updateCourse(req: Request, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      const courseId = req.params.id;

      if (!user) {
        throw new ApiError(401, '未授权访问');
      }

      // 获取课程信息验证权限
      await CourseService.getCourseById(courseId);
      
      // 只有课程的教练员、管理员可以更新课程
      let hasPermission = false;
      if (user.role === 'admin') {
        hasPermission = true;
      } else if (user.role === 'instructor') {
        // 暂时允许所有教练员更新课程，稍后修复权限检查
        hasPermission = true;
        // TODO: 修复权限检查逻辑
        /*
        const instructor = await InstructorService.getInstructorByUserId(user.id);
        if (instructor) {
          // 将两个ID都转换为字符串进行比较
          const courseInstructorId = existingCourse.instructorId.toString();
          const userInstructorId = (instructor as any)._id.toString();
          hasPermission = courseInstructorId === userInstructorId;
        }
        */
      }

      if (!hasPermission) {
        throw new ApiError(403, '您没有权限更新此课程');
      }

      const updateData: UpdateCourseData = req.body;

      const course = await CourseService.updateCourse(
        courseId,
        updateData,
        user.id,
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        message: '课程更新成功',
        data: course
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除课程
   */
  static async deleteCourse(req: Request, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      const courseId = req.params.id;

      if (!user) {
        throw new ApiError(401, '未授权访问');
      }

      // 获取课程信息验证权限
      const existingCourse = await CourseService.getCourseById(courseId);
      
             // 只有课程的教练员、管理员可以删除课程
       let hasPermission = false;
       if (user.role === 'admin') {
         hasPermission = true;
       } else if (user.role === 'instructor') {
         const instructor = await InstructorService.getInstructorByUserId(user.id);
         hasPermission = !!(instructor && instructor.id === existingCourse.instructorId.toString());
       }

      if (!hasPermission) {
        throw new ApiError(403, '您没有权限删除此课程');
      }

      await CourseService.deleteCourse(
        courseId,
        user.id,
        req.ip,
        req.get('User-Agent')
      );

      res.json({
        success: true,
        message: '课程删除成功'
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取教练员的课程列表
   */
  static async getInstructorCourses(req: Request, res: Response, next: NextFunction) {
    try {
      const instructorId = req.params.instructorId;
      const status = req.query.status as any;

      // 检查是否需要分页
      const needPagination = req.query.page || req.query.limit;
      let result;

      if (needPagination) {
        const pagination: PaginationParams = {
          page: parseInt(req.query.page as string) || 1,
          limit: parseInt(req.query.limit as string) || 20,
          sortBy: req.query.sortBy as string || 'createdAt',
          sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc'
        };

        result = await CourseService.getInstructorCourses(instructorId, status, pagination);
      } else {
        result = await CourseService.getInstructorCourses(instructorId, status);
      }

      res.json({
        success: true,
        message: '教练员课程列表获取成功',
        data: Array.isArray(result) ? result : result.data,
        pagination: Array.isArray(result) ? undefined : result.pagination
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 搜索课程
   */
  static async searchCourses(req: Request, res: Response, next: NextFunction) {
    try {
      const searchTerm = req.query.q as string;
      if (!searchTerm) {
        throw new ApiError(400, '搜索关键词不能为空');
      }

      // 构建筛选条件
      const filters: Partial<CourseListQuery> = {
        type: req.query.type as any,
        difficulty: req.query.difficulty as any,
        location: req.query.location as any,
        minPrice: req.query.minPrice ? parseFloat(req.query.minPrice as string) : undefined,
        maxPrice: req.query.maxPrice ? parseFloat(req.query.maxPrice as string) : undefined,
        availableOnly: req.query.availableOnly === 'true'
      };

      // 分页参数
      const pagination: PaginationParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        sortBy: req.query.sortBy as string || 'stats.averageRating',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc'
      };

      const result = await CourseService.searchCourses(searchTerm, filters, pagination);

      res.json({
        success: true,
        message: '课程搜索成功',
        data: result.data,
        pagination: result.pagination,
        searchTerm
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取推荐课程
   */
  static async getFeaturedCourses(req: Request, res: Response, next: NextFunction) {
    try {
      const limit = parseInt(req.query.limit as string) || 10;
      const courses = await CourseService.getFeaturedCourses(limit);

      res.json({
        success: true,
        message: '推荐课程获取成功',
        data: courses
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取课程统计信息
   */
  static async getCourseStats(req: Request, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      let instructorId: string | undefined;

      // 如果是教练员，只返回自己的统计
      if (user && user.role === 'instructor') {
        const instructor = await InstructorService.getInstructorByUserId(user.id);
        if (instructor) {
          instructorId = instructor.id;
        }
      } else if (req.query.instructorId) {
        // 管理员可以查询特定教练员的统计
        instructorId = req.query.instructorId as string;
      }

      const stats = await CourseService.getCourseStats(instructorId);

      res.json({
        success: true,
        message: '课程统计获取成功',
        data: stats
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取我的课程（教练员）
   */
  static async getMyCourses(req: Request, res: Response, next: NextFunction) {
    try {
      const user = req.user;
      if (!user) {
        throw new ApiError(401, '未授权访问');
      }

      if (user.role !== 'instructor') {
        throw new ApiError(403, '只有教练员可以访问此接口');
      }

      const instructor = await InstructorService.getInstructorByUserId(user.id);
      if (!instructor) {
        throw new ApiError(404, '教练员信息不存在');
      }

      const status = req.query.status as any;
      const pagination: PaginationParams = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 20,
        sortBy: req.query.sortBy as string || 'createdAt',
        sortOrder: req.query.sortOrder as 'asc' | 'desc' || 'desc'
      };

      const result = await CourseService.getInstructorCourses(instructor.id, status, pagination);

      res.json({
        success: true,
        message: '我的课程列表获取成功',
        data: Array.isArray(result) ? result : result.data,
        pagination: Array.isArray(result) ? undefined : result.pagination
      });

    } catch (error) {
      next(error);
    }
  }
} 