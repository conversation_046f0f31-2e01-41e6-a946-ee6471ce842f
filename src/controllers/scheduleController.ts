import { Request, Response } from 'express';
import { ScheduleService } from '../services/scheduleService';
import { ScheduleStatus } from '../models/CourseSchedule';
import logger from '../utils/logger';

export class ScheduleController {
  /**
   * 创建课程时间段
   */
  static async createSchedule(req: Request, res: Response): Promise<void> {
    try {
      const scheduleData = req.body;
      
      // 确保时间是Date对象
      if (scheduleData.startTime) {
        scheduleData.startTime = new Date(scheduleData.startTime);
      }
      if (scheduleData.endTime) {
        scheduleData.endTime = new Date(scheduleData.endTime);
      }
      if (scheduleData.recurringPattern?.endDate) {
        scheduleData.recurringPattern.endDate = new Date(scheduleData.recurringPattern.endDate);
      }

      const schedule = await ScheduleService.createSchedule(scheduleData);

      res.status(201).json({
        success: true,
        message: '课程时间段创建成功',
        data: schedule
      });

    } catch (error) {
      logger.error('Create schedule error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '创建课程时间段失败'
      });
    }
  }

  /**
   * 获取时间段列表
   */
  static async getScheduleList(req: Request, res: Response): Promise<void> {
    try {
      const query = req.query;
      
      // 转换查询参数
      const scheduleQuery: any = {
        ...query,
        page: query.page ? parseInt(query.page as string) : 1,
        limit: query.limit ? parseInt(query.limit as string) : 20,
        availableOnly: query.availableOnly === 'true'
      };

      // 转换日期参数
      if (query.startDate) {
        scheduleQuery.startDate = new Date(query.startDate as string);
      }
      if (query.endDate) {
        scheduleQuery.endDate = new Date(query.endDate as string);
      }

      const result = await ScheduleService.getScheduleList(scheduleQuery);

      res.json({
        success: true,
        message: '获取时间段列表成功',
        data: result.schedules,
        pagination: result.pagination
      });

    } catch (error) {
      logger.error('Get schedule list error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '获取时间段列表失败'
      });
    }
  }

  /**
   * 获取单个时间段详情
   */
  static async getScheduleById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const schedule = await ScheduleService.getScheduleById(id);

      res.json({
        success: true,
        message: '获取时间段详情成功',
        data: schedule
      });

    } catch (error) {
      logger.error('Get schedule by ID error:', error);
      const status = error instanceof Error && error.message === '课程时间段不存在' ? 404 : 400;
      res.status(status).json({
        success: false,
        message: error instanceof Error ? error.message : '获取时间段详情失败'
      });
    }
  }

  /**
   * 更新时间段
   */
  static async updateSchedule(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // 转换时间字段
      if (updateData.startTime) {
        updateData.startTime = new Date(updateData.startTime);
      }
      if (updateData.endTime) {
        updateData.endTime = new Date(updateData.endTime);
      }

      const schedule = await ScheduleService.updateSchedule(id, updateData);

      res.json({
        success: true,
        message: '更新时间段成功',
        data: schedule
      });

    } catch (error) {
      logger.error('Update schedule error:', error);
      const status = error instanceof Error && error.message === '课程时间段不存在' ? 404 : 400;
      res.status(status).json({
        success: false,
        message: error instanceof Error ? error.message : '更新时间段失败'
      });
    }
  }

  /**
   * 删除时间段
   */
  static async deleteSchedule(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      await ScheduleService.deleteSchedule(id);

      res.json({
        success: true,
        message: '删除时间段成功'
      });

    } catch (error) {
      logger.error('Delete schedule error:', error);
      const status = error instanceof Error && error.message === '课程时间段不存在' ? 404 : 400;
      res.status(status).json({
        success: false,
        message: error instanceof Error ? error.message : '删除时间段失败'
      });
    }
  }

  /**
   * 获取可用时间段
   */
  static async getAvailableSchedules(req: Request, res: Response): Promise<void> {
    try {
      const { startDate, endDate } = req.query;
      
      const start = startDate ? new Date(startDate as string) : undefined;
      const end = endDate ? new Date(endDate as string) : undefined;

      const schedules = await ScheduleService.getAvailableSchedules(start, end);

      res.json({
        success: true,
        message: '获取可用时间段成功',
        data: schedules
      });

    } catch (error) {
      logger.error('Get available schedules error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '获取可用时间段失败'
      });
    }
  }

  /**
   * 获取教练员的时间段
   */
  static async getInstructorSchedules(req: Request, res: Response): Promise<void> {
    try {
      const { instructorId } = req.params;
      const { startDate } = req.query;
      
      const start = startDate ? new Date(startDate as string) : undefined;
      const schedules = await ScheduleService.getInstructorSchedules(instructorId, start);

      res.json({
        success: true,
        message: '获取教练员时间段成功',
        data: schedules
      });

    } catch (error) {
      logger.error('Get instructor schedules error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '获取教练员时间段失败'
      });
    }
  }

  /**
   * 批量更新时间段状态
   */
  static async batchUpdateStatus(req: Request, res: Response): Promise<void> {
    try {
      const { scheduleIds, status } = req.body;

      if (!Array.isArray(scheduleIds) || scheduleIds.length === 0) {
        res.status(400).json({
          success: false,
          message: '请提供有效的时间段ID列表'
        });
        return;
      }

      if (!Object.values(ScheduleStatus).includes(status)) {
        res.status(400).json({
          success: false,
          message: '无效的状态值'
        });
        return;
      }

      await ScheduleService.batchUpdateStatus(scheduleIds, status);

      res.json({
        success: true,
        message: `批量更新${scheduleIds.length}个时间段状态成功`
      });

    } catch (error) {
      logger.error('Batch update status error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '批量更新状态失败'
      });
    }
  }

  /**
   * 获取时间段统计信息
   */
  static async getScheduleStats(req: Request, res: Response): Promise<void> {
    try {
      const { instructorId } = req.query;
      
      const stats = await ScheduleService.getScheduleStats(instructorId as string);

      res.json({
        success: true,
        message: '获取时间段统计成功',
        data: stats
      });

    } catch (error) {
      logger.error('Get schedule stats error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '获取时间段统计失败'
      });
    }
  }
} 