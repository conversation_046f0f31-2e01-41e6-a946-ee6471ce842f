import { Request, Response, NextFunction } from 'express';
import { AuthService, RegisterData, LoginData } from '@/services/authService';
import { User } from '@/models/User';
import { ApiResponse } from '@/utils/types';

export class AuthController {
  /**
   * 用户注册
   */
  static async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const registerData: RegisterData = req.body;
      
      const result = await AuthService.register(registerData);
      
      const response: ApiResponse = {
        success: true,
        data: result,
        message: '注册成功',
        timestamp: new Date().toISOString()
      };

      res.status(201).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 用户登录
   */
  static async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const loginData: LoginData = req.body;
      
      const result = await AuthService.login(loginData);
      
      const response: ApiResponse = {
        success: true,
        data: result,
        message: '登录成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 刷新访问令牌
   */
  static async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refreshToken } = req.body;
      
      if (!refreshToken) {
        const response: ApiResponse = {
          success: false,
          message: '刷新令牌是必需的',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      const result = await AuthService.refreshToken(refreshToken);
      
      const response: ApiResponse = {
        success: true,
        data: result,
        message: '令牌刷新成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 用户退出登录
   */
  static async logout(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { refreshToken } = req.body;
      
      if (!userId) {
        const response: ApiResponse = {
          success: false,
          message: '用户未认证',
          timestamp: new Date().toISOString()
        };
        res.status(401).json(response);
        return;
      }

      await AuthService.logout(userId, refreshToken);
      
      const response: ApiResponse = {
        success: true,
        message: '退出登录成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 验证邮箱
   */
  static async verifyEmail(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { token } = req.params;
      
      if (!token) {
        const response: ApiResponse = {
          success: false,
          message: '验证令牌是必需的',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      await AuthService.verifyEmail(token);
      
      const response: ApiResponse = {
        success: true,
        message: '邮箱验证成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 发送密码重置邮件
   */
  static async forgotPassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { email } = req.body;
      
      if (!email) {
        const response: ApiResponse = {
          success: false,
          message: '邮箱地址是必需的',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      await AuthService.forgotPassword(email);
      
      const response: ApiResponse = {
        success: true,
        message: '如果邮箱存在，密码重置链接已发送',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 重置密码
   */
  static async resetPassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { token } = req.params;
      const { password } = req.body;
      
      if (!token) {
        const response: ApiResponse = {
          success: false,
          message: '重置令牌是必需的',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      if (!password) {
        const response: ApiResponse = {
          success: false,
          message: '新密码是必需的',
          timestamp: new Date().toISOString()
        };
        res.status(400).json(response);
        return;
      }

      await AuthService.resetPassword(token, password);
      
      const response: ApiResponse = {
        success: true,
        message: '密码重置成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 修改密码
   */
  static async changePassword(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const { currentPassword, newPassword } = req.body;
      
      if (!userId) {
        const response: ApiResponse = {
          success: false,
          message: '用户未认证',
          timestamp: new Date().toISOString()
        };
        res.status(401).json(response);
        return;
      }

      await AuthService.changePassword(userId, currentPassword, newPassword);
      
      const response: ApiResponse = {
        success: true,
        message: '密码修改成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取当前用户信息
   */
  static async getProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        const response: ApiResponse = {
          success: false,
          message: '用户未认证',
          timestamp: new Date().toISOString()
        };
        res.status(401).json(response);
        return;
      }

      const user = await AuthService.getUserProfile(userId);
      
      const response: ApiResponse = {
        success: true,
        data: { user },
        message: '获取用户信息成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新用户信息
   */
  static async updateProfile(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      const updateData = req.body;
      
      if (!userId) {
        const response: ApiResponse = {
          success: false,
          message: '用户未认证',
          timestamp: new Date().toISOString()
        };
        res.status(401).json(response);
        return;
      }

      const user = await AuthService.updateUserProfile(userId, updateData);
      
      const response: ApiResponse = {
        success: true,
        data: { user },
        message: '用户信息更新成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除用户账户
   */
  static async deleteAccount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        const response: ApiResponse = {
          success: false,
          message: '用户未认证',
          timestamp: new Date().toISOString()
        };
        res.status(401).json(response);
        return;
      }

      await AuthService.deleteAccount(userId);
      
      const response: ApiResponse = {
        success: true,
        message: '账户删除成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取用户统计信息（仅管理员）
   */
  static async getUserStats(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const totalUsers = await User.countDocuments();
      const activeUsers = await User.countDocuments({ isActive: true });
      const verifiedUsers = await User.countDocuments({ isEmailVerified: true });
      const newUsersToday = await User.countDocuments({
        createdAt: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) }
      });

      const response: ApiResponse = {
        success: true,
        data: {
          totalUsers,
          activeUsers,
          verifiedUsers,
          newUsersToday,
          verificationRate: totalUsers > 0 ? Math.round((verifiedUsers / totalUsers) * 100) : 0
        },
        message: '用户统计信息获取成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
      
    } catch (error) {
      next(error);
    }
  }
} 