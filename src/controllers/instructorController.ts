import { Request, Response, NextFunction } from 'express';
import { InstructorService } from '@/services/instructorService';
import { ApiResponse } from '@/utils/types';

export class InstructorController {
  /**
   * 获取教练员列表
   */
  static async getInstructors(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const result = await InstructorService.getInstructors(req.query as any);

      const response: ApiResponse = {
        success: true,
        data: result,
        message: '获取教练员列表成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取教练员详情
   */
  static async getInstructorById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const instructor = await InstructorService.getInstructorById(id);

      const response: ApiResponse = {
        success: true,
        data: { instructor },
        message: '获取教练员详情成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 根据用户ID获取教练员信息
   */
  static async getInstructorByUserId(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { userId } = req.params;
      const instructor = await InstructorService.getInstructorByUserId(userId);

      if (!instructor) {
        const response: ApiResponse = {
          success: false,
          message: '该用户不是教练员',
          timestamp: new Date().toISOString()
        };
        res.status(404).json(response);
        return;
      }

      const response: ApiResponse = {
        success: true,
        data: { instructor },
        message: '获取教练员信息成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 创建教练员
   */
  static async createInstructor(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const instructor = await InstructorService.createInstructor(req.body);

      const response: ApiResponse = {
        success: true,
        data: { instructor },
        message: '教练员创建成功',
        timestamp: new Date().toISOString()
      };

      res.status(201).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新教练员信息
   */
  static async updateInstructor(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const instructor = await InstructorService.updateInstructor(id, req.body);

      const response: ApiResponse = {
        success: true,
        data: { instructor },
        message: '教练员信息更新成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 删除教练员
   */
  static async deleteInstructor(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      await InstructorService.deleteInstructor(id);

      const response: ApiResponse = {
        success: true,
        message: '教练员删除成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新教练员状态
   */
  static async updateInstructorStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { isActive } = req.body;
      const instructor = await InstructorService.updateInstructorStatus(id, isActive);

      const response: ApiResponse = {
        success: true,
        data: { instructor },
        message: `教练员${isActive ? '激活' : '停用'}成功`,
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 批量操作教练员
   */
  static async batchOperation(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { ids, operation } = req.body;
      const result = await InstructorService.batchOperation(ids, operation);

      const response: ApiResponse = {
        success: true,
        data: result,
        message: `批量${operation}操作完成`,
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取教练员统计信息
   */
  static async getInstructorStats(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const stats = await InstructorService.getInstructorStats();

      const response: ApiResponse = {
        success: true,
        data: stats,
        message: '获取教练员统计信息成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 更新教练员统计信息
   */
  static async updateInstructorStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { newRating, newClassCount } = req.body;
      await InstructorService.updateInstructorStats(id, newRating, newClassCount);

      const response: ApiResponse = {
        success: true,
        message: '教练员统计信息更新成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取活跃教练员列表
   */
  static async getActiveInstructors(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const instructors = await InstructorService.getActiveInstructors();

      const response: ApiResponse = {
        success: true,
        data: { instructors },
        message: '获取活跃教练员列表成功',
        timestamp: new Date().toISOString()
      };

      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  }
} 