import { Request, Response } from 'express';
import { BookingService } from '../services/bookingService';
import { BookingStatus, CancellationReason } from '../models/Booking';
import logger from '../utils/logger';

export class BookingController {
  /**
   * 创建预约
   */
  static async createBooking(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const bookingData = {
        ...req.body,
        userId
      };

      // 检查预约限制
      const limits = await BookingService.checkBookingLimits(userId);
      if (!limits.canBook) {
        res.status(400).json({
          success: false,
          message: limits.reason,
          data: {
            currentActiveBookings: limits.currentActiveBookings,
            maxAllowedBookings: limits.maxAllowedBookings
          }
        });
        return;
      }

      const booking = await BookingService.createBooking(bookingData);

      res.status(201).json({
        success: true,
        message: '预约创建成功',
        data: booking
      });

    } catch (error) {
      logger.error('Create booking error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '创建预约失败'
      });
    }
  }

  /**
   * 获取预约列表
   */
  static async getBookingList(req: Request, res: Response): Promise<void> {
    try {
      const query = req.query;
      
      // 转换查询参数
      const bookingQuery: any = {
        ...query,
        page: query.page ? parseInt(query.page as string) : 1,
        limit: query.limit ? parseInt(query.limit as string) : 20
      };

      // 转换日期参数
      if (query.startDate) {
        bookingQuery.startDate = new Date(query.startDate as string);
      }
      if (query.endDate) {
        bookingQuery.endDate = new Date(query.endDate as string);
      }

      // 如果是普通用户，只能查看自己的预约
      if (req.user!.role === 'user') {
        bookingQuery.userId = req.user!.id;
      }

      const result = await BookingService.getBookingList(bookingQuery);

      res.json({
        success: true,
        message: '获取预约列表成功',
        data: result.bookings,
        pagination: result.pagination
      });

    } catch (error) {
      logger.error('Get booking list error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '获取预约列表失败'
      });
    }
  }

  /**
   * 获取单个预约详情
   */
  static async getBookingById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const booking = await BookingService.getBookingById(id);

      // 权限检查：用户只能查看自己的预约，教练员只能查看自己课程的预约
      if (req.user!.role === 'user' && booking.userId.toString() !== req.user!.id) {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      if (req.user!.role === 'instructor' && booking.instructorId.toString() !== req.user!.id) {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      res.json({
        success: true,
        message: '获取预约详情成功',
        data: booking
      });

    } catch (error) {
      logger.error('Get booking by ID error:', error);
      const status = error instanceof Error && error.message === '预约不存在' ? 404 : 400;
      res.status(status).json({
        success: false,
        message: error instanceof Error ? error.message : '获取预约详情失败'
      });
    }
  }

  /**
   * 更新预约
   */
  static async updateBooking(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // 先获取预约信息进行权限检查
      const existingBooking = await BookingService.getBookingById(id);
      
      // 用户只能修改自己的预约
      if (req.user!.role === 'user' && existingBooking.userId.toString() !== req.user!.id) {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      const booking = await BookingService.updateBooking(id, updateData);

      res.json({
        success: true,
        message: '更新预约成功',
        data: booking
      });

    } catch (error) {
      logger.error('Update booking error:', error);
      const status = error instanceof Error && error.message === '预约不存在' ? 404 : 400;
      res.status(status).json({
        success: false,
        message: error instanceof Error ? error.message : '更新预约失败'
      });
    }
  }

  /**
   * 取消预约
   */
  static async cancelBooking(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { reason, note } = req.body;

      // 先获取预约信息进行权限检查
      const existingBooking = await BookingService.getBookingById(id);
      
      // 用户只能取消自己的预约
      if (req.user!.role === 'user' && existingBooking.userId.toString() !== req.user!.id) {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      // 验证取消原因
      if (!Object.values(CancellationReason).includes(reason)) {
        res.status(400).json({
          success: false,
          message: '无效的取消原因'
        });
        return;
      }

      const booking = await BookingService.cancelBooking(id, reason, note, req.user!.id);

      res.json({
        success: true,
        message: '取消预约成功',
        data: booking
      });

    } catch (error) {
      logger.error('Cancel booking error:', error);
      const status = error instanceof Error && error.message === '预约不存在' ? 404 : 400;
      res.status(status).json({
        success: false,
        message: error instanceof Error ? error.message : '取消预约失败'
      });
    }
  }

  /**
   * 确认预约（教练员操作）
   */
  static async confirmBooking(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const instructorId = req.user!.id;

      // 只有教练员可以确认预约
      if (req.user!.role !== 'instructor') {
        res.status(403).json({
          success: false,
          message: '只有教练员可以确认预约'
        });
        return;
      }

      const booking = await BookingService.confirmBooking(id, instructorId);

      res.json({
        success: true,
        message: '确认预约成功',
        data: booking
      });

    } catch (error) {
      logger.error('Confirm booking error:', error);
      const status = error instanceof Error && error.message === '预约不存在' ? 404 : 400;
      res.status(status).json({
        success: false,
        message: error instanceof Error ? error.message : '确认预约失败'
      });
    }
  }

  /**
   * 标记出席（教练员操作）
   */
  static async markAttendance(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { attended } = req.body;
      const instructorId = req.user!.id;

      // 只有教练员可以标记出席
      if (req.user!.role !== 'instructor') {
        res.status(403).json({
          success: false,
          message: '只有教练员可以标记出席'
        });
        return;
      }

      if (typeof attended !== 'boolean') {
        res.status(400).json({
          success: false,
          message: '请提供有效的出席状态'
        });
        return;
      }

      const booking = await BookingService.markAttendance(id, instructorId, attended);

      res.json({
        success: true,
        message: `标记${attended ? '出席' : '未到场'}成功`,
        data: booking
      });

    } catch (error) {
      logger.error('Mark attendance error:', error);
      const status = error instanceof Error && error.message === '预约不存在' ? 404 : 400;
      res.status(status).json({
        success: false,
        message: error instanceof Error ? error.message : '标记出席失败'
      });
    }
  }

  /**
   * 添加评价
   */
  static async addRating(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { score, comment } = req.body;
      const userId = req.user!.id;

      // 验证评分
      if (!score || score < 1 || score > 5 || !Number.isInteger(score)) {
        res.status(400).json({
          success: false,
          message: '评分必须是1-5之间的整数'
        });
        return;
      }

      const booking = await BookingService.addRating(id, userId, score, comment);

      res.json({
        success: true,
        message: '添加评价成功',
        data: booking
      });

    } catch (error) {
      logger.error('Add rating error:', error);
      const status = error instanceof Error && error.message === '预约不存在' ? 404 : 400;
      res.status(status).json({
        success: false,
        message: error instanceof Error ? error.message : '添加评价失败'
      });
    }
  }

  /**
   * 获取用户预约历史
   */
  static async getUserBookings(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.params.userId || req.user!.id;
      const { status } = req.query;

      // 权限检查：用户只能查看自己的预约历史
      if (req.user!.role === 'user' && userId !== req.user!.id) {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      const bookings = await BookingService.getUserBookings(
        userId, 
        status as BookingStatus
      );

      res.json({
        success: true,
        message: '获取用户预约历史成功',
        data: bookings
      });

    } catch (error) {
      logger.error('Get user bookings error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '获取用户预约历史失败'
      });
    }
  }

  /**
   * 获取教练员预约
   */
  static async getInstructorBookings(req: Request, res: Response): Promise<void> {
    try {
      const instructorId = req.params.instructorId || req.user!.id;
      const { status } = req.query;

      // 权限检查：教练员只能查看自己的预约
      if (req.user!.role === 'instructor' && instructorId !== req.user!.id) {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      const bookings = await BookingService.getInstructorBookings(
        instructorId, 
        status as BookingStatus
      );

      res.json({
        success: true,
        message: '获取教练员预约成功',
        data: bookings
      });

    } catch (error) {
      logger.error('Get instructor bookings error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '获取教练员预约失败'
      });
    }
  }

  /**
   * 获取预约统计信息
   */
  static async getBookingStats(req: Request, res: Response): Promise<void> {
    try {
      const { instructorId, userId } = req.query;
      
      // 权限检查
      if (req.user!.role === 'user' && userId && userId !== req.user!.id) {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      if (req.user!.role === 'instructor' && instructorId && instructorId !== req.user!.id) {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      // 如果是普通用户，只能查看自己的统计
      const finalUserId = req.user!.role === 'user' ? req.user!.id : (userId as string);
      const finalInstructorId = req.user!.role === 'instructor' ? req.user!.id : (instructorId as string);

      const stats = await BookingService.getBookingStats(finalInstructorId, finalUserId);

      res.json({
        success: true,
        message: '获取预约统计成功',
        data: stats
      });

    } catch (error) {
      logger.error('Get booking stats error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '获取预约统计失败'
      });
    }
  }

  /**
   * 批量更新预约状态（管理员操作）
   */
  static async batchUpdateStatus(req: Request, res: Response): Promise<void> {
    try {
      const { bookingIds, status } = req.body;

      // 只有管理员可以批量更新
      if (req.user!.role !== 'admin') {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      if (!Array.isArray(bookingIds) || bookingIds.length === 0) {
        res.status(400).json({
          success: false,
          message: '请提供有效的预约ID列表'
        });
        return;
      }

      if (!Object.values(BookingStatus).includes(status)) {
        res.status(400).json({
          success: false,
          message: '无效的状态值'
        });
        return;
      }

      await BookingService.batchUpdateStatus(bookingIds, status);

      res.json({
        success: true,
        message: `批量更新${bookingIds.length}个预约状态成功`
      });

    } catch (error) {
      logger.error('Batch update status error:', error);
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : '批量更新状态失败'
      });
    }
  }
} 