import mongoose, { Document, Schema } from 'mongoose';

// 课程难度枚举
export enum CourseDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate', 
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

// 课程类型枚举
export enum CourseType {
  HATHA = 'hatha',           // 哈达瑜伽
  VINYASA = 'vinyasa',       // 流瑜伽
  YIN = 'yin',               // 阴瑜伽
  ASHTANGA = 'ashtanga',     // 阿斯汤加瑜伽
  POWER = 'power',           // 力量瑜伽
  RESTORATIVE = 'restorative', // 修复瑜伽
  HOT = 'hot',               // 热瑜伽
  MEDITATION = 'meditation',  // 冥想
  PRANAYAMA = 'pranayama'    // 呼吸法
}

// 课程状态枚举
export enum CourseStatus {
  DRAFT = 'draft',           // 草稿
  PUBLISHED = 'published',   // 已发布
  CANCELLED = 'cancelled',   // 已取消
  COMPLETED = 'completed'    // 已完成
}

// 课程时间安排接口
export interface CourseSchedule {
  startTime: Date;
  endTime: Date;
  duration: number; // 课程时长（分钟）
  capacity: number; // 最大容量
  enrolledCount: number; // 已报名人数
  isRecurring: boolean; // 是否重复课程
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number; // 间隔
    daysOfWeek?: number[]; // 星期几（0-6，0为周日）
    endDate?: Date; // 重复结束日期
  };
}

// 课程内容接口
export interface CourseContent {
  description: string; // 课程描述
  outline: string[]; // 课程大纲
  benefits: string[]; // 课程益处
  requirements: string[]; // 参与要求
  equipment: string[]; // 所需设备
  contraindications: string[]; // 禁忌症
}

// 课程媒体接口
export interface CourseMedia {
  images: string[]; // 课程图片
  videos: string[]; // 课程视频
  thumbnail: string; // 缩略图
}

// 课程价格接口
export interface CoursePrice {
  basePrice: number; // 基础价格
  currency: string; // 货币
  discountPrice?: number; // 折扣价格
  discountEndDate?: Date; // 折扣结束日期
  isPackage: boolean; // 是否套餐
  packageDetails?: {
    sessionsCount: number; // 课程节数
    validityDays: number; // 有效期（天）
  };
}

// 课程文档接口
export interface ICourse extends Document {
  // 基本信息
  title: string;
  slug: string; // URL友好的标识符
  type: CourseType;
  difficulty: CourseDifficulty;
  status: CourseStatus;
  
  // 教练员信息
  instructorId: mongoose.Types.ObjectId;
  
  // 课程内容
  content: CourseContent;
  
  // 时间安排
  schedule: CourseSchedule;
  
  // 媒体资源
  media: CourseMedia;
  
  // 价格信息
  price: CoursePrice;
  
  // 位置信息
  location: {
    type: 'online' | 'offline' | 'hybrid';
    address?: string;
    room?: string;
    platform?: string; // 在线平台
    meetingLink?: string; // 会议链接
  };
  
  // 统计信息
  stats: {
    totalEnrollments: number; // 总报名数
    completedSessions: number; // 已完成课程数
    averageRating: number; // 平均评分
    totalReviews: number; // 评价总数
    viewCount: number; // 浏览量
  };
  
  // 设置
  isActive: boolean;
  isFeatured: boolean; // 是否推荐
  allowWaitlist: boolean; // 是否允许候补
  
  // 系统字段
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  
  // 实例方法
  updateStats(newRating?: number, newEnrollment?: boolean): Promise<void>;
  isAvailable(): boolean;
  getAvailableSlots(): number;
}

// 课程schema定义
const courseSchema = new Schema<ICourse>({
  // 基本信息
  title: {
    type: String,
    required: [true, '课程标题是必需的'],
    trim: true,
    maxlength: [200, '课程标题最多200个字符']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-]+$/, 'slug只能包含小写字母、数字和连字符']
  },
  type: {
    type: String,
    enum: Object.values(CourseType),
    required: [true, '课程类型是必需的']
  },
  difficulty: {
    type: String,
    enum: Object.values(CourseDifficulty),
    required: [true, '课程难度是必需的']
  },
  status: {
    type: String,
    enum: Object.values(CourseStatus),
    default: CourseStatus.DRAFT
  },
  
  // 教练员信息
  instructorId: {
    type: Schema.Types.ObjectId,
    ref: 'Instructor',
    required: [true, '教练员ID是必需的'],
    index: true
  },
  
  // 课程内容
  content: {
    description: {
      type: String,
      required: [true, '课程描述是必需的'],
      maxlength: [2000, '课程描述最多2000个字符']
    },
    outline: [{
      type: String,
      maxlength: [500, '大纲项目最多500个字符']
    }],
    benefits: [{
      type: String,
      maxlength: [200, '益处描述最多200个字符']
    }],
    requirements: [{
      type: String,
      maxlength: [200, '要求描述最多200个字符']
    }],
    equipment: [{
      type: String,
      maxlength: [100, '设备名称最多100个字符']
    }],
    contraindications: [{
      type: String,
      maxlength: [200, '禁忌症描述最多200个字符']
    }]
  },
  
  // 时间安排
  schedule: {
    startTime: {
      type: Date,
      required: [true, '课程开始时间是必需的']
    },
    endTime: {
      type: Date,
      required: [true, '课程结束时间是必需的']
    },
    duration: {
      type: Number,
      required: [true, '课程时长是必需的'],
      min: [15, '课程时长不能少于15分钟'],
      max: [480, '课程时长不能超过8小时']
    },
    capacity: {
      type: Number,
      required: [true, '课程容量是必需的'],
      min: [1, '课程容量至少为1'],
      max: [100, '课程容量不能超过100']
    },
    enrolledCount: {
      type: Number,
      default: 0,
      min: 0
    },
    isRecurring: {
      type: Boolean,
      default: false
    },
    recurringPattern: {
      frequency: {
        type: String,
        enum: ['daily', 'weekly', 'monthly']
      },
      interval: {
        type: Number,
        min: 1
      },
      daysOfWeek: [{
        type: Number,
        min: 0,
        max: 6
      }],
      endDate: Date
    }
  },
  
  // 媒体资源
  media: {
    images: [{
      type: String,
      validate: {
        validator: function(v: string) {
          return /^(https?:\/\/|\/uploads\/)/.test(v);
        },
        message: '图片URL格式无效'
      }
    }],
    videos: [{
      type: String,
      validate: {
        validator: function(v: string) {
          return /^(https?:\/\/|\/uploads\/)/.test(v);
        },
        message: '视频URL格式无效'
      }
    }],
    thumbnail: {
      type: String,
      validate: {
        validator: function(v: string) {
          return !v || /^(https?:\/\/|\/uploads\/)/.test(v);
        },
        message: '缩略图URL格式无效'
      }
    }
  },
  
  // 价格信息
  price: {
    basePrice: {
      type: Number,
      required: [true, '课程价格是必需的'],
      min: [0, '价格不能为负数']
    },
    currency: {
      type: String,
      default: 'CNY',
      enum: ['CNY', 'USD', 'EUR']
    },
    discountPrice: {
      type: Number,
      min: 0,
      validate: {
        validator: function(this: ICourse, v: number) {
          return !v || v < this.price.basePrice;
        },
        message: '折扣价格必须低于基础价格'
      }
    },
    discountEndDate: Date,
    isPackage: {
      type: Boolean,
      default: false
    },
    packageDetails: {
      sessionsCount: {
        type: Number,
        min: 1
      },
      validityDays: {
        type: Number,
        min: 1
      }
    }
  },
  
  // 位置信息
  location: {
    type: {
      type: String,
      enum: ['online', 'offline', 'hybrid'],
      required: [true, '位置类型是必需的']
    },
    address: {
      type: String,
      maxlength: [500, '地址最多500个字符']
    },
    room: {
      type: String,
      maxlength: [100, '房间号最多100个字符']
    },
    platform: {
      type: String,
      maxlength: [100, '平台名称最多100个字符']
    },
    meetingLink: {
      type: String,
      validate: {
        validator: function(v: string) {
          return !v || /^https?:\/\//.test(v);
        },
        message: '会议链接必须是有效的URL'
      }
    }
  },
  
  // 统计信息
  stats: {
    totalEnrollments: { type: Number, default: 0 },
    completedSessions: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0, min: 0, max: 5 },
    totalReviews: { type: Number, default: 0 },
    viewCount: { type: Number, default: 0 }
  },
  
  // 设置
  isActive: { type: Boolean, default: true },
  isFeatured: { type: Boolean, default: false },
  allowWaitlist: { type: Boolean, default: true },
  
  // 系统字段
  deletedAt: { type: Date, default: null }
}, {
  timestamps: true,
  toJSON: {
    transform: function(_doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 索引
courseSchema.index({ instructorId: 1 });
courseSchema.index({ type: 1 });
courseSchema.index({ difficulty: 1 });
courseSchema.index({ status: 1 });
courseSchema.index({ 'schedule.startTime': 1 });
courseSchema.index({ 'price.basePrice': 1 });
courseSchema.index({ 'stats.averageRating': -1 });
courseSchema.index({ isFeatured: 1 });
courseSchema.index({ isActive: 1 });
courseSchema.index({ createdAt: -1 });

// 复合索引
courseSchema.index({ type: 1, difficulty: 1, 'schedule.startTime': 1 });
courseSchema.index({ instructorId: 1, status: 1 });

// 软删除查询中间件
courseSchema.pre(/^find/, function(this: any, next) {
  this.where({ deletedAt: null });
  next();
});

// 验证课程时间逻辑
courseSchema.pre('save', function(next) {
  if (this.schedule.endTime <= this.schedule.startTime) {
    next(new Error('课程结束时间必须晚于开始时间'));
  }
  
  if (this.schedule.enrolledCount > this.schedule.capacity) {
    next(new Error('报名人数不能超过课程容量'));
  }
  
  next();
});

// 实例方法：更新统计信息
courseSchema.methods.updateStats = async function(
  newRating?: number,
  newEnrollment?: boolean
): Promise<void> {
  if (newRating !== undefined) {
    const totalRatingPoints = this.stats.averageRating * this.stats.totalReviews + newRating;
    this.stats.totalReviews += 1;
    this.stats.averageRating = totalRatingPoints / this.stats.totalReviews;
  }
  
  if (newEnrollment) {
    this.stats.totalEnrollments += 1;
  }
  
  await this.save();
};

// 实例方法：检查课程是否可预订
courseSchema.methods.isAvailable = function(): boolean {
  return this.isActive && 
         this.status === CourseStatus.PUBLISHED && 
         this.schedule.enrolledCount < this.schedule.capacity &&
         this.schedule.startTime > new Date();
};

// 实例方法：获取可用名额
courseSchema.methods.getAvailableSlots = function(): number {
  return Math.max(0, this.schedule.capacity - this.schedule.enrolledCount);
};

// 静态方法：查找可预订的课程
courseSchema.statics.findAvailable = function() {
  return this.find({
    isActive: true,
    status: CourseStatus.PUBLISHED,
    'schedule.startTime': { $gt: new Date() }
  }).populate('instructorId', 'displayName profileImage specialties averageRating');
};

// 静态方法：按教练查找课程
courseSchema.statics.findByInstructor = function(instructorId: string) {
  return this.find({ instructorId }).populate('instructorId', 'displayName profileImage');
};

export const Course = mongoose.model<ICourse>('Course', courseSchema); 