import mongoose, { Document, Schema } from 'mongoose';

// 教练员接口
export interface IInstructor extends Document {
  // 关联用户
  userId: mongoose.Types.ObjectId;
  
  // 基本信息
  displayName: string; // 教练显示名称
  bio?: string; // 个人简介
  experience?: number; // 教学经验（年）
  
  // 专业技能（简化）
  specialties: string[]; // 专长领域，如["hatha", "vinyasa", "yin"]
  
  // 媒体资源
  profileImage?: string; // 头像
  
  // 统计信息
  totalClasses: number; // 总课程数
  averageRating: number; // 平均评分
  totalReviews: number; // 评价总数
  
  // 设置
  isActive: boolean; // 是否启用
  
  // 系统字段
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

// 教练员schema
const instructorSchema = new Schema<IInstructor>({
  // 关联用户
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  
  // 基本信息
  displayName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  bio: {
    type: String,
    maxlength: 500
  },
  experience: {
    type: Number,
    min: 0,
    default: 0
  },
  
  // 专业技能
  specialties: {
    type: [String],
    default: []
  },
  
  // 媒体资源
  profileImage: String,
  
  // 统计信息
  totalClasses: { type: Number, default: 0 },
  averageRating: { type: Number, default: 0, min: 0, max: 5 },
  totalReviews: { type: Number, default: 0 },
  
  // 设置
  isActive: { type: Boolean, default: true },
  
  // 系统字段
  deletedAt: { type: Date, default: null }
}, {
  timestamps: true,
  toJSON: {
    transform: function(_doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// 索引
instructorSchema.index({ userId: 1 });
instructorSchema.index({ displayName: 1 });
instructorSchema.index({ specialties: 1 });
instructorSchema.index({ averageRating: -1 });
instructorSchema.index({ isActive: 1 });
instructorSchema.index({ createdAt: -1 });

// 软删除查询中间件
instructorSchema.pre(/^find/, function(this: any, next) {
  this.where({ deletedAt: null });
  next();
});

// 实例方法：更新统计信息
instructorSchema.methods.updateStats = async function(
  newRating?: number,
  newClassCount?: number
) {
  if (newRating !== undefined) {
    // 更新评分
    const totalRatingPoints = this.averageRating * this.totalReviews + newRating;
    this.totalReviews += 1;
    this.averageRating = totalRatingPoints / this.totalReviews;
  }
  
  if (newClassCount !== undefined) {
    this.totalClasses = newClassCount;
  }
  
  await this.save();
};

// 静态方法：查找活跃教练员
instructorSchema.statics.findActive = function() {
  return this.find({ isActive: true }).populate('userId', 'email username avatar');
};

export const Instructor = mongoose.model<IInstructor>('Instructor', instructorSchema); 