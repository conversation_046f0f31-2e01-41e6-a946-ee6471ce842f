import mongoose, { Schema, Document, Types } from 'mongoose';

// 时间段状态枚举
export enum ScheduleStatus {
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress', 
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// 课程时间段接口
export interface ICourseSchedule extends Document {
  courseId: Types.ObjectId;
  instructorId: Types.ObjectId;
  startTime: Date;
  endTime: Date;
  maxCapacity: number;
  currentBookings: number;
  status: ScheduleStatus;
  location: string;
  description?: string;
  specialInstructions?: string;
  isRecurring: boolean;
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number; // 每隔几天/周/月
    daysOfWeek?: number[]; // 周几 (0=周日, 1=周一, ...)
    endDate?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  
  // 实例方法
  isAvailable(): boolean;
  getAvailableSpots(): number;
  canBook(): boolean;
  incrementBookings(): Promise<ICourseSchedule>;
  decrementBookings(): Promise<ICourseSchedule>;
}

// 课程时间段Schema
const courseScheduleSchema = new Schema<ICourseSchedule>({
  courseId: {
    type: Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, '课程ID是必需的'],
    index: true
  },
  instructorId: {
    type: Schema.Types.ObjectId,
    ref: 'Instructor', 
    required: [true, '教练员ID是必需的'],
    index: true
  },
  startTime: {
    type: Date,
    required: [true, '开始时间是必需的'],
    index: true
  },
  endTime: {
    type: Date,
    required: [true, '结束时间是必需的'],
    validate: {
      validator: function(this: ICourseSchedule, endTime: Date) {
        return endTime > this.startTime;
      },
      message: '结束时间必须晚于开始时间'
    }
  },
  maxCapacity: {
    type: Number,
    required: [true, '最大容量是必需的'],
    min: [1, '最大容量必须至少为1'],
    max: [100, '最大容量不能超过100']
  },
  currentBookings: {
    type: Number,
    default: 0,
    min: [0, '当前预约数不能为负数'],
    validate: {
      validator: function(this: ICourseSchedule, currentBookings: number) {
        return currentBookings <= this.maxCapacity;
      },
      message: '当前预约数不能超过最大容量'
    }
  },
  status: {
    type: String,
    enum: Object.values(ScheduleStatus),
    default: ScheduleStatus.SCHEDULED,
    index: true
  },
  location: {
    type: String,
    required: [true, '地点是必需的'],
    trim: true,
    maxlength: [200, '地点描述不能超过200个字符']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, '描述不能超过1000个字符']
  },
  specialInstructions: {
    type: String,
    trim: true,
    maxlength: [500, '特殊说明不能超过500个字符']
  },
  isRecurring: {
    type: Boolean,
    default: false
  },
  recurringPattern: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly']
    },
    interval: {
      type: Number,
      min: 1,
      max: 52
    },
    daysOfWeek: [{
      type: Number,
      min: 0,
      max: 6
    }],
    endDate: Date
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(_doc, ret) {
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// 复合索引
courseScheduleSchema.index({ courseId: 1, startTime: 1 });
courseScheduleSchema.index({ instructorId: 1, startTime: 1 });
courseScheduleSchema.index({ startTime: 1, endTime: 1 });
courseScheduleSchema.index({ status: 1, startTime: 1 });

// 虚拟字段
courseScheduleSchema.virtual('availableSpots').get(function() {
  return this.maxCapacity - this.currentBookings;
});

courseScheduleSchema.virtual('duration').get(function() {
  return this.endTime.getTime() - this.startTime.getTime();
});

courseScheduleSchema.virtual('course', {
  ref: 'Course',
  localField: 'courseId', 
  foreignField: '_id',
  justOne: true
});

courseScheduleSchema.virtual('instructor', {
  ref: 'Instructor',
  localField: 'instructorId',
  foreignField: '_id', 
  justOne: true
});

// 实例方法
courseScheduleSchema.methods.isAvailable = function(): boolean {
  return this.status === ScheduleStatus.SCHEDULED && 
         this.currentBookings < this.maxCapacity &&
         this.startTime > new Date();
};

courseScheduleSchema.methods.getAvailableSpots = function(): number {
  return this.maxCapacity - this.currentBookings;
};

courseScheduleSchema.methods.canBook = function(): boolean {
  return this.isAvailable() && this.getAvailableSpots() > 0;
};

courseScheduleSchema.methods.incrementBookings = async function(): Promise<ICourseSchedule> {
  if (this.currentBookings >= this.maxCapacity) {
    throw new Error('课程已满，无法增加预约');
  }
  this.currentBookings += 1;
  return await this.save();
};

courseScheduleSchema.methods.decrementBookings = async function(): Promise<ICourseSchedule> {
  if (this.currentBookings <= 0) {
    throw new Error('当前预约数已为0，无法减少');
  }
  this.currentBookings -= 1;
  return await this.save();
};

// 静态方法
courseScheduleSchema.statics.findAvailable = function(startDate?: Date, endDate?: Date) {
  const query: any = {
    status: ScheduleStatus.SCHEDULED,
    startTime: { $gt: new Date() },
    $expr: { $lt: ['$currentBookings', '$maxCapacity'] }
  };
  
  if (startDate) {
    query.startTime.$gte = startDate;
  }
  if (endDate) {
    query.endTime = { $lte: endDate };
  }
  
  return this.find(query).populate('course instructor');
};

courseScheduleSchema.statics.findByInstructor = function(instructorId: string, startDate?: Date) {
  const query: any = { instructorId };
  if (startDate) {
    query.startTime = { $gte: startDate };
  }
  return this.find(query).populate('course').sort({ startTime: 1 });
};

// 预保存中间件
courseScheduleSchema.pre('save', function(next) {
  // 验证时间冲突
  if (this.isModified('startTime') || this.isModified('endTime')) {
    if (this.endTime <= this.startTime) {
      next(new Error('结束时间必须晚于开始时间'));
      return;
    }
  }
  next();
});

// 预删除中间件
courseScheduleSchema.pre('deleteOne', { document: true, query: false }, async function(next) {
  // 检查是否有预约
  const Booking = mongoose.model('Booking');
  const bookingCount = await Booking.countDocuments({ 
    scheduleId: this._id,
    status: { $in: ['pending', 'confirmed'] }
  });
  
  if (bookingCount > 0) {
    next(new Error('存在有效预约，无法删除课程时间段'));
    return;
  }
  next();
});

const CourseSchedule = mongoose.model<ICourseSchedule>('CourseSchedule', courseScheduleSchema);

export default CourseSchedule; 