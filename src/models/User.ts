import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';
import { UserRole } from '@/utils/types';

// 用户文档接口
export interface IUser extends Document {
  // 基本信息
  email: string;
  password: string;
  username: string;
  displayName?: string;
  avatar?: string;
  
  // 角色和权限
  role: UserRole;
  permissions?: string[];
  
  // 个人信息
  firstName?: string;
  lastName?: string;
  gender?: 'male' | 'female' | 'other';
  dateOfBirth?: Date;
  phone?: string;
  
  // 地址信息
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  
  // 瑜伽相关信息
  yogaExperience?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  preferredStyles?: string[];
  healthConditions?: string[];
  goals?: string[];
  
  // 账户状态
  isActive: boolean;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  
  // 验证相关
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  phoneVerificationCode?: string;
  phoneVerificationExpires?: Date;
  
  // 登录相关
  lastLoginAt?: Date;
  loginAttempts: number;
  lockUntil?: Date;
  refreshTokens?: string[];
  
  // 订阅和偏好
  notificationPreferences?: {
    email?: boolean;
    sms?: boolean;
    push?: boolean;
    marketing?: boolean;
  };
  language?: string;
  timezone?: string;
  
  // 系统字段
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  
  // 实例方法
  comparePassword(password: string): Promise<boolean>;
  isLocked(): boolean;
  generateEmailVerificationToken(): string;
  generatePasswordResetToken(): string;
  generatePhoneVerificationCode(): string;
  addRefreshToken(token: string): void;
  removeRefreshToken(token: string): void;
  clearAllRefreshTokens(): void;
}

// 用户schema定义
const userSchema = new Schema<IUser>({
  // 基本信息
  email: {
    type: String,
    required: [true, '邮箱是必需的'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, '请输入有效的邮箱地址']
  },
  password: {
    type: String,
    required: [true, '密码是必需的'],
    minlength: [8, '密码至少需要8个字符'],
    select: false // 默认查询时不返回密码
  },
  username: {
    type: String,
    required: [true, '用户名是必需的'],
    unique: true,
    trim: true,
    minlength: [3, '用户名至少需要3个字符'],
    maxlength: [30, '用户名最多30个字符'],
    match: [/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线']
  },
  displayName: {
    type: String,
    trim: true,
    maxlength: [50, '显示名称最多50个字符']
  },
  avatar: {
    type: String,
    default: null
  },
  
  // 角色和权限
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.USER
  },
  permissions: [{
    type: String
  }],
  
  // 个人信息
  firstName: {
    type: String,
    trim: true,
    maxlength: [50, '名字最多50个字符']
  },
  lastName: {
    type: String,
    trim: true,
    maxlength: [50, '姓氏最多50个字符']
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other'],
    default: null
  },
  dateOfBirth: {
    type: Date,
    validate: {
      validator: function(value: Date) {
        return !value || value < new Date();
      },
      message: '出生日期不能是未来时间'
    }
  },
  phone: {
    type: String,
    trim: true,
    sparse: true, // 允许多个null值
    match: [/^[+]?[1-9]\d{1,14}$/, '请输入有效的手机号码']
  },
  
  // 地址信息
  address: {
    street: { type: String, trim: true },
    city: { type: String, trim: true },
    state: { type: String, trim: true },
    zipCode: { type: String, trim: true },
    country: { type: String, trim: true }
  },
  
  // 瑜伽相关信息
  yogaExperience: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced', 'expert'],
    default: 'beginner'
  },
  preferredStyles: [{
    type: String,
    trim: true
  }],
  healthConditions: [{
    type: String,
    trim: true
  }],
  goals: [{
    type: String,
    trim: true
  }],
  
  // 账户状态
  isActive: {
    type: Boolean,
    default: true
  },
  isEmailVerified: {
    type: Boolean,
    default: false
  },
  isPhoneVerified: {
    type: Boolean,
    default: false
  },
  
  // 验证相关
  emailVerificationToken: {
    type: String,
    select: false
  },
  emailVerificationExpires: {
    type: Date,
    select: false
  },
  passwordResetToken: {
    type: String,
    select: false
  },
  passwordResetExpires: {
    type: Date,
    select: false
  },
  phoneVerificationCode: {
    type: String,
    select: false
  },
  phoneVerificationExpires: {
    type: Date,
    select: false
  },
  
  // 登录相关
  lastLoginAt: {
    type: Date,
    default: null
  },
  loginAttempts: {
    type: Number,
    default: 0
  },
  lockUntil: {
    type: Date,
    select: false
  },
  refreshTokens: [{
    type: String,
    select: false
  }],
  
  // 订阅和偏好
  notificationPreferences: {
    email: { type: Boolean, default: true },
    sms: { type: Boolean, default: false },
    push: { type: Boolean, default: true },
    marketing: { type: Boolean, default: false }
  },
  language: {
    type: String,
    default: 'zh-CN'
  },
  timezone: {
    type: String,
    default: 'Asia/Shanghai'
  },
  
  // 系统字段
  deletedAt: {
    type: Date,
    default: null,
    select: false
  }
}, {
  timestamps: true, // 自动添加createdAt和updatedAt
  toJSON: {
    transform: function(_doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      delete ret.refreshTokens;
      delete ret.emailVerificationToken;
      delete ret.passwordResetToken;
      delete ret.phoneVerificationCode;
      delete ret.lockUntil;
      return ret;
    }
  }
});

// 索引
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ phone: 1 }, { sparse: true });
userSchema.index({ role: 1 });
userSchema.index({ createdAt: -1 });
userSchema.index({ isActive: 1, deletedAt: 1 });

// 密码加密中间件
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// 软删除查询中间件
userSchema.pre(/^find/, function(this: any, next) {
  // 默认不查询已删除的用户 (deletedAt为null或不存在)
  this.where({ deletedAt: null });
  next();
});

// 实例方法：比较密码
userSchema.methods.comparePassword = async function(password: string): Promise<boolean> {
  return bcrypt.compare(password, this.password);
};

// 实例方法：检查账户是否被锁定
userSchema.methods.isLocked = function(): boolean {
  return !!(this.lockUntil && this.lockUntil > Date.now());
};

// 实例方法：生成邮箱验证token
userSchema.methods.generateEmailVerificationToken = function(): string {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  
  this.emailVerificationToken = token;
  this.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时过期
  
  return token;
};

// 实例方法：生成密码重置token
userSchema.methods.generatePasswordResetToken = function(): string {
  const crypto = require('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  
  this.passwordResetToken = token;
  this.passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1小时过期
  
  return token;
};

// 实例方法：生成手机验证码
userSchema.methods.generatePhoneVerificationCode = function(): string {
  const code = Math.floor(100000 + Math.random() * 900000).toString(); // 6位数字
  
  this.phoneVerificationCode = code;
  this.phoneVerificationExpires = new Date(Date.now() + 15 * 60 * 1000); // 15分钟过期
  
  return code;
};

// 实例方法：添加刷新token
userSchema.methods.addRefreshToken = function(token: string): void {
  if (!this.refreshTokens) {
    this.refreshTokens = [];
  }
  this.refreshTokens.push(token);
  
  // 限制刷新token数量（最多5个）
  if (this.refreshTokens.length > 5) {
    this.refreshTokens = this.refreshTokens.slice(-5);
  }
};

// 实例方法：移除刷新token
userSchema.methods.removeRefreshToken = function(token: string): void {
  if (this.refreshTokens) {
    this.refreshTokens = this.refreshTokens.filter((t: string) => t !== token);
  }
};

// 实例方法：清除所有刷新token
userSchema.methods.clearAllRefreshTokens = function(): void {
  this.refreshTokens = [];
};

// 静态方法：根据邮箱或用户名查找用户
userSchema.statics.findByEmailOrUsername = function(identifier: string) {
  return this.findOne({
    $or: [
      { email: identifier.toLowerCase() },
      { username: identifier }
    ]
  }).select('+password');
};

// 静态方法：增加登录尝试次数
userSchema.statics.handleFailedLogin = async function(userId: string) {
  const updates: any = { $inc: { loginAttempts: 1 } };
  
  const user = await this.findById(userId);
  if (user && user.loginAttempts >= 4) { // 5次失败后锁定账户
    updates.$set = {
      lockUntil: Date.now() + 30 * 60 * 1000 // 锁定30分钟
    };
  }
  
  return this.findByIdAndUpdate(userId, updates);
};

// 静态方法：重置登录尝试
userSchema.statics.resetLoginAttempts = function(userId: string) {
  return this.findByIdAndUpdate(userId, {
    $unset: { loginAttempts: 1, lockUntil: 1 },
    $set: { lastLoginAt: new Date() }
  });
};

export const User = mongoose.model<IUser>('User', userSchema); 