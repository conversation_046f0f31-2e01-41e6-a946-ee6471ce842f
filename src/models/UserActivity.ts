import mongoose, { Document, Schema } from 'mongoose';

// 用户活动类型枚举
export enum ActivityType {
  // 账户相关
  REGISTER = 'register',
  LOGIN = 'login',
  LOGOUT = 'logout',
  PASSWORD_CHANGE = 'password_change',
  EMAIL_VERIFY = 'email_verify',
  PHONE_VERIFY = 'phone_verify',
  
  // 个人资料相关
  PROFILE_UPDATE = 'profile_update',
  AVATAR_UPLOAD = 'avatar_upload',
  PREFERENCES_UPDATE = 'preferences_update',
  
  // 课程相关
  COURSE_VIEW = 'course_view',
  COURSE_BOOK = 'course_book',
  COURSE_CANCEL = 'course_cancel',
  COURSE_COMPLETE = 'course_complete',
  
  // 教练相关
  INSTRUCTOR_VIEW = 'instructor_view',
  INSTRUCTOR_FOLLOW = 'instructor_follow',
  INSTRUCTOR_UNFOLLOW = 'instructor_unfollow',
  
  // 评价相关
  REVIEW_CREATE = 'review_create',
  REVIEW_UPDATE = 'review_update',
  REVIEW_DELETE = 'review_delete',
  
  // 支付相关
  PAYMENT_SUCCESS = 'payment_success',
  PAYMENT_FAILED = 'payment_failed',
  REFUND_REQUEST = 'refund_request',
  
  // 其他
  OTHER = 'other'
}

// 用户活动文档接口
export interface IUserActivity extends Document {
  userId: mongoose.Types.ObjectId;
  type: ActivityType;
  title: string;
  description?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

// 用户活动模型接口（包含静态方法）
export interface IUserActivityModel extends mongoose.Model<IUserActivity> {
  createActivity(
    userId: string | mongoose.Types.ObjectId,
    type: ActivityType,
    title: string,
    options?: {
      description?: string;
      metadata?: Record<string, any>;
      ipAddress?: string;
      userAgent?: string;
    }
  ): Promise<IUserActivity>;

  getUserActivities(
    userId: string | mongoose.Types.ObjectId,
    options?: {
      type?: ActivityType;
      page?: number;
      limit?: number;
      startDate?: Date;
      endDate?: Date;
    }
  ): Promise<{
    data: IUserActivity[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }>;
}

// 用户活动schema定义
const userActivitySchema = new Schema<IUserActivity>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '用户ID是必需的'],
    index: true
  },
  type: {
    type: String,
    enum: Object.values(ActivityType),
    required: [true, '活动类型是必需的'],
    index: true
  },
  title: {
    type: String,
    required: [true, '活动标题是必需的'],
    trim: true,
    maxlength: [200, '活动标题最多200个字符']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [1000, '活动描述最多1000个字符']
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  },
  ipAddress: {
    type: String,
    trim: true,
    validate: {
      validator: function(v: string) {
        if (!v) return true; // 允许空值
        // IPv4 or IPv6 or localhost
        const ipv4Regex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        const ipv6Regex = /^(?:[a-fA-F0-9]{1,4}:){7}[a-fA-F0-9]{1,4}$/;
        const localhostRegex = /^::1$|^127\.0\.0\.1$/;
        return ipv4Regex.test(v) || ipv6Regex.test(v) || localhostRegex.test(v) || v === 'localhost';
      },
      message: '请输入有效的IP地址'
    }
  },
  userAgent: {
    type: String,
    trim: true,
    maxlength: [500, '用户代理最多500个字符']
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  }
});

// 复合索引
userActivitySchema.index({ userId: 1, createdAt: -1 });
userActivitySchema.index({ userId: 1, type: 1, createdAt: -1 });

// 静态方法：创建活动记录
userActivitySchema.statics.createActivity = async function(
  userId: string | mongoose.Types.ObjectId,
  type: ActivityType,
  title: string,
  options: {
    description?: string;
    metadata?: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
  } = {}
) {
  const activity = new this({
    userId,
    type,
    title,
    description: options.description,
    metadata: options.metadata || {},
    ipAddress: options.ipAddress,
    userAgent: options.userAgent
  });

  return await activity.save();
};

// 静态方法：获取用户活动历史
userActivitySchema.statics.getUserActivities = async function(
  userId: string | mongoose.Types.ObjectId,
  options: {
    type?: ActivityType;
    page?: number;
    limit?: number;
    startDate?: Date;
    endDate?: Date;
  } = {}
) {
  const {
    type,
    page = 1,
    limit = 20,
    startDate,
    endDate
  } = options;

  const filter: any = { userId };

  if (type) {
    filter.type = type;
  }

  if (startDate || endDate) {
    filter.createdAt = {};
    if (startDate) filter.createdAt.$gte = startDate;
    if (endDate) filter.createdAt.$lte = endDate;
  }

  const skip = (page - 1) * limit;
  const total = await this.countDocuments(filter);

  const activities = await this.find(filter)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .lean();

  return {
    data: activities,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1
    }
  };
};

// 虚拟字段：格式化时间
userActivitySchema.virtual('formattedDate').get(function() {
  return this.createdAt.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
});

export const UserActivity = mongoose.model<IUserActivity, IUserActivityModel>('UserActivity', userActivitySchema); 