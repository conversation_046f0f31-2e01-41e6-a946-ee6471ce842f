import mongoose, { Schema, Document, Types } from 'mongoose';

// 预约状态枚举
export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed', 
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  NO_SHOW = 'no_show'
}

// 取消原因枚举
export enum CancellationReason {
  USER_REQUEST = 'user_request',
  INSTRUCTOR_CANCELLED = 'instructor_cancelled',
  SCHEDULE_CHANGED = 'schedule_changed',
  EMERGENCY = 'emergency',
  WEATHER = 'weather',
  OTHER = 'other'
}

// 预约接口
export interface IBooking extends Document {
  userId: Types.ObjectId;
  scheduleId: Types.ObjectId;
  courseId: Types.ObjectId;
  instructorId: Types.ObjectId;
  status: BookingStatus;
  bookingTime: Date;
  notes?: string;
  specialRequests?: string;
  cancellationReason?: CancellationReason;
  cancellationNote?: string;
  cancelledAt?: Date;
  cancelledBy?: Types.ObjectId;
  confirmationToken?: string;
  reminderSent: boolean;
  attendanceMarked: boolean;
  rating?: {
    score: number;
    comment?: string;
    ratedAt: Date;
  };
  paymentStatus: 'pending' | 'paid' | 'refunded' | 'failed';
  paymentAmount?: number;
  createdAt: Date;
  updatedAt: Date;

  // 实例方法
  canCancel(): boolean;
  canConfirm(): boolean;
  canModify(): boolean;
  markAsCompleted(): Promise<IBooking>;
  markAsNoShow(): Promise<IBooking>;
  cancel(reason: CancellationReason, note?: string, cancelledBy?: string): Promise<IBooking>;
}

// 预约Schema
const bookingSchema = new Schema<IBooking>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '用户ID是必需的'],
    index: true
  },
  scheduleId: {
    type: Schema.Types.ObjectId,
    ref: 'CourseSchedule',
    required: [true, '课程时间段ID是必需的'],
    index: true
  },
  courseId: {
    type: Schema.Types.ObjectId,
    ref: 'Course',
    required: [true, '课程ID是必需的'],
    index: true
  },
  instructorId: {
    type: Schema.Types.ObjectId,
    ref: 'Instructor',
    required: [true, '教练员ID是必需的'],
    index: true
  },
  status: {
    type: String,
    enum: Object.values(BookingStatus),
    default: BookingStatus.PENDING,
    index: true
  },
  bookingTime: {
    type: Date,
    default: Date.now,
    index: true
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [500, '备注不能超过500个字符']
  },
  specialRequests: {
    type: String,
    trim: true,
    maxlength: [1000, '特殊要求不能超过1000个字符']
  },
  cancellationReason: {
    type: String,
    enum: Object.values(CancellationReason)
  },
  cancellationNote: {
    type: String,
    trim: true,
    maxlength: [500, '取消说明不能超过500个字符']
  },
  cancelledAt: {
    type: Date
  },
  cancelledBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  confirmationToken: {
    type: String,
    unique: true,
    sparse: true
  },
  reminderSent: {
    type: Boolean,
    default: false
  },
  attendanceMarked: {
    type: Boolean,
    default: false
  },
  rating: {
    score: {
      type: Number,
      min: [1, '评分不能低于1'],
      max: [5, '评分不能高于5']
    },
    comment: {
      type: String,
      trim: true,
      maxlength: [1000, '评价不能超过1000个字符']
    },
    ratedAt: {
      type: Date,
      default: Date.now
    }
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'refunded', 'failed'],
    default: 'pending',
    index: true
  },
  paymentAmount: {
    type: Number,
    min: [0, '支付金额不能为负数']
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(_doc, ret) {
      delete ret._id;
      delete ret.__v;
      delete ret.confirmationToken;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// 复合索引
bookingSchema.index({ userId: 1, status: 1 });
bookingSchema.index({ scheduleId: 1, status: 1 });
bookingSchema.index({ instructorId: 1, status: 1 });
bookingSchema.index({ userId: 1, scheduleId: 1 }, { unique: true });
bookingSchema.index({ bookingTime: 1, status: 1 });
bookingSchema.index({ createdAt: 1 });

// 虚拟字段
bookingSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

bookingSchema.virtual('schedule', {
  ref: 'CourseSchedule',
  localField: 'scheduleId',
  foreignField: '_id',
  justOne: true
});

bookingSchema.virtual('course', {
  ref: 'Course',
  localField: 'courseId',
  foreignField: '_id',
  justOne: true
});

bookingSchema.virtual('instructor', {
  ref: 'Instructor',
  localField: 'instructorId',
  foreignField: '_id',
  justOne: true
});

// 实例方法
bookingSchema.methods.canCancel = function(): boolean {
  return this.status === BookingStatus.PENDING || 
         this.status === BookingStatus.CONFIRMED;
};

bookingSchema.methods.canConfirm = function(): boolean {
  return this.status === BookingStatus.PENDING;
};

bookingSchema.methods.canModify = function(): boolean {
  return this.status === BookingStatus.PENDING || 
         this.status === BookingStatus.CONFIRMED;
};

bookingSchema.methods.markAsCompleted = async function(): Promise<IBooking> {
  if (this.status !== BookingStatus.CONFIRMED) {
    throw new Error('只有已确认的预约才能标记为完成');
  }
  this.status = BookingStatus.COMPLETED;
  this.attendanceMarked = true;
  return await this.save();
};

bookingSchema.methods.markAsNoShow = async function(): Promise<IBooking> {
  if (this.status !== BookingStatus.CONFIRMED) {
    throw new Error('只有已确认的预约才能标记为未到场');
  }
  this.status = BookingStatus.NO_SHOW;
  this.attendanceMarked = true;
  return await this.save();
};

bookingSchema.methods.cancel = async function(
  reason: CancellationReason, 
  note?: string, 
  cancelledBy?: string
): Promise<IBooking> {
  if (!this.canCancel()) {
    throw new Error('当前状态无法取消预约');
  }
  
  this.status = BookingStatus.CANCELLED;
  this.cancellationReason = reason;
  this.cancellationNote = note;
  this.cancelledAt = new Date();
  if (cancelledBy) {
    this.cancelledBy = new Types.ObjectId(cancelledBy);
  }
  
  return await this.save();
};

// 静态方法
bookingSchema.statics.findByUser = function(userId: string, status?: BookingStatus) {
  const query: any = { userId };
  if (status) {
    query.status = status;
  }
  return this.find(query)
    .populate('schedule course instructor')
    .sort({ createdAt: -1 });
};

bookingSchema.statics.findByInstructor = function(instructorId: string, status?: BookingStatus) {
  const query: any = { instructorId };
  if (status) {
    query.status = status;
  }
  return this.find(query)
    .populate('user schedule course')
    .sort({ createdAt: -1 });
};

bookingSchema.statics.findBySchedule = function(scheduleId: string, status?: BookingStatus) {
  const query: any = { scheduleId };
  if (status) {
    query.status = status;
  }
  return this.find(query)
    .populate('user course instructor')
    .sort({ bookingTime: 1 });
};

bookingSchema.statics.checkConflict = async function(userId: string, startTime: Date, endTime: Date, excludeBookingId?: string) {
  const pipeline = [
    {
      $match: {
        userId: new Types.ObjectId(userId),
        status: { $in: [BookingStatus.PENDING, BookingStatus.CONFIRMED] },
        ...(excludeBookingId ? { _id: { $ne: new Types.ObjectId(excludeBookingId) } } : {})
      }
    },
    {
      $lookup: {
        from: 'courseschedules',
        localField: 'scheduleId',
        foreignField: '_id',
        as: 'schedule'
      }
    },
    {
      $unwind: '$schedule'
    },
    {
      $match: {
        $or: [
          {
            'schedule.startTime': { $lt: endTime },
            'schedule.endTime': { $gt: startTime }
          }
        ]
      }
    }
  ];
  
  const conflicts = await this.aggregate(pipeline);
  return conflicts.length > 0;
};

// 预保存中间件
bookingSchema.pre('save', async function(next) {
  if (this.isNew) {
    // 生成确认令牌
    if (!this.confirmationToken) {
      this.confirmationToken = new mongoose.Types.ObjectId().toString();
    }
    
    // 检查重复预约
    const existingBooking = await mongoose.model('Booking').findOne({
      userId: this.userId,
      scheduleId: this.scheduleId,
      status: { $in: [BookingStatus.PENDING, BookingStatus.CONFIRMED] }
    });
    
    if (existingBooking) {
      next(new Error('您已经预约了这个课程时间段'));
      return;
    }
  }
  
  next();
});

// 预删除中间件
bookingSchema.pre('deleteOne', { document: true, query: false }, async function(next) {
  // 更新课程时间段的预约计数
  if (this.status === BookingStatus.PENDING || this.status === BookingStatus.CONFIRMED) {
    const CourseSchedule = mongoose.model('CourseSchedule');
    await CourseSchedule.findByIdAndUpdate(
      this.scheduleId,
      { $inc: { currentBookings: -1 } }
    );
  }
  next();
});

const Booking = mongoose.model<IBooking>('Booking', bookingSchema);

export default Booking; 