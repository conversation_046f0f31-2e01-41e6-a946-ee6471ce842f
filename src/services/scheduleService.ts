import CourseSchedule, { ICourseSchedule, ScheduleStatus } from '../models/CourseSchedule';
import { Course } from '../models/Course';
import { Instructor } from '../models/Instructor';
import logger from '../utils/logger';
import { Types } from 'mongoose';

export interface CreateScheduleData {
  courseId: string;
  instructorId: string;
  startTime: Date;
  endTime: Date;
  maxCapacity: number;
  location: string;
  description?: string;
  specialInstructions?: string;
  isRecurring?: boolean;
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    interval: number;
    daysOfWeek?: number[];
    endDate?: Date;
  };
}

export interface UpdateScheduleData {
  startTime?: Date;
  endTime?: Date;
  maxCapacity?: number;
  location?: string;
  description?: string;
  specialInstructions?: string;
  status?: ScheduleStatus;
}

export interface ScheduleQuery {
  courseId?: string;
  instructorId?: string;
  startDate?: Date;
  endDate?: Date;
  status?: ScheduleStatus;
  location?: string;
  availableOnly?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class ScheduleService {
  /**
   * 创建课程时间段
   */
  static async createSchedule(scheduleData: CreateScheduleData): Promise<ICourseSchedule> {
    try {
      // 验证课程是否存在
      const course = await Course.findById(scheduleData.courseId);
      if (!course) {
        throw new Error('课程不存在');
      }

      // 验证教练员是否存在
      const instructor = await Instructor.findById(scheduleData.instructorId);
      if (!instructor) {
        throw new Error('教练员不存在');
      }

      // 检查时间冲突
      const hasConflict = await this.checkInstructorConflict(
        scheduleData.instructorId,
        scheduleData.startTime,
        scheduleData.endTime
      );

      if (hasConflict) {
        throw new Error('教练员在该时间段已有其他课程安排');
      }

      // 创建时间段
      const schedule = new CourseSchedule(scheduleData);
      await schedule.save();

      // 如果是循环课程，创建后续时间段
      if (scheduleData.isRecurring && scheduleData.recurringPattern) {
        await this.createRecurringSchedules(schedule, scheduleData.recurringPattern);
      }

      logger.info(`Schedule created: ${schedule.id} for course ${scheduleData.courseId}`);
      
      return await CourseSchedule.findById(schedule.id)
        .populate('course instructor')
        .exec() as ICourseSchedule;

    } catch (error) {
      logger.error('Schedule creation failed:', error);
      throw error;
    }
  }

  /**
   * 获取时间段列表
   */
  static async getScheduleList(query: ScheduleQuery): Promise<{
    schedules: ICourseSchedule[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
    };
  }> {
    try {
      const {
        courseId,
        instructorId,
        startDate,
        endDate,
        status,
        location,
        availableOnly,
        page = 1,
        limit = 20,
        sortBy = 'startTime',
        sortOrder = 'asc'
      } = query;

      // 构建查询条件
      const filter: any = {};

      if (courseId) {
        filter.courseId = courseId;
      }

      if (instructorId) {
        filter.instructorId = instructorId;
      }

      if (status) {
        filter.status = status;
      }

      if (location) {
        filter.location = { $regex: location, $options: 'i' };
      }

      if (startDate || endDate) {
        filter.startTime = {};
        if (startDate) {
          filter.startTime.$gte = startDate;
        }
        if (endDate) {
          filter.startTime.$lte = endDate;
        }
      }

      if (availableOnly) {
        filter.status = ScheduleStatus.SCHEDULED;
        filter.startTime = { ...filter.startTime, $gt: new Date() };
        filter.$expr = { $lt: ['$currentBookings', '$maxCapacity'] };
      }

      // 计算分页
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // 获取总数
      const totalItems = await CourseSchedule.countDocuments(filter);
      const totalPages = Math.ceil(totalItems / limit);

      // 获取数据
      const schedules = await CourseSchedule.find(filter)
        .populate('course instructor')
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .exec();

      return {
        schedules,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit
        }
      };

    } catch (error) {
      logger.error('Get schedule list failed:', error);
      throw error;
    }
  }

  /**
   * 获取单个时间段详情
   */
  static async getScheduleById(scheduleId: string): Promise<ICourseSchedule> {
    try {
      const schedule = await CourseSchedule.findById(scheduleId)
        .populate('course instructor')
        .exec();

      if (!schedule) {
        throw new Error('课程时间段不存在');
      }

      return schedule;

    } catch (error) {
      logger.error('Get schedule by ID failed:', error);
      throw error;
    }
  }

  /**
   * 更新时间段
   */
  static async updateSchedule(scheduleId: string, updateData: UpdateScheduleData): Promise<ICourseSchedule> {
    try {
      const schedule = await CourseSchedule.findById(scheduleId);
      if (!schedule) {
        throw new Error('课程时间段不存在');
      }

      // 检查是否有预约，有预约时限制修改
      if (schedule.currentBookings > 0) {
        const allowedFields = ['description', 'specialInstructions', 'status'];
        const updateFields = Object.keys(updateData);
        const hasRestrictedFields = updateFields.some(field => !allowedFields.includes(field));
        
        if (hasRestrictedFields) {
          throw new Error('该时间段已有预约，只能修改描述、特殊说明和状态');
        }
      }

      // 如果修改了时间，检查冲突
      if (updateData.startTime || updateData.endTime) {
        const newStartTime = updateData.startTime || schedule.startTime;
        const newEndTime = updateData.endTime || schedule.endTime;

        const hasConflict = await this.checkInstructorConflict(
          schedule.instructorId.toString(),
          newStartTime,
          newEndTime,
          scheduleId
        );

        if (hasConflict) {
          throw new Error('教练员在该时间段已有其他课程安排');
        }
      }

      // 更新时间段
      Object.assign(schedule, updateData);
      await schedule.save();

      logger.info(`Schedule updated: ${scheduleId}`);

      return await CourseSchedule.findById(scheduleId)
        .populate('course instructor')
        .exec() as ICourseSchedule;

    } catch (error) {
      logger.error('Update schedule failed:', error);
      throw error;
    }
  }

  /**
   * 删除时间段
   */
  static async deleteSchedule(scheduleId: string): Promise<void> {
    try {
      const schedule = await CourseSchedule.findById(scheduleId);
      if (!schedule) {
        throw new Error('课程时间段不存在');
      }

      // 通过预删除中间件检查是否有预约
      await schedule.deleteOne();

      logger.info(`Schedule deleted: ${scheduleId}`);

    } catch (error) {
      logger.error('Delete schedule failed:', error);
      throw error;
    }
  }

  /**
   * 获取可用时间段
   */
  static async getAvailableSchedules(startDate?: Date, endDate?: Date): Promise<ICourseSchedule[]> {
    try {
      return await (CourseSchedule as any).findAvailable(startDate, endDate);
    } catch (error) {
      logger.error('Get available schedules failed:', error);
      throw error;
    }
  }

  /**
   * 获取教练员的时间段
   */
  static async getInstructorSchedules(instructorId: string, startDate?: Date): Promise<ICourseSchedule[]> {
    try {
      return await (CourseSchedule as any).findByInstructor(instructorId, startDate);
    } catch (error) {
      logger.error('Get instructor schedules failed:', error);
      throw error;
    }
  }

  /**
   * 批量更新时间段状态
   */
  static async batchUpdateStatus(scheduleIds: string[], status: ScheduleStatus): Promise<void> {
    try {
      await CourseSchedule.updateMany(
        { _id: { $in: scheduleIds } },
        { status }
      );

      logger.info(`Batch updated ${scheduleIds.length} schedules to status: ${status}`);

    } catch (error) {
      logger.error('Batch update schedule status failed:', error);
      throw error;
    }
  }

  /**
   * 检查教练员时间冲突
   */
  private static async checkInstructorConflict(
    instructorId: string,
    startTime: Date,
    endTime: Date,
    excludeScheduleId?: string
  ): Promise<boolean> {
    try {
      const query: any = {
        instructorId,
        status: { $ne: ScheduleStatus.CANCELLED },
        $or: [
          {
            startTime: { $lt: endTime },
            endTime: { $gt: startTime }
          }
        ]
      };

      if (excludeScheduleId) {
        query._id = { $ne: excludeScheduleId };
      }

      const conflictCount = await CourseSchedule.countDocuments(query);
      return conflictCount > 0;

    } catch (error) {
      logger.error('Check instructor conflict failed:', error);
      throw error;
    }
  }

  /**
   * 创建循环时间段
   */
  private static async createRecurringSchedules(
    baseSchedule: ICourseSchedule,
    pattern: CreateScheduleData['recurringPattern']
  ): Promise<void> {
    try {
      if (!pattern) return;

      const { frequency, interval, daysOfWeek, endDate } = pattern;
      const schedules: any[] = [];
      
      let currentDate = new Date(baseSchedule.startTime);
      const duration = baseSchedule.endTime.getTime() - baseSchedule.startTime.getTime();
      const maxSchedules = 100; // 限制最大创建数量
      let createdCount = 0;

      while (createdCount < maxSchedules) {
        // 计算下一个日期
        switch (frequency) {
          case 'daily':
            currentDate.setDate(currentDate.getDate() + interval);
            break;
          case 'weekly':
            currentDate.setDate(currentDate.getDate() + (interval * 7));
            break;
          case 'monthly':
            currentDate.setMonth(currentDate.getMonth() + interval);
            break;
        }

        // 检查结束日期
        if (endDate && currentDate > endDate) {
          break;
        }

        // 检查星期几限制
        if (daysOfWeek && daysOfWeek.length > 0) {
          if (!daysOfWeek.includes(currentDate.getDay())) {
            continue;
          }
        }

        // 检查冲突
        const hasConflict = await this.checkInstructorConflict(
          baseSchedule.instructorId.toString(),
          currentDate,
          new Date(currentDate.getTime() + duration)
        );

        if (!hasConflict) {
          schedules.push({
            courseId: baseSchedule.courseId,
            instructorId: baseSchedule.instructorId,
            startTime: new Date(currentDate),
            endTime: new Date(currentDate.getTime() + duration),
            maxCapacity: baseSchedule.maxCapacity,
            location: baseSchedule.location,
            description: baseSchedule.description,
            specialInstructions: baseSchedule.specialInstructions,
            isRecurring: false // 子时间段不标记为循环
          });
          createdCount++;
        }
      }

      if (schedules.length > 0) {
        await CourseSchedule.insertMany(schedules);
        logger.info(`Created ${schedules.length} recurring schedules for base schedule: ${baseSchedule.id}`);
      }

    } catch (error) {
      logger.error('Create recurring schedules failed:', error);
      throw error;
    }
  }

  /**
   * 获取时间段统计信息
   */
  static async getScheduleStats(instructorId?: string): Promise<{
    totalSchedules: number;
    scheduledCount: number;
    completedCount: number;
    cancelledCount: number;
    averageCapacity: number;
    averageBookingRate: number;
  }> {
    try {
      const matchStage: any = {};
      if (instructorId) {
        matchStage.instructorId = new Types.ObjectId(instructorId);
      }

      const stats = await CourseSchedule.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalSchedules: { $sum: 1 },
            scheduledCount: {
              $sum: { $cond: [{ $eq: ['$status', ScheduleStatus.SCHEDULED] }, 1, 0] }
            },
            completedCount: {
              $sum: { $cond: [{ $eq: ['$status', ScheduleStatus.COMPLETED] }, 1, 0] }
            },
            cancelledCount: {
              $sum: { $cond: [{ $eq: ['$status', ScheduleStatus.CANCELLED] }, 1, 0] }
            },
            totalCapacity: { $sum: '$maxCapacity' },
            totalBookings: { $sum: '$currentBookings' }
          }
        },
        {
          $project: {
            _id: 0,
            totalSchedules: 1,
            scheduledCount: 1,
            completedCount: 1,
            cancelledCount: 1,
            averageCapacity: { 
              $cond: [
                { $gt: ['$totalSchedules', 0] },
                { $divide: ['$totalCapacity', '$totalSchedules'] },
                0
              ]
            },
            averageBookingRate: {
              $cond: [
                { $gt: ['$totalCapacity', 0] },
                { $multiply: [{ $divide: ['$totalBookings', '$totalCapacity'] }, 100] },
                0
              ]
            }
          }
        }
      ]);

      return stats[0] || {
        totalSchedules: 0,
        scheduledCount: 0,
        completedCount: 0,
        cancelledCount: 0,
        averageCapacity: 0,
        averageBookingRate: 0
      };

    } catch (error) {
      logger.error('Get schedule stats failed:', error);
      throw error;
    }
  }
} 