import { Instructor, IInstructor } from '@/models/Instructor';
import { User } from '@/models/User';
import { ApiError } from '@/utils/apiError';
import logger from '@/utils/logger';
import mongoose from 'mongoose';

interface InstructorQueryOptions {
  page?: number;
  limit?: number;
  search?: string;
  specialty?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  minRating?: number;
  maxRating?: number;
  minExperience?: number;
  maxExperience?: number;
}

interface InstructorListResult {
  instructors: IInstructor[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

interface InstructorCreateData {
  userId: string;
  displayName: string;
  bio?: string;
  experience?: number;
  specialties?: string[];
  profileImage?: string;
}

interface InstructorUpdateData {
  displayName?: string;
  bio?: string;
  experience?: number;
  specialties?: string[];
  profileImage?: string;
  isActive?: boolean;
}

interface InstructorStats {
  overview: {
    totalInstructors: number;
    activeInstructors: number;
    inactiveInstructors: number;
    averageRating: number;
    totalClasses: number;
    totalReviews: number;
  };
  specialtyDistribution: Array<{
    specialty: string;
    count: number;
    percentage: number;
  }>;
  ratingDistribution: Array<{
    range: string;
    count: number;
    percentage: number;
  }>;
  experienceDistribution: Array<{
    range: string;
    count: number;
    percentage: number;
  }>;
}

interface BatchOperationResult {
  success: number;
  failed: number;
  results: Array<{
    id: string;
    success: boolean;
    error?: string;
  }>;
}

export class InstructorService {
  /**
   * 获取教练员列表
   */
  static async getInstructors(options: InstructorQueryOptions = {}): Promise<InstructorListResult> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        specialty,
        isActive,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        minRating = 0,
        maxRating = 5,
        minExperience = 0,
        maxExperience = 100
      } = options;

      // 构建查询条件
      const query: any = {};

      // 搜索条件
      if (search) {
        query.$or = [
          { displayName: { $regex: search, $options: 'i' } },
          { bio: { $regex: search, $options: 'i' } },
          { specialties: { $regex: search, $options: 'i' } }
        ];
      }

      // 专长筛选
      if (specialty) {
        query.specialties = { $in: [specialty] };
      }

      // 状态筛选
      if (isActive !== undefined) {
        query.isActive = isActive;
      }

      // 评分范围
      if (minRating > 0 || maxRating < 5) {
        query.averageRating = {
          $gte: minRating,
          $lte: maxRating
        };
      }

      // 经验范围
      if (minExperience > 0 || maxExperience < 100) {
        query.experience = {
          $gte: minExperience,
          $lte: maxExperience
        };
      }

      // 排序
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // 分页
      const skip = (page - 1) * limit;

      // 执行查询
      const [instructors, total] = await Promise.all([
        Instructor.find(query)
          .populate('userId', 'email username avatar firstName lastName')
          .sort(sortOptions)
          .skip(skip)
          .limit(limit),
        Instructor.countDocuments(query)
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        instructors,
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      };
    } catch (error) {
      logger.error('获取教练员列表失败:', error);
      throw new ApiError(500, '获取教练员列表失败');
    }
  }

  /**
   * 根据ID获取教练员详情
   */
  static async getInstructorById(id: string): Promise<IInstructor> {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ApiError(400, '无效的教练员ID');
      }

      const instructor = await Instructor.findById(id)
        .populate('userId', 'email username avatar firstName lastName');

      if (!instructor) {
        throw new ApiError(404, '教练员不存在');
      }

      return instructor;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error('获取教练员详情失败:', error);
      throw new ApiError(500, '获取教练员详情失败');
    }
  }

  /**
   * 根据用户ID获取教练员信息
   */
  static async getInstructorByUserId(userId: string): Promise<IInstructor | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new ApiError(400, '无效的用户ID');
      }

      const instructor = await Instructor.findOne({ userId })
        .populate('userId', 'email username avatar firstName lastName');

      return instructor;
    } catch (error) {
      logger.error('根据用户ID获取教练员失败:', error);
      throw new ApiError(500, '获取教练员信息失败');
    }
  }

  /**
   * 创建教练员
   */
  static async createInstructor(data: InstructorCreateData): Promise<IInstructor> {
    try {
      // 验证用户是否存在
      const user = await User.findById(data.userId);
      if (!user) {
        throw new ApiError(404, '用户不存在');
      }

      // 检查是否已经是教练员
      const existingInstructor = await Instructor.findOne({ userId: data.userId });
      if (existingInstructor) {
        throw new ApiError(400, '该用户已经是教练员');
      }

      // 创建教练员
      const instructor = new Instructor(data);
      await instructor.save();

      // 填充用户信息
      await instructor.populate('userId', 'email username avatar firstName lastName');

      logger.info('教练员创建成功:', { instructorId: instructor.id, userId: data.userId });
      return instructor;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error('创建教练员失败:', error);
      throw new ApiError(500, '创建教练员失败');
    }
  }

  /**
   * 更新教练员信息
   */
  static async updateInstructor(id: string, data: InstructorUpdateData): Promise<IInstructor> {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ApiError(400, '无效的教练员ID');
      }

      const instructor = await Instructor.findByIdAndUpdate(
        id,
        { ...data, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).populate('userId', 'email username avatar firstName lastName');

      if (!instructor) {
        throw new ApiError(404, '教练员不存在');
      }

      logger.info('教练员信息更新成功:', { instructorId: id });
      return instructor;
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error('更新教练员信息失败:', error);
      throw new ApiError(500, '更新教练员信息失败');
    }
  }

  /**
   * 删除教练员（软删除）
   */
  static async deleteInstructor(id: string): Promise<void> {
    try {
      if (!mongoose.Types.ObjectId.isValid(id)) {
        throw new ApiError(400, '无效的教练员ID');
      }

      const instructor = await Instructor.findById(id);
      if (!instructor) {
        throw new ApiError(404, '教练员不存在');
      }

      instructor.deletedAt = new Date();
      await instructor.save();

      logger.info('教练员删除成功:', { instructorId: id });
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error('删除教练员失败:', error);
      throw new ApiError(500, '删除教练员失败');
    }
  }

  /**
   * 更新教练员状态
   */
  static async updateInstructorStatus(id: string, isActive: boolean): Promise<IInstructor> {
    try {
      return await this.updateInstructor(id, { isActive });
    } catch (error) {
      logger.error('更新教练员状态失败:', error);
      throw error;
    }
  }

  /**
   * 批量操作
   */
  static async batchOperation(
    ids: string[],
    operation: 'activate' | 'deactivate' | 'delete'
  ): Promise<BatchOperationResult> {
    try {
      const results: BatchOperationResult['results'] = [];
      let success = 0;
      let failed = 0;

      for (const id of ids) {
        try {
          switch (operation) {
            case 'activate':
              await this.updateInstructorStatus(id, true);
              break;
            case 'deactivate':
              await this.updateInstructorStatus(id, false);
              break;
            case 'delete':
              await this.deleteInstructor(id);
              break;
          }
          results.push({ id, success: true });
          success++;
        } catch (error) {
          results.push({
            id,
            success: false,
            error: error instanceof Error ? error.message : '未知错误'
          });
          failed++;
        }
      }

      logger.info('批量操作完成:', { operation, success, failed });
      return { success, failed, results };
    } catch (error) {
      logger.error('批量操作失败:', error);
      throw new ApiError(500, '批量操作失败');
    }
  }

  /**
   * 获取教练员统计信息
   */
  static async getInstructorStats(): Promise<InstructorStats> {
    try {
      // 基础统计
      const [
        totalInstructors,
        activeInstructors,
        allInstructors,
        specialtyAgg,
        ratingAgg,
        experienceAgg
      ] = await Promise.all([
        Instructor.countDocuments(),
        Instructor.countDocuments({ isActive: true }),
        Instructor.find({}, 'averageRating totalClasses totalReviews experience specialties'),
        Instructor.aggregate([
          { $unwind: '$specialties' },
          { $group: { _id: '$specialties', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),
        Instructor.aggregate([
          {
            $bucket: {
              groupBy: '$averageRating',
              boundaries: [0, 1, 2, 3, 4, 5],
              default: 'other',
              output: { count: { $sum: 1 } }
            }
          }
        ]),
        Instructor.aggregate([
          {
            $bucket: {
              groupBy: '$experience',
              boundaries: [0, 1, 3, 5, 10, 100],
              default: 'other',
              output: { count: { $sum: 1 } }
            }
          }
        ])
      ]);

      // 计算平均值
      const totalClasses = allInstructors.reduce((sum, inst) => sum + inst.totalClasses, 0);
      const totalReviews = allInstructors.reduce((sum, inst) => sum + inst.totalReviews, 0);
      const avgRating = allInstructors.length > 0
        ? allInstructors.reduce((sum, inst) => sum + inst.averageRating, 0) / allInstructors.length
        : 0;

      // 专长分布
      const specialtyDistribution = specialtyAgg.map(item => ({
        specialty: item._id,
        count: item.count,
        percentage: totalInstructors > 0 ? Math.round((item.count / totalInstructors) * 100) : 0
      }));

      // 评分分布
      const ratingDistribution = ratingAgg.map(item => {
        const range = typeof item._id === 'number' 
          ? `${item._id}-${item._id + 1}` 
          : '其他';
        return {
          range,
          count: item.count,
          percentage: totalInstructors > 0 ? Math.round((item.count / totalInstructors) * 100) : 0
        };
      });

      // 经验分布
      const experienceDistribution = experienceAgg.map(item => {
        let range: string;
        if (item._id === 0) range = '新手 (0年)';
        else if (item._id === 1) range = '初级 (1-2年)';
        else if (item._id === 3) range = '中级 (3-4年)';
        else if (item._id === 5) range = '高级 (5-9年)';
        else if (item._id === 10) range = '专家 (10年+)';
        else range = '其他';

        return {
          range,
          count: item.count,
          percentage: totalInstructors > 0 ? Math.round((item.count / totalInstructors) * 100) : 0
        };
      });

      return {
        overview: {
          totalInstructors,
          activeInstructors,
          inactiveInstructors: totalInstructors - activeInstructors,
          averageRating: Math.round(avgRating * 100) / 100,
          totalClasses,
          totalReviews
        },
        specialtyDistribution,
        ratingDistribution,
        experienceDistribution
      };
    } catch (error) {
      logger.error('获取教练员统计失败:', error);
      throw new ApiError(500, '获取教练员统计失败');
    }
  }

  /**
   * 更新教练员统计信息
   */
  static async updateInstructorStats(
    instructorId: string,
    newRating?: number,
    newClassCount?: number
  ): Promise<void> {
    try {
      const instructor = await Instructor.findById(instructorId);
      if (!instructor) {
        throw new ApiError(404, '教练员不存在');
      }

      // 手动更新统计信息
      if (newRating !== undefined) {
        const totalRatingPoints = instructor.averageRating * instructor.totalReviews + newRating;
        instructor.totalReviews += 1;
        instructor.averageRating = totalRatingPoints / instructor.totalReviews;
      }
      
      if (newClassCount !== undefined) {
        instructor.totalClasses = newClassCount;
      }
      
      await instructor.save();
      logger.info('教练员统计更新成功:', { instructorId });
    } catch (error) {
      if (error instanceof ApiError) throw error;
      logger.error('更新教练员统计失败:', error);
      throw new ApiError(500, '更新教练员统计失败');
    }
  }

  /**
   * 获取活跃教练员列表
   */
  static async getActiveInstructors(): Promise<IInstructor[]> {
    try {
      return await Instructor.find({ isActive: true }).populate('userId', 'email username avatar');
    } catch (error) {
      logger.error('获取活跃教练员失败:', error);
      throw new ApiError(500, '获取活跃教练员失败');
    }
  }
} 