import Booking, { IBooking, BookingStatus, CancellationReason } from '../models/Booking';
import CourseSchedule from '../models/CourseSchedule';
import { User } from '../models/User';
import logger from '../utils/logger';
import { Types } from 'mongoose';

export interface CreateBookingData {
  userId: string;
  scheduleId: string;
  notes?: string;
  specialRequests?: string;
}

export interface UpdateBookingData {
  notes?: string;
  specialRequests?: string;
  status?: BookingStatus;
}

export interface BookingQuery {
  userId?: string;
  instructorId?: string;
  courseId?: string;
  scheduleId?: string;
  status?: BookingStatus;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class BookingService {
  /**
   * 创建预约
   */
  static async createBooking(bookingData: CreateBookingData): Promise<IBooking> {
    try {
      const { userId, scheduleId, notes, specialRequests } = bookingData;

      // 验证用户是否存在
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 验证时间段是否存在
      const schedule = await CourseSchedule.findById(scheduleId).populate('course instructor');
      if (!schedule) {
        throw new Error('课程时间段不存在');
      }

      // 检查时间段是否可预约
      if (!schedule.canBook()) {
        throw new Error('该时间段不可预约');
      }

      // 检查用户时间冲突
      const hasConflict = await (Booking as any).checkConflict(
        userId,
        schedule.startTime,
        schedule.endTime
      );

      if (hasConflict) {
        throw new Error('您在该时间段已有其他预约');
      }

      // 使用事务创建预约并更新容量
      const session = await Booking.startSession();
      
      try {
        await session.withTransaction(async () => {
          // 创建预约
          const booking = new Booking({
            userId,
            scheduleId,
            courseId: schedule.courseId,
            instructorId: schedule.instructorId,
            notes,
            specialRequests
          });

          await booking.save({ session });

          // 增加预约计数
          await schedule.incrementBookings();
        });

        await session.endSession();

        // 获取完整的预约信息
        const newBooking = await Booking.findOne({ userId, scheduleId })
          .populate('user schedule course instructor')
          .exec();

        logger.info(`Booking created: ${newBooking!.id} for user ${userId} and schedule ${scheduleId}`);

        return newBooking!;

      } catch (error) {
        await session.endSession();
        throw error;
      }

    } catch (error) {
      logger.error('Booking creation failed:', error);
      throw error;
    }
  }

  /**
   * 获取预约列表
   */
  static async getBookingList(query: BookingQuery): Promise<{
    bookings: IBooking[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
    };
  }> {
    try {
      const {
        userId,
        instructorId,
        courseId,
        scheduleId,
        status,
        startDate,
        endDate,
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = query;

      // 构建查询条件
      const filter: any = {};

      if (userId) {
        filter.userId = userId;
      }

      if (instructorId) {
        filter.instructorId = instructorId;
      }

      if (courseId) {
        filter.courseId = courseId;
      }

      if (scheduleId) {
        filter.scheduleId = scheduleId;
      }

      if (status) {
        filter.status = status;
      }

      if (startDate || endDate) {
        filter.bookingTime = {};
        if (startDate) {
          filter.bookingTime.$gte = startDate;
        }
        if (endDate) {
          filter.bookingTime.$lte = endDate;
        }
      }

      // 计算分页
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // 获取总数
      const totalItems = await Booking.countDocuments(filter);
      const totalPages = Math.ceil(totalItems / limit);

      // 获取数据
      const bookings = await Booking.find(filter)
        .populate('user schedule course instructor')
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .exec();

      return {
        bookings,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit
        }
      };

    } catch (error) {
      logger.error('Get booking list failed:', error);
      throw error;
    }
  }

  /**
   * 获取单个预约详情
   */
  static async getBookingById(bookingId: string): Promise<IBooking> {
    try {
      const booking = await Booking.findById(bookingId)
        .populate('user schedule course instructor')
        .exec();

      if (!booking) {
        throw new Error('预约不存在');
      }

      return booking;

    } catch (error) {
      logger.error('Get booking by ID failed:', error);
      throw error;
    }
  }

  /**
   * 更新预约
   */
  static async updateBooking(bookingId: string, updateData: UpdateBookingData): Promise<IBooking> {
    try {
      const booking = await Booking.findById(bookingId);
      if (!booking) {
        throw new Error('预约不存在');
      }

      // 检查是否可以修改
      if (!booking.canModify()) {
        throw new Error('当前状态无法修改预约');
      }

      // 更新预约
      Object.assign(booking, updateData);
      await booking.save();

      logger.info(`Booking updated: ${bookingId}`);

      return await Booking.findById(bookingId)
        .populate('user schedule course instructor')
        .exec() as IBooking;

    } catch (error) {
      logger.error('Update booking failed:', error);
      throw error;
    }
  }

  /**
   * 取消预约
   */
  static async cancelBooking(
    bookingId: string, 
    reason: CancellationReason, 
    note?: string, 
    cancelledBy?: string
  ): Promise<IBooking> {
    try {
      const booking = await Booking.findById(bookingId).populate('schedule');
      if (!booking) {
        throw new Error('预约不存在');
      }

      if (!booking.canCancel()) {
        throw new Error('当前状态无法取消预约');
      }

      // 使用事务取消预约并更新容量
      const session = await Booking.startSession();
      
      try {
        await session.withTransaction(async () => {
          // 取消预约
          await booking.cancel(reason, note, cancelledBy);

          // 减少预约计数
          const schedule = await CourseSchedule.findById(booking.scheduleId);
          if (schedule) {
            await schedule.decrementBookings();
          }
        });

        await session.endSession();

        logger.info(`Booking cancelled: ${bookingId} by ${cancelledBy || 'user'}`);

        return await Booking.findById(bookingId)
          .populate('user schedule course instructor')
          .exec() as IBooking;

      } catch (error) {
        await session.endSession();
        throw error;
      }

    } catch (error) {
      logger.error('Cancel booking failed:', error);
      throw error;
    }
  }

  /**
   * 确认预约（教练员操作）
   */
  static async confirmBooking(bookingId: string, instructorId: string): Promise<IBooking> {
    try {
      const booking = await Booking.findById(bookingId);
      if (!booking) {
        throw new Error('预约不存在');
      }

      // 验证教练员权限
      if (booking.instructorId.toString() !== instructorId) {
        throw new Error('只能确认自己的课程预约');
      }

      if (!booking.canConfirm()) {
        throw new Error('当前状态无法确认预约');
      }

      booking.status = BookingStatus.CONFIRMED;
      await booking.save();

      logger.info(`Booking confirmed: ${bookingId} by instructor ${instructorId}`);

      return await Booking.findById(bookingId)
        .populate('user schedule course instructor')
        .exec() as IBooking;

    } catch (error) {
      logger.error('Confirm booking failed:', error);
      throw error;
    }
  }

  /**
   * 标记出席（教练员操作）
   */
  static async markAttendance(
    bookingId: string, 
    instructorId: string, 
    attended: boolean
  ): Promise<IBooking> {
    try {
      const booking = await Booking.findById(bookingId);
      if (!booking) {
        throw new Error('预约不存在');
      }

      // 验证教练员权限
      if (booking.instructorId.toString() !== instructorId) {
        throw new Error('只能标记自己的课程出席');
      }

      if (booking.status !== BookingStatus.CONFIRMED) {
        throw new Error('只能标记已确认的预约出席');
      }

      if (attended) {
        await booking.markAsCompleted();
      } else {
        await booking.markAsNoShow();
      }

      logger.info(`Attendance marked: ${bookingId} - ${attended ? 'attended' : 'no-show'}`);

      return await Booking.findById(bookingId)
        .populate('user schedule course instructor')
        .exec() as IBooking;

    } catch (error) {
      logger.error('Mark attendance failed:', error);
      throw error;
    }
  }

  /**
   * 添加评价
   */
  static async addRating(
    bookingId: string, 
    userId: string, 
    score: number, 
    comment?: string
  ): Promise<IBooking> {
    try {
      const booking = await Booking.findById(bookingId);
      if (!booking) {
        throw new Error('预约不存在');
      }

      // 验证用户权限
      if (booking.userId.toString() !== userId) {
        throw new Error('只能评价自己的预约');
      }

      if (booking.status !== BookingStatus.COMPLETED) {
        throw new Error('只能评价已完成的课程');
      }

      if (booking.rating) {
        throw new Error('该预约已有评价');
      }

      booking.rating = {
        score,
        comment,
        ratedAt: new Date()
      };

      await booking.save();

      logger.info(`Rating added: ${bookingId} - score: ${score}`);

      return await Booking.findById(bookingId)
        .populate('user schedule course instructor')
        .exec() as IBooking;

    } catch (error) {
      logger.error('Add rating failed:', error);
      throw error;
    }
  }

  /**
   * 获取用户预约历史
   */
  static async getUserBookings(userId: string, status?: BookingStatus): Promise<IBooking[]> {
    try {
      return await (Booking as any).findByUser(userId, status);
    } catch (error) {
      logger.error('Get user bookings failed:', error);
      throw error;
    }
  }

  /**
   * 获取教练员预约
   */
  static async getInstructorBookings(instructorId: string, status?: BookingStatus): Promise<IBooking[]> {
    try {
      return await (Booking as any).findByInstructor(instructorId, status);
    } catch (error) {
      logger.error('Get instructor bookings failed:', error);
      throw error;
    }
  }

  /**
   * 获取时间段预约
   */
  static async getScheduleBookings(scheduleId: string, status?: BookingStatus): Promise<IBooking[]> {
    try {
      return await (Booking as any).findBySchedule(scheduleId, status);
    } catch (error) {
      logger.error('Get schedule bookings failed:', error);
      throw error;
    }
  }

  /**
   * 批量更新预约状态
   */
  static async batchUpdateStatus(bookingIds: string[], status: BookingStatus): Promise<void> {
    try {
      await Booking.updateMany(
        { _id: { $in: bookingIds } },
        { status }
      );

      logger.info(`Batch updated ${bookingIds.length} bookings to status: ${status}`);

    } catch (error) {
      logger.error('Batch update booking status failed:', error);
      throw error;
    }
  }

  /**
   * 获取预约统计信息
   */
  static async getBookingStats(instructorId?: string, userId?: string): Promise<{
    totalBookings: number;
    pendingCount: number;
    confirmedCount: number;
    completedCount: number;
    cancelledCount: number;
    noShowCount: number;
    averageRating: number;
    completionRate: number;
  }> {
    try {
      const matchStage: any = {};
      if (instructorId) {
        matchStage.instructorId = new Types.ObjectId(instructorId);
      }
      if (userId) {
        matchStage.userId = new Types.ObjectId(userId);
      }

      const stats = await Booking.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalBookings: { $sum: 1 },
            pendingCount: {
              $sum: { $cond: [{ $eq: ['$status', BookingStatus.PENDING] }, 1, 0] }
            },
            confirmedCount: {
              $sum: { $cond: [{ $eq: ['$status', BookingStatus.CONFIRMED] }, 1, 0] }
            },
            completedCount: {
              $sum: { $cond: [{ $eq: ['$status', BookingStatus.COMPLETED] }, 1, 0] }
            },
            cancelledCount: {
              $sum: { $cond: [{ $eq: ['$status', BookingStatus.CANCELLED] }, 1, 0] }
            },
            noShowCount: {
              $sum: { $cond: [{ $eq: ['$status', BookingStatus.NO_SHOW] }, 1, 0] }
            },
            avgRating: { $avg: '$rating.score' },
            confirmedOrCompleted: {
              $sum: {
                $cond: [
                  { $in: ['$status', [BookingStatus.CONFIRMED, BookingStatus.COMPLETED]] },
                  1,
                  0
                ]
              }
            }
          }
        },
        {
          $project: {
            _id: 0,
            totalBookings: 1,
            pendingCount: 1,
            confirmedCount: 1,
            completedCount: 1,
            cancelledCount: 1,
            noShowCount: 1,
            averageRating: { $ifNull: ['$avgRating', 0] },
            completionRate: {
              $cond: [
                { $gt: ['$totalBookings', 0] },
                { $multiply: [{ $divide: ['$completedCount', '$totalBookings'] }, 100] },
                0
              ]
            }
          }
        }
      ]);

      return stats[0] || {
        totalBookings: 0,
        pendingCount: 0,
        confirmedCount: 0,
        completedCount: 0,
        cancelledCount: 0,
        noShowCount: 0,
        averageRating: 0,
        completionRate: 0
      };

    } catch (error) {
      logger.error('Get booking stats failed:', error);
      throw error;
    }
  }

  /**
   * 检查用户预约限制
   */
  static async checkBookingLimits(userId: string): Promise<{
    canBook: boolean;
    reason?: string;
    currentActiveBookings: number;
    maxAllowedBookings: number;
  }> {
    try {
      const maxBookingsPerUser = 10; // 每个用户最多同时10个预约
      
      const activeBookings = await Booking.countDocuments({
        userId,
        status: { $in: [BookingStatus.PENDING, BookingStatus.CONFIRMED] }
      });

      const canBook = activeBookings < maxBookingsPerUser;

      return {
        canBook,
        reason: canBook ? undefined : '预约数量已达上限',
        currentActiveBookings: activeBookings,
        maxAllowedBookings: maxBookingsPerUser
      };

    } catch (error) {
      logger.error('Check booking limits failed:', error);
      throw error;
    }
  }

  /**
   * 获取即将到来的预约提醒
   */
  static async getUpcomingBookings(hours: number = 24): Promise<IBooking[]> {
    try {
      const now = new Date();
      const futureTime = new Date(now.getTime() + hours * 60 * 60 * 1000);

      const upcomingBookings = await Booking.aggregate([
        {
          $match: {
            status: BookingStatus.CONFIRMED,
            reminderSent: false
          }
        },
        {
          $lookup: {
            from: 'courseschedules',
            localField: 'scheduleId',
            foreignField: '_id',
            as: 'schedule'
          }
        },
        {
          $unwind: '$schedule'
        },
        {
          $match: {
            'schedule.startTime': {
              $gte: now,
              $lte: futureTime
            }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user'
          }
        },
        {
          $lookup: {
            from: 'courses',
            localField: 'courseId',
            foreignField: '_id',
            as: 'course'
          }
        }
      ]);

      return upcomingBookings;

    } catch (error) {
      logger.error('Get upcoming bookings failed:', error);
      throw error;
    }
  }

  /**
   * 标记提醒已发送
   */
  static async markReminderSent(bookingIds: string[]): Promise<void> {
    try {
      await Booking.updateMany(
        { _id: { $in: bookingIds } },
        { reminderSent: true }
      );

      logger.info(`Marked reminder sent for ${bookingIds.length} bookings`);

    } catch (error) {
      logger.error('Mark reminder sent failed:', error);
      throw error;
    }
  }
} 