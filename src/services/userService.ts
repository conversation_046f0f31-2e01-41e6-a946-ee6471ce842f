import { User, IUser } from '@/models/User';
import { UserActivity, ActivityType } from '@/models/UserActivity';
import { UserRole, PaginationParams, PaginationResult } from '@/utils/types';
import logger from '@/utils/logger';
import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

export interface UserListQuery {
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  isEmailVerified?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface UpdateUserData {
  displayName?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  gender?: 'male' | 'female' | 'other';
  dateOfBirth?: Date;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  yogaExperience?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  preferredStyles?: string[];
  healthConditions?: string[];
  goals?: string[];
  notificationPreferences?: {
    email?: boolean;
    sms?: boolean;
    push?: boolean;
    marketing?: boolean;
  };
  language?: string;
  timezone?: string;
}

export interface BatchUserOperation {
  action: 'activate' | 'deactivate' | 'delete' | 'verifyEmail';
  userIds: string[];
}

export interface ProfileUpdateData {
  displayName?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  gender?: 'male' | 'female' | 'other';
  dateOfBirth?: Date;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  yogaExperience?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  preferredStyles?: string[];
  healthConditions?: string[];
  goals?: string[];
  language?: string;
  timezone?: string;
}

export interface PreferencesUpdateData {
  notificationPreferences?: {
    email?: boolean;
    sms?: boolean;
    push?: boolean;
    marketing?: boolean;
  };
  language?: string;
  timezone?: string;
  preferredStyles?: string[];
  goals?: string[];
}

export interface PasswordChangeData {
  currentPassword: string;
  newPassword: string;
}

export class UserService {
  /**
   * 获取用户列表（分页、搜索、筛选）
   */
  static async getUserList(
    query: UserListQuery, 
    pagination: PaginationParams
  ): Promise<PaginationResult<IUser>> {
    try {
      // 构建查询条件
      const filter: any = {};

      // 搜索条件（邮箱、用户名、显示名称）
      if (query.search) {
        filter.$or = [
          { email: { $regex: query.search, $options: 'i' } },
          { username: { $regex: query.search, $options: 'i' } },
          { displayName: { $regex: query.search, $options: 'i' } },
          { firstName: { $regex: query.search, $options: 'i' } },
          { lastName: { $regex: query.search, $options: 'i' } }
        ];
      }

      // 角色筛选
      if (query.role) {
        filter.role = query.role;
      }

      // 激活状态筛选
      if (query.isActive !== undefined) {
        filter.isActive = query.isActive;
      }

      // 邮箱验证状态筛选
      if (query.isEmailVerified !== undefined) {
        filter.isEmailVerified = query.isEmailVerified;
      }

      // 日期范围筛选
      if (query.dateFrom || query.dateTo) {
        filter.createdAt = {};
        if (query.dateFrom) {
          filter.createdAt.$gte = query.dateFrom;
        }
        if (query.dateTo) {
          filter.createdAt.$lte = query.dateTo;
        }
      }

      // 计算总数
      const total = await User.countDocuments(filter);

      // 计算分页
      const skip = (pagination.page - 1) * pagination.limit;
      const totalPages = Math.ceil(total / pagination.limit);

             // 排序
       const sortField = pagination.sortBy || 'createdAt';
       const sortOrder = pagination.sortOrder === 'asc' ? 1 : -1;
       const sort: Record<string, 1 | -1> = { [sortField]: sortOrder };

      // 查询用户列表
      const users = await User.find(filter)
        .select('-password -refreshTokens -emailVerificationToken -passwordResetToken -phoneVerificationCode')
        .sort(sort)
        .skip(skip)
        .limit(pagination.limit)
        .lean();

      logger.info(`User list query: ${users.length} users found, total: ${total}`);

      return {
        data: users as IUser[],
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages,
          hasNext: pagination.page < totalPages,
          hasPrev: pagination.page > 1
        }
      };

    } catch (error) {
      logger.error('Get user list failed:', error);
      throw error;
    }
  }

  /**
   * 获取用户详情
   */
  static async getUserById(userId: string): Promise<IUser> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findById(userId)
        .select('-password -refreshTokens -emailVerificationToken -passwordResetToken -phoneVerificationCode');

      if (!user) {
        throw new Error('用户不存在');
      }

      logger.info(`User details retrieved: ${user.email}`);
      return user;

    } catch (error) {
      logger.error('Get user by ID failed:', error);
      throw error;
    }
  }

  /**
   * 更新用户信息（管理员）
   */
  static async updateUser(userId: string, updateData: UpdateUserData): Promise<IUser> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 更新用户信息
      Object.assign(user, updateData);
      await user.save();

      // 返回更新后的用户信息（不包含敏感信息）
      const updatedUser = await User.findById(userId)
        .select('-password -refreshTokens -emailVerificationToken -passwordResetToken -phoneVerificationCode');

      logger.info(`User updated by admin: ${user.email}`);
      return updatedUser!;

    } catch (error) {
      logger.error('Update user failed:', error);
      throw error;
    }
  }

  /**
   * 更改用户状态
   */
  static async updateUserStatus(
    userId: string, 
    status: { isActive?: boolean; isEmailVerified?: boolean }
  ): Promise<IUser> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findByIdAndUpdate(
        userId,
        { ...status, updatedAt: new Date() },
        { new: true }
      ).select('-password -refreshTokens -emailVerificationToken -passwordResetToken -phoneVerificationCode');

      if (!user) {
        throw new Error('用户不存在');
      }

      logger.info(`User status updated: ${user.email}, status: ${JSON.stringify(status)}`);
      return user;

    } catch (error) {
      logger.error('Update user status failed:', error);
      throw error;
    }
  }

  /**
   * 更改用户角色
   */
  static async updateUserRole(userId: string, role: UserRole): Promise<IUser> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findByIdAndUpdate(
        userId,
        { role, updatedAt: new Date() },
        { new: true }
      ).select('-password -refreshTokens -emailVerificationToken -passwordResetToken -phoneVerificationCode');

      if (!user) {
        throw new Error('用户不存在');
      }

      logger.info(`User role updated: ${user.email}, new role: ${role}`);
      return user;

    } catch (error) {
      logger.error('Update user role failed:', error);
      throw error;
    }
  }

  /**
   * 软删除用户
   */
  static async deleteUser(userId: string): Promise<void> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findByIdAndUpdate(
        userId,
        { 
          deletedAt: new Date(),
          isActive: false,
          updatedAt: new Date()
        }
      );

      if (!user) {
        throw new Error('用户不存在');
      }

      logger.info(`User soft deleted: ${user.email}`);

    } catch (error) {
      logger.error('Delete user failed:', error);
      throw error;
    }
  }

  /**
   * 批量操作用户
   */
  static async batchUserOperation(operation: BatchUserOperation): Promise<{ success: number; failed: number; errors: string[] }> {
    try {
      const results = { success: 0, failed: 0, errors: [] as string[] };

      // 验证用户ID
      const validUserIds = operation.userIds.filter(id => mongoose.Types.ObjectId.isValid(id));
      if (validUserIds.length !== operation.userIds.length) {
        results.failed += operation.userIds.length - validUserIds.length;
        results.errors.push('部分用户ID无效');
      }

      if (validUserIds.length === 0) {
        throw new Error('没有有效的用户ID');
      }

      let updateData: any = {};

      switch (operation.action) {
        case 'activate':
          updateData = { isActive: true, updatedAt: new Date() };
          break;
        case 'deactivate':
          updateData = { isActive: false, updatedAt: new Date() };
          break;
        case 'delete':
          updateData = { deletedAt: new Date(), isActive: false, updatedAt: new Date() };
          break;
        case 'verifyEmail':
          updateData = { 
            isEmailVerified: true, 
            emailVerificationToken: undefined,
            emailVerificationExpires: undefined,
            updatedAt: new Date() 
          };
          break;
        default:
          throw new Error('不支持的批量操作类型');
      }

      // 执行批量更新
      const result = await User.updateMany(
        { _id: { $in: validUserIds } },
        updateData
      );

      results.success = result.modifiedCount;
      results.failed += validUserIds.length - result.modifiedCount;

      if (results.failed > 0) {
        results.errors.push(`${results.failed}个用户操作失败`);
      }

      logger.info(`Batch user operation completed: ${operation.action}, success: ${results.success}, failed: ${results.failed}`);
      return results;

    } catch (error) {
      logger.error('Batch user operation failed:', error);
      throw error;
    }
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStatistics() {
    try {
      const totalUsers = await User.countDocuments();
      const activeUsers = await User.countDocuments({ isActive: true });
      const verifiedUsers = await User.countDocuments({ isEmailVerified: true });
      const inactiveUsers = await User.countDocuments({ isActive: false });
      
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const newUsersToday = await User.countDocuments({ createdAt: { $gte: today } });

      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const newUsersThisWeek = await User.countDocuments({ createdAt: { $gte: sevenDaysAgo } });

      // 按角色统计
      const usersByRole = await User.aggregate([
        { $group: { _id: '$role', count: { $sum: 1 } } }
      ]);

      // 按注册时间统计（最近7天）
      const registrationTrend = await User.aggregate([
        {
          $match: {
            createdAt: { $gte: sevenDaysAgo }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { _id: 1 } }
      ]);

      const statistics = {
        overview: {
          totalUsers,
          activeUsers,
          inactiveUsers,
          verifiedUsers,
          unverifiedUsers: totalUsers - verifiedUsers,
          verificationRate: totalUsers > 0 ? Math.round((verifiedUsers / totalUsers) * 100) : 0
        },
        growth: {
          newUsersToday,
          newUsersThisWeek,
          registrationTrend
        },
        demographics: {
          usersByRole: usersByRole.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {} as Record<string, number>)
        }
      };

      logger.info('User statistics retrieved');
      return statistics;

    } catch (error) {
      logger.error('Get user statistics failed:', error);
      throw error;
    }
  }

  /**
   * 获取个人资料（用户自己）
   */
  static async getProfile(userId: string): Promise<IUser> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findById(userId)
        .select('-password -refreshTokens -emailVerificationToken -passwordResetToken -phoneVerificationCode');

      if (!user) {
        throw new Error('用户不存在');
      }

      logger.info(`Profile retrieved: ${user.email}`);
      return user;

    } catch (error) {
      logger.error('Get profile failed:', error);
      throw error;
    }
  }

  /**
   * 更新个人资料（用户自己）
   */
  static async updateProfile(
    userId: string, 
    updateData: ProfileUpdateData,
    ipAddress?: string,
    userAgent?: string
  ): Promise<IUser> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 记录更新前的数据用于日志
      const changedFields: string[] = [];
      
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof ProfileUpdateData] !== undefined) {
          changedFields.push(key);
        }
      });

      // 更新用户信息
      Object.assign(user, updateData);
      await user.save();

      // 记录活动
      await UserActivity.createActivity(
        userId,
        ActivityType.PROFILE_UPDATE,
        '更新个人资料',
        {
          description: `更新了以下字段: ${changedFields.join(', ')}`,
          metadata: { updatedFields: changedFields },
          ipAddress,
          userAgent
        }
      );

      // 返回更新后的用户信息
      const updatedUser = await User.findById(userId)
        .select('-password -refreshTokens -emailVerificationToken -passwordResetToken -phoneVerificationCode');

      logger.info(`Profile updated: ${user.email}, fields: ${changedFields.join(', ')}`);
      return updatedUser!;

    } catch (error) {
      logger.error('Update profile failed:', error);
      throw error;
    }
  }

  /**
   * 更新用户偏好设置
   */
  static async updatePreferences(
    userId: string,
    updateData: PreferencesUpdateData,
    ipAddress?: string,
    userAgent?: string
  ): Promise<IUser> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 更新偏好设置
      if (updateData.notificationPreferences) {
        user.notificationPreferences = {
          ...user.notificationPreferences,
          ...updateData.notificationPreferences
        };
      }

      if (updateData.language) user.language = updateData.language;
      if (updateData.timezone) user.timezone = updateData.timezone;
      if (updateData.preferredStyles) user.preferredStyles = updateData.preferredStyles;
      if (updateData.goals) user.goals = updateData.goals;

      await user.save();

      // 记录活动
      await UserActivity.createActivity(
        userId,
        ActivityType.PREFERENCES_UPDATE,
        '更新偏好设置',
        {
          description: '用户更新了偏好设置',
          metadata: updateData,
          ipAddress,
          userAgent
        }
      );

      // 返回更新后的用户信息
      const updatedUser = await User.findById(userId)
        .select('-password -refreshTokens -emailVerificationToken -passwordResetToken -phoneVerificationCode');

      logger.info(`Preferences updated: ${user.email}`);
      return updatedUser!;

    } catch (error) {
      logger.error('Update preferences failed:', error);
      throw error;
    }
  }

  /**
   * 更改密码
   */
  static async changePassword(
    userId: string,
    passwordData: PasswordChangeData,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findById(userId).select('+password');
      if (!user) {
        throw new Error('用户不存在');
      }

      // 验证当前密码
      const isCurrentPasswordValid = await user.comparePassword(passwordData.currentPassword);
      if (!isCurrentPasswordValid) {
        throw new Error('当前密码不正确');
      }

      // 检查新密码是否与当前密码相同
      const isSamePassword = await bcrypt.compare(passwordData.newPassword, user.password);
      if (isSamePassword) {
        throw new Error('新密码不能与当前密码相同');
      }

      // 更新密码
      user.password = passwordData.newPassword;
      
      // 清除所有刷新令牌，强制重新登录
      user.clearAllRefreshTokens();
      
      await user.save();

      // 记录活动
      await UserActivity.createActivity(
        userId,
        ActivityType.PASSWORD_CHANGE,
        '修改密码',
        {
          description: '用户成功修改了密码',
          ipAddress,
          userAgent
        }
      );

      logger.info(`Password changed: ${user.email}`);

    } catch (error) {
      logger.error('Change password failed:', error);
      throw error;
    }
  }

  /**
   * 上传头像
   */
  static async uploadAvatar(
    userId: string,
    avatarUrl: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<IUser> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 更新头像
      user.avatar = avatarUrl;
      await user.save();

      // 记录活动
      await UserActivity.createActivity(
        userId,
        ActivityType.AVATAR_UPLOAD,
        '上传头像',
        {
          description: '用户上传了新头像',
          metadata: { avatarUrl },
          ipAddress,
          userAgent
        }
      );

      // 返回更新后的用户信息
      const updatedUser = await User.findById(userId)
        .select('-password -refreshTokens -emailVerificationToken -passwordResetToken -phoneVerificationCode');

      logger.info(`Avatar uploaded: ${user.email}`);
      return updatedUser!;

    } catch (error) {
      logger.error('Upload avatar failed:', error);
      throw error;
    }
  }

  /**
   * 获取用户活动历史
   */
  static async getUserActivities(
    userId: string,
    options: {
      type?: ActivityType;
      page?: number;
      limit?: number;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ) {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const result = await UserActivity.getUserActivities(userId, options);

      logger.info(`User activities retrieved: ${userId}, count: ${result.data.length}`);
      return result;

    } catch (error) {
      logger.error('Get user activities failed:', error);
      throw error;
    }
  }

  /**
   * 删除个人账户（软删除）
   */
  static async deleteAccount(
    userId: string,
    password: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        throw new Error('无效的用户ID');
      }

      const user = await User.findById(userId).select('+password');
      if (!user) {
        throw new Error('用户不存在');
      }

      // 验证密码
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        throw new Error('密码不正确');
      }

      // 记录活动（在删除前）
      await UserActivity.createActivity(
        userId,
        ActivityType.OTHER,
        '删除账户',
        {
          description: '用户主动删除了自己的账户',
          ipAddress,
          userAgent
        }
      );

      // 软删除用户
      user.deletedAt = new Date();
      user.isActive = false;
      user.clearAllRefreshTokens();
      await user.save();

      logger.info(`Account deleted: ${user.email}`);

    } catch (error) {
      logger.error('Delete account failed:', error);
      throw error;
    }
  }
} 