import { User, IUser } from '@/models/User';
import { generateAccessToken, generateRefreshToken, verifyRefreshToken } from '@/utils/jwt-simple';
import logger from '@/utils/logger';
import { UserRole } from '@/utils/types';

export interface RegisterData {
  email: string;
  password: string;
  username: string;
  displayName?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
}

export interface LoginData {
  emailOrUsername: string;
  password: string;
}

export interface AuthResponse {
  user: Partial<IUser>;
  accessToken: string;
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
}

export class AuthService {
  /**
   * 用户注册
   */
  static async register(userData: RegisterData): Promise<AuthResponse> {
    try {
      // 检查邮箱是否已存在
      const existingEmailUser = await User.findOne({ email: userData.email.toLowerCase() });
      if (existingEmailUser) {
        throw new Error('邮箱已被注册');
      }

      // 检查用户名是否已存在
      const existingUsernameUser = await User.findOne({ username: userData.username });
      if (existingUsernameUser) {
        throw new Error('用户名已被使用');
      }

      // 检查手机号是否已存在（如果提供）
      if (userData.phone) {
        const existingPhoneUser = await User.findOne({ phone: userData.phone });
        if (existingPhoneUser) {
          throw new Error('手机号已被注册');
        }
      }

      // 创建新用户
      const newUser = new User({
        email: userData.email.toLowerCase(),
        password: userData.password, // 将由pre-save中间件加密
        username: userData.username,
        displayName: userData.displayName,
        firstName: userData.firstName,
        lastName: userData.lastName,
        phone: userData.phone,
        role: UserRole.USER,
        isActive: true,
        isEmailVerified: false // 需要邮箱验证
      });

      // 生成邮箱验证token
      const emailVerificationToken = newUser.generateEmailVerificationToken();
      
      // 保存用户
      await newUser.save();

      // 生成JWT tokens
      const accessToken = generateAccessToken(
        newUser.id,
        newUser.email,
        newUser.role,
        newUser.isEmailVerified,
        newUser.isActive
      );

      const refreshToken = generateRefreshToken(
        newUser.id,
        newUser.email
      );

      // 保存refresh token
      newUser.addRefreshToken(refreshToken);
      await newUser.save();

      // TODO: 发送邮箱验证邮件
      logger.info(`New user registered: ${newUser.email}, verification token: ${emailVerificationToken}`);

      // 返回用户信息和tokens（不包含敏感信息）
      const userResponse = newUser.toJSON();
      
      return {
        user: userResponse,
        accessToken,
        refreshToken
      };

    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  }

  /**
   * 用户登录
   */
  static async login(loginData: LoginData): Promise<AuthResponse> {
    try {
      // 查找用户（邮箱或用户名）
      const user = await User.findOne({
        $or: [
          { email: loginData.emailOrUsername.toLowerCase() },
          { username: loginData.emailOrUsername }
        ]
      }).select('+password +refreshTokens');

      if (!user) {
        throw new Error('用户不存在');
      }

      // 检查用户是否被锁定
      if (user.isLocked()) {
        throw new Error('账户已被锁定，请稍后再试');
      }

      // 检查用户是否被禁用
      if (!user.isActive) {
        throw new Error('账户已被禁用');
      }

      // 验证密码
      const isPasswordValid = await user.comparePassword(loginData.password);
      if (!isPasswordValid) {
        // 增加失败登录尝试次数
        await (User as any).handleFailedLogin(user.id);
        throw new Error('密码错误');
      }

      // 重置登录尝试次数
      await (User as any).resetLoginAttempts(user.id);

      // 生成新的JWT tokens
      const accessToken = generateAccessToken(
        user.id,
        user.email,
        user.role,
        user.isEmailVerified,
        user.isActive
      );

      const refreshToken = generateRefreshToken(
        user.id,
        user.email
      );

      // 保存新的refresh token
      user.addRefreshToken(refreshToken);
      await user.save();

      logger.info(`User logged in: ${user.email}`);

      // 返回用户信息和tokens
      const userResponse = user.toJSON();
      
      return {
        user: userResponse,
        accessToken,
        refreshToken
      };

    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  }

  /**
   * 刷新访问令牌
   */
  static async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      // 验证refresh token
      const decoded = verifyRefreshToken(refreshToken);

      // 查找用户并检查refresh token是否有效
      const user = await User.findById(decoded.userId).select('+refreshTokens');
      if (!user) {
        throw new Error('用户不存在');
      }

      if (!user.isActive) {
        throw new Error('账户已被禁用');
      }

      // 检查refresh token是否在用户的有效token列表中
      if (!user.refreshTokens || !user.refreshTokens.includes(refreshToken)) {
        throw new Error('无效的刷新令牌');
      }

      // 生成新的tokens
      const newAccessToken = generateAccessToken(
        user.id,
        user.email,
        user.role,
        user.isEmailVerified,
        user.isActive
      );

      const newRefreshToken = generateRefreshToken(
        user.id,
        user.email
      );

      // 移除旧的refresh token，添加新的
      user.removeRefreshToken(refreshToken);
      user.addRefreshToken(newRefreshToken);
      await user.save();

      logger.info(`Token refreshed for user: ${user.email}`);

      return {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken
      };

    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw error;
    }
  }

  /**
   * 用户退出登录
   */
  static async logout(userId: string, refreshToken?: string): Promise<void> {
    try {
      const user = await User.findById(userId).select('+refreshTokens');
      if (!user) {
        throw new Error('用户不存在');
      }

      if (refreshToken) {
        // 移除特定的refresh token
        user.removeRefreshToken(refreshToken);
      } else {
        // 清除所有refresh tokens（全局退出）
        user.clearAllRefreshTokens();
      }

      await user.save();
      logger.info(`User logged out: ${user.email}`);

    } catch (error) {
      logger.error('Logout failed:', error);
      throw error;
    }
  }

  /**
   * 验证邮箱
   */
  static async verifyEmail(token: string): Promise<void> {
    try {
      const user = await User.findOne({
        emailVerificationToken: token,
        emailVerificationExpires: { $gt: new Date() }
      });

      if (!user) {
        throw new Error('邮箱验证链接无效或已过期');
      }

      user.isEmailVerified = true;
      user.emailVerificationToken = undefined;
      user.emailVerificationExpires = undefined;
      
      await user.save();
      logger.info(`Email verified for user: ${user.email}`);

    } catch (error) {
      logger.error('Email verification failed:', error);
      throw error;
    }
  }

  /**
   * 发送密码重置邮件
   */
  static async forgotPassword(email: string): Promise<void> {
    try {
      const user = await User.findOne({ email: email.toLowerCase() });
      if (!user) {
        // 为了安全，即使用户不存在也返回成功消息
        logger.warn(`Password reset requested for non-existent email: ${email}`);
        return;
      }

      if (!user.isActive) {
        throw new Error('账户已被禁用');
      }

      // 生成密码重置token
      const resetToken = user.generatePasswordResetToken();
      await user.save();

      // TODO: 发送密码重置邮件
      logger.info(`Password reset token generated for user: ${user.email}, token: ${resetToken}`);

    } catch (error) {
      logger.error('Forgot password failed:', error);
      throw error;
    }
  }

  /**
   * 重置密码
   */
  static async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      const user = await User.findOne({
        passwordResetToken: token,
        passwordResetExpires: { $gt: new Date() }
      });

      if (!user) {
        throw new Error('密码重置链接无效或已过期');
      }

      user.password = newPassword; // 将由pre-save中间件加密
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      
      // 清除所有refresh tokens，强制重新登录
      user.clearAllRefreshTokens();
      
      await user.save();
      logger.info(`Password reset successful for user: ${user.email}`);

    } catch (error) {
      logger.error('Password reset failed:', error);
      throw error;
    }
  }

  /**
   * 修改密码（已登录用户）
   */
  static async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    try {
      const user = await User.findById(userId).select('+password +refreshTokens');
      if (!user) {
        throw new Error('用户不存在');
      }

      // 验证当前密码
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        throw new Error('当前密码错误');
      }

      user.password = newPassword; // 将由pre-save中间件加密
      
      // 清除所有refresh tokens，强制重新登录
      user.clearAllRefreshTokens();
      
      await user.save();
      logger.info(`Password changed for user: ${user.email}`);

    } catch (error) {
      logger.error('Change password failed:', error);
      throw error;
    }
  }

  /**
   * 获取用户信息
   */
  static async getUserProfile(userId: string): Promise<Partial<IUser>> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      return user.toJSON();

    } catch (error) {
      logger.error('Get user profile failed:', error);
      throw error;
    }
  }

  /**
   * 更新用户信息
   */
  static async updateUserProfile(userId: string, updateData: Partial<IUser>): Promise<Partial<IUser>> {
    try {
      // 防止更新敏感字段
      const allowedFields = [
        'displayName', 'firstName', 'lastName', 'gender', 'dateOfBirth',
        'phone', 'address', 'yogaExperience', 'preferredStyles', 
        'healthConditions', 'goals', 'notificationPreferences', 
        'language', 'timezone'
      ];

      const filteredData: any = {};
      allowedFields.forEach(field => {
        if (updateData[field as keyof IUser] !== undefined) {
          filteredData[field] = updateData[field as keyof IUser];
        }
      });

      const user = await User.findByIdAndUpdate(
        userId,
        filteredData,
        { new: true, runValidators: true }
      );

      if (!user) {
        throw new Error('用户不存在');
      }

      logger.info(`User profile updated: ${user.email}`);
      return user.toJSON();

    } catch (error) {
      logger.error('Update user profile failed:', error);
      throw error;
    }
  }

  /**
   * 删除用户账户（软删除）
   */
  static async deleteAccount(userId: string): Promise<void> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      // 软删除
      user.deletedAt = new Date();
      user.isActive = false;
      user.clearAllRefreshTokens();
      
      await user.save();
      logger.info(`User account deleted: ${user.email}`);

    } catch (error) {
      logger.error('Delete account failed:', error);
      throw error;
    }
  }
} 