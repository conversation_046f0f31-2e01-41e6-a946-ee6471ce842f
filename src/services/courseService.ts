import { Course, ICourse, CourseType, CourseDifficulty, CourseStatus } from '@/models/Course';
import { Instructor } from '@/models/Instructor';
import { UserActivity, ActivityType } from '@/models/UserActivity';
import { PaginationParams, PaginationResult } from '@/utils/types';
import logger from '@/utils/logger';
import mongoose from 'mongoose';

// 课程创建数据接口
export interface CreateCourseData {
  title: string;
  type: CourseType;
  difficulty: CourseDifficulty;
  content: {
    description: string;
    outline: string[];
    benefits: string[];
    requirements: string[];
    equipment: string[];
    contraindications: string[];
  };
  schedule: {
    startTime: Date;
    endTime: Date;
    duration: number;
    capacity: number;
    isRecurring: boolean;
    recurringPattern?: {
      frequency: 'daily' | 'weekly' | 'monthly';
      interval: number;
      daysOfWeek?: number[];
      endDate?: Date;
    };
  };
  price: {
    basePrice: number;
    currency: string;
    discountPrice?: number;
    discountEndDate?: Date;
    isPackage: boolean;
    packageDetails?: {
      sessionsCount: number;
      validityDays: number;
    };
  };
  location: {
    type: 'online' | 'offline' | 'hybrid';
    address?: string;
    room?: string;
    platform?: string;
    meetingLink?: string;
  };
  media?: {
    images?: string[];
    videos?: string[];
    thumbnail?: string;
  };
}

// 课程更新数据接口
export interface UpdateCourseData extends Partial<CreateCourseData> {
  slug?: string;
  status?: CourseStatus;
  isActive?: boolean;
  isFeatured?: boolean;
  allowWaitlist?: boolean;
}

// 课程查询接口
export interface CourseListQuery {
  search?: string;
  type?: CourseType;
  difficulty?: CourseDifficulty;
  status?: CourseStatus;
  instructorId?: string;
  minPrice?: number;
  maxPrice?: number;
  startDate?: Date;
  endDate?: Date;
  location?: 'online' | 'offline' | 'hybrid';
  isActive?: boolean;
  isFeatured?: boolean;
  availableOnly?: boolean;
}

export class CourseService {
  /**
   * 创建课程
   */
  static async createCourse(
    instructorId: string,
    courseData: CreateCourseData,
    creatorUserId?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<ICourse> {
    try {
      // 验证教练员存在
      const instructor = await Instructor.findById(instructorId);
      if (!instructor) {
        throw new Error('教练员不存在');
      }

      // 生成slug
      const slug = this.generateSlug(courseData.title);
      logger.info(`Generated slug for course: ${slug}`);
      
      // 确保slug唯一
      const existingCourse = await Course.findOne({ slug });
      if (existingCourse) {
        throw new Error('课程标题已存在，请使用不同的标题');
      }

      // 创建课程
      const course = new Course({
        ...courseData,
        slug,
        instructorId,
        media: {
          images: courseData.media?.images || [],
          videos: courseData.media?.videos || [],
          thumbnail: courseData.media?.thumbnail
        },
        stats: {
          totalEnrollments: 0,
          completedSessions: 0,
          averageRating: 0,
          totalReviews: 0,
          viewCount: 0
        }
      });

      await course.save();

      // 记录活动
      if (creatorUserId) {
        await UserActivity.createActivity(
          creatorUserId,
          ActivityType.OTHER,
          '创建课程',
          {
            description: `创建了新课程: ${courseData.title}`,
            metadata: { courseId: course._id, courseTitle: courseData.title },
            ipAddress,
            userAgent
          }
        );
      }

      logger.info(`Course created: ${course.title} by instructor ${instructorId}`);
      return course;

    } catch (error) {
      logger.error('Create course failed:', error);
      throw error;
    }
  }

  /**
   * 获取课程列表
   */
  static async getCourseList(
    query: CourseListQuery,
    pagination: PaginationParams
  ): Promise<PaginationResult<ICourse>> {
    try {
      // 构建查询条件
      const filter: any = {};

      // 搜索条件
      if (query.search) {
        filter.$or = [
          { title: { $regex: query.search, $options: 'i' } },
          { 'content.description': { $regex: query.search, $options: 'i' } }
        ];
      }

      // 基础筛选
      if (query.type) filter.type = query.type;
      if (query.difficulty) filter.difficulty = query.difficulty;
      if (query.status) filter.status = query.status;
      if (query.instructorId) filter.instructorId = query.instructorId;
      if (query.location) filter['location.type'] = query.location;
      if (query.isActive !== undefined) filter.isActive = query.isActive;
      if (query.isFeatured !== undefined) filter.isFeatured = query.isFeatured;

      // 价格范围筛选
      if (query.minPrice || query.maxPrice) {
        filter['price.basePrice'] = {};
        if (query.minPrice) filter['price.basePrice'].$gte = query.minPrice;
        if (query.maxPrice) filter['price.basePrice'].$lte = query.maxPrice;
      }

      // 时间范围筛选
      if (query.startDate || query.endDate) {
        filter['schedule.startTime'] = {};
        if (query.startDate) filter['schedule.startTime'].$gte = query.startDate;
        if (query.endDate) filter['schedule.startTime'].$lte = query.endDate;
      }

      // 只显示可预订的课程
      if (query.availableOnly) {
        filter.isActive = true;
        filter.status = CourseStatus.PUBLISHED;
        filter['schedule.startTime'] = { $gt: new Date() };
        filter.$expr = { $lt: ['$schedule.enrolledCount', '$schedule.capacity'] };
      }

      // 计算总数
      const total = await Course.countDocuments(filter);

      // 分页设置
      const skip = (pagination.page - 1) * pagination.limit;
      const totalPages = Math.ceil(total / pagination.limit);

      // 排序
      const sortField = pagination.sortBy || 'createdAt';
      const sortOrder = pagination.sortOrder === 'asc' ? 1 : -1;
      const sort: Record<string, 1 | -1> = { [sortField]: sortOrder };

      // 查询课程列表
      const courses = await Course.find(filter)
        .populate('instructorId', 'displayName profileImage specialties averageRating')
        .sort(sort)
        .skip(skip)
        .limit(pagination.limit)
        .lean();

      logger.info(`Course list query: ${courses.length} courses found, total: ${total}`);

      return {
        data: courses as ICourse[],
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages,
          hasNext: pagination.page < totalPages,
          hasPrev: pagination.page > 1
        }
      };

    } catch (error) {
      logger.error('Get course list failed:', error);
      throw error;
    }
  }

  /**
   * 获取课程详情
   */
  static async getCourseById(courseId: string): Promise<ICourse> {
    try {
      if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new Error('无效的课程ID');
      }

      const course = await Course.findById(courseId)
        .populate('instructorId', 'displayName profileImage bio specialties experience averageRating totalReviews');

      if (!course) {
        throw new Error('课程不存在');
      }

      // 增加浏览量
      course.stats.viewCount += 1;
      await course.save();

      logger.info(`Course details retrieved: ${course.title}`);
      return course;

    } catch (error) {
      logger.error('Get course by ID failed:', error);
      throw error;
    }
  }

  /**
   * 更新课程
   */
  static async updateCourse(
    courseId: string,
    updateData: UpdateCourseData,
    updaterUserId?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<ICourse> {
    try {
      if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new Error('无效的课程ID');
      }

      const course = await Course.findById(courseId);
      if (!course) {
        throw new Error('课程不存在');
      }

      // 如果更新标题，需要重新生成slug
      if (updateData.title && updateData.title !== course.title) {
        const newSlug = this.generateSlug(updateData.title);
        const existingCourse = await Course.findOne({ slug: newSlug, _id: { $ne: courseId } });
        if (existingCourse) {
          throw new Error('课程标题已存在，请使用不同的标题');
        }
        updateData.slug = newSlug;
      }

      // 更新课程信息
      Object.assign(course, updateData);
      await course.save();

      // 记录活动
      if (updaterUserId) {
        await UserActivity.createActivity(
          updaterUserId,
          ActivityType.OTHER,
          '更新课程',
          {
            description: `更新了课程: ${course.title}`,
            metadata: { courseId: course._id, courseTitle: course.title },
            ipAddress,
            userAgent
          }
        );
      }

      const updatedCourse = await Course.findById(courseId)
        .populate('instructorId', 'displayName profileImage specialties');

      logger.info(`Course updated: ${course.title}`);
      return updatedCourse!;

    } catch (error) {
      logger.error('Update course failed:', error);
      throw error;
    }
  }

  /**
   * 删除课程（软删除）
   */
  static async deleteCourse(
    courseId: string,
    deleterUserId?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new Error('无效的课程ID');
      }

      const course = await Course.findById(courseId);
      if (!course) {
        throw new Error('课程不存在');
      }

      // 检查是否有人报名
      if (course.schedule.enrolledCount > 0) {
        throw new Error('已有用户报名的课程不能删除');
      }

      // 软删除
      course.deletedAt = new Date();
      course.isActive = false;
      await course.save();

      // 记录活动
      if (deleterUserId) {
        await UserActivity.createActivity(
          deleterUserId,
          ActivityType.OTHER,
          '删除课程',
          {
            description: `删除了课程: ${course.title}`,
            metadata: { courseId: course._id, courseTitle: course.title },
            ipAddress,
            userAgent
          }
        );
      }

      logger.info(`Course deleted: ${course.title}`);

    } catch (error) {
      logger.error('Delete course failed:', error);
      throw error;
    }
  }

  /**
   * 获取教练员的课程列表
   */
  static async getInstructorCourses(
    instructorId: string,
    status?: CourseStatus,
    pagination?: PaginationParams
  ): Promise<PaginationResult<ICourse> | ICourse[]> {
    try {
      if (!mongoose.Types.ObjectId.isValid(instructorId)) {
        throw new Error('无效的教练员ID');
      }

      const filter: any = { instructorId };
      if (status) filter.status = status;

      if (pagination) {
        const total = await Course.countDocuments(filter);
        const skip = (pagination.page - 1) * pagination.limit;
        const totalPages = Math.ceil(total / pagination.limit);

        const courses = await Course.find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(pagination.limit)
          .lean();

        return {
          data: courses as ICourse[],
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total,
            totalPages,
            hasNext: pagination.page < totalPages,
            hasPrev: pagination.page > 1
          }
        };
      } else {
        const courses = await Course.find(filter).sort({ createdAt: -1 }).lean();
        return courses as ICourse[];
      }

    } catch (error) {
      logger.error('Get instructor courses failed:', error);
      throw error;
    }
  }

  /**
   * 获取课程统计信息
   */
  static async getCourseStats(instructorId?: string) {
    try {
      const filter: any = {};
      if (instructorId) filter.instructorId = instructorId;

      const stats = await Course.aggregate([
        { $match: filter },
        {
          $group: {
            _id: null,
            totalCourses: { $sum: 1 },
            publishedCourses: {
              $sum: { $cond: [{ $eq: ['$status', CourseStatus.PUBLISHED] }, 1, 0] }
            },
            totalEnrollments: { $sum: '$stats.totalEnrollments' },
            totalRevenue: { $sum: '$price.basePrice' },
            avgRating: { $avg: '$stats.averageRating' },
            totalReviews: { $sum: '$stats.totalReviews' }
          }
        }
      ]);

      const typeStats = await Course.aggregate([
        { $match: filter },
        { $group: { _id: '$type', count: { $sum: 1 } } }
      ]);

      const difficultyStats = await Course.aggregate([
        { $match: filter },
        { $group: { _id: '$difficulty', count: { $sum: 1 } } }
      ]);

      return {
        overview: stats[0] || {
          totalCourses: 0,
          publishedCourses: 0,
          totalEnrollments: 0,
          totalRevenue: 0,
          avgRating: 0,
          totalReviews: 0
        },
        typeDistribution: typeStats,
        difficultyDistribution: difficultyStats
      };

    } catch (error) {
      logger.error('Get course stats failed:', error);
      throw error;
    }
  }

  /**
   * 搜索课程
   */
  static async searchCourses(
    searchTerm: string,
    filters?: Partial<CourseListQuery>,
    pagination?: PaginationParams
  ): Promise<PaginationResult<ICourse>> {
    try {
      const query: CourseListQuery = {
        search: searchTerm,
        ...filters
      };

      return await this.getCourseList(query, pagination || {
        page: 1,
        limit: 20,
        sortBy: 'stats.averageRating',
        sortOrder: 'desc'
      });

    } catch (error) {
      logger.error('Search courses failed:', error);
      throw error;
    }
  }

  /**
   * 获取推荐课程
   */
  static async getFeaturedCourses(limit: number = 10): Promise<ICourse[]> {
    try {
      const courses = await Course.find({
        isActive: true,
        isFeatured: true,
        status: CourseStatus.PUBLISHED,
        'schedule.startTime': { $gt: new Date() }
      })
        .populate('instructorId', 'displayName profileImage specialties averageRating')
        .sort({ 'stats.averageRating': -1, createdAt: -1 })
        .limit(limit)
        .lean();

      return courses as ICourse[];

    } catch (error) {
      logger.error('Get featured courses failed:', error);
      throw error;
    }
  }

  /**
   * 生成课程slug
   */
  private static generateSlug(title: string): string {
    // 使用时间戳确保唯一性
    const timestamp = Date.now();
    
    // 简单的中文到拼音映射（有限的映射，主要处理常见瑜伽术语）
    const chineseToEnglishMap: { [key: string]: string } = {
      '初级': 'beginner',
      '中级': 'intermediate', 
      '高级': 'advanced',
      '瑜伽': 'yoga',
      '哈达': 'hatha',
      '流': 'vinyasa',
      '阴': 'yin',
      '力量': 'power',
      '修复': 'restorative',
      '热': 'hot',
      '课程': 'course',
      '体式': 'asana',
      '冥想': 'meditation',
      '呼吸': 'pranayama'
    };
    
    // 先尝试翻译常见的中文词汇
    let processedTitle = title;
    Object.keys(chineseToEnglishMap).forEach(chinese => {
      const english = chineseToEnglishMap[chinese];
      processedTitle = processedTitle.replace(new RegExp(chinese, 'g'), english);
    });
    
    // 处理剩余字符，保留英文、数字，移除其他字符
    let slug = processedTitle
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // 只保留英文、数字、空格和连字符
      .replace(/\s+/g, '-') // 空格替换为连字符
      .replace(/--+/g, '-') // 多个连字符替换为单个
      .trim();
    
    // 如果slug为空或者只包含连字符，使用默认名称
    if (!slug || slug === '-' || /^-+$/.test(slug)) {
      slug = 'course';
    }
    
    // 添加时间戳后缀确保唯一性
    slug = `${slug}-${timestamp}`;
    
    return slug.slice(0, 100); // 限制长度
  }
} 