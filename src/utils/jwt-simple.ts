import jwt from 'jsonwebtoken';
import { config } from '../config';
import { JwtPayload } from './types';
import logger from './logger';

/**
 * 生成访问令牌 - 简化版本
 */
export const generateAccessToken = (userId: string, email: string, role?: string, isEmailVerified?: boolean, isActive?: boolean): string => {
  try {
    const payload = {
      userId,
      email,
      role: role || 'user',
      isEmailVerified: isEmailVerified || false,
      isActive: isActive !== false
    };
    
    // 暂时使用简化的签名方式
    return jwt.sign(payload, config.jwt.secret);
  } catch (error) {
    logger.error('Failed to generate access token:', error);
    throw new Error('Token generation failed');
  }
};

/**
 * 生成刷新令牌 - 简化版本
 */
export const generateRefreshToken = (userId: string, email: string): string => {
  try {
    const payload = {
      userId,
      email,
      type: 'refresh'
    };
    
    // 暂时使用简化的签名方式
    return jwt.sign(payload, config.jwt.refreshSecret);
  } catch (error) {
    logger.error('Failed to generate refresh token:', error);
    throw new Error('Refresh token generation failed');
  }
};

/**
 * 验证访问令牌
 */
export const verifyAccessToken = (token: string): JwtPayload => {
  try {
    const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Access token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid access token');
    } else {
      logger.error('Token verification failed:', error);
      throw new Error('Token verification failed');
    }
  }
};

/**
 * 验证刷新令牌
 */
export const verifyRefreshToken = (token: string): JwtPayload => {
  try {
    const decoded = jwt.verify(token, config.jwt.refreshSecret) as JwtPayload;
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('Refresh token expired');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('Invalid refresh token');
    } else {
      logger.error('Refresh token verification failed:', error);
      throw new Error('Refresh token verification failed');
    }
  }
};

/**
 * 从Authorization头中提取token
 */
export const extractTokenFromHeader = (authHeader?: string): string | null => {
  if (!authHeader) {
    return null;
  }

  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }

  return parts[1];
}; 