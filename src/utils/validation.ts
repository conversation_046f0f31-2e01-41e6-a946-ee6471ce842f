import Joi from 'joi';

// 用户注册验证模式
export const registerSchema = Joi.object({
  email: Joi.string()
    .email()
    .max(255)
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'string.max': '邮箱地址不能超过255个字符',
      'any.required': '邮箱地址是必填项'
    }),
  
  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .required()
    .messages({
      'string.min': '密码至少需要8个字符',
      'string.max': '密码不能超过128个字符',
      'string.pattern.base': '密码必须包含大小写字母、数字和特殊字符',
      'any.required': '密码是必填项'
    }),
  
  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': '确认密码必须与密码相同',
      'any.required': '确认密码是必填项'
    }),
  
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过30个字符',
      'any.required': '用户名是必填项'
    }),
  
  phone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .optional()
    .messages({
      'string.pattern.base': '请输入有效的手机号码'
    }),
  
  agreeToTerms: Joi.boolean()
    .valid(true)
    .required()
    .messages({
      'any.only': '请同意服务条款',
      'any.required': '请同意服务条款'
    })
});

// 用户登录验证模式
export const loginSchema = Joi.object({
  emailOrUsername: Joi.string()
    .min(1)
    .required()
    .messages({
      'string.min': '请输入邮箱或用户名',
      'any.required': '邮箱或用户名是必填项'
    }),
  
  password: Joi.string()
    .min(1)
    .required()
    .messages({
      'string.min': '请输入密码',
      'any.required': '密码是必填项'
    }),
  
  rememberMe: Joi.boolean()
    .optional()
    .default(false)
});

// 忘记密码验证模式
export const forgotPasswordSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': '请输入有效的邮箱地址',
      'any.required': '邮箱地址是必填项'
    })
});

// 重置密码验证模式
export const resetPasswordSchema = Joi.object({
  token: Joi.string()
    .required()
    .messages({
      'any.required': '重置令牌是必需的'
    }),
  
  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .required()
    .messages({
      'string.min': '密码至少需要8个字符',
      'string.max': '密码不能超过128个字符',
      'string.pattern.base': '密码必须包含大小写字母、数字和特殊字符',
      'any.required': '密码是必填项'
    }),
  
  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': '确认密码必须与密码相同',
      'any.required': '确认密码是必填项'
    })
});

// 更新个人信息验证模式
export const updateProfileSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .optional()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过30个字符'
    }),
  
  phone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .optional()
    .allow('')
    .messages({
      'string.pattern.base': '请输入有效的手机号码'
    }),
  
  firstName: Joi.string()
    .max(50)
    .optional()
    .allow('')
    .messages({
      'string.max': '名字不能超过50个字符'
    }),
  
  lastName: Joi.string()
    .max(50)
    .optional()
    .allow('')
    .messages({
      'string.max': '姓氏不能超过50个字符'
    }),
  
  birthDate: Joi.date()
    .max('now')
    .optional()
    .messages({
      'date.max': '出生日期不能是未来日期'
    }),
  
  gender: Joi.string()
    .valid('male', 'female', 'other')
    .optional()
    .messages({
      'any.only': '性别只能是 male、female 或 other'
    })
});

// 修改密码验证模式
export const changePasswordSchema = Joi.object({
  currentPassword: Joi.string()
    .required()
    .messages({
      'any.required': '当前密码是必填项'
    }),
  
  newPassword: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .required()
    .messages({
      'string.min': '新密码至少需要8个字符',
      'string.max': '新密码不能超过128个字符',
      'string.pattern.base': '新密码必须包含大小写字母、数字和特殊字符',
      'any.required': '新密码是必填项'
    }),
  
  confirmNewPassword: Joi.string()
    .valid(Joi.ref('newPassword'))
    .required()
    .messages({
      'any.only': '确认新密码必须与新密码相同',
      'any.required': '确认新密码是必填项'
    })
});

// 分页参数验证模式
export const paginationSchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于1'
    }),
  
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.base': '每页条数必须是数字',
      'number.integer': '每页条数必须是整数',
      'number.min': '每页条数不能小于1',
      'number.max': '每页条数不能超过100'
    }),
  
  sort: Joi.string()
    .optional()
    .messages({
      'string.base': '排序字段必须是字符串'
    }),
  
  order: Joi.string()
    .valid('asc', 'desc')
    .default('desc')
    .messages({
      'any.only': '排序方向只能是 asc 或 desc'
    })
});

// 用户列表查询验证
export const userListQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sortBy: Joi.string().valid('createdAt', 'updatedAt', 'email', 'username', 'role', 'isActive').default('createdAt'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  search: Joi.string().min(1).max(100),
  role: Joi.string().valid('user', 'instructor', 'admin', 'super_admin'),
  isActive: Joi.boolean(),
  isEmailVerified: Joi.boolean(),
  dateFrom: Joi.date(),
  dateTo: Joi.date().greater(Joi.ref('dateFrom'))
});

// 管理员更新用户信息验证
export const adminUpdateUserSchema = Joi.object({
  displayName: Joi.string().min(1).max(50),
  firstName: Joi.string().min(1).max(50),
  lastName: Joi.string().min(1).max(50),
  phone: Joi.string().pattern(/^[+]?[1-9]\d{1,14}$/),
  gender: Joi.string().valid('male', 'female', 'other'),
  dateOfBirth: Joi.date().max('now'),
  address: Joi.object({
    street: Joi.string().max(200),
    city: Joi.string().max(100),
    state: Joi.string().max(100),
    zipCode: Joi.string().max(20),
    country: Joi.string().max(100)
  }),
  yogaExperience: Joi.string().valid('beginner', 'intermediate', 'advanced', 'expert'),
  preferredStyles: Joi.array().items(Joi.string().max(50)),
  healthConditions: Joi.array().items(Joi.string().max(200)),
  goals: Joi.array().items(Joi.string().max(200)),
  notificationPreferences: Joi.object({
    email: Joi.boolean(),
    sms: Joi.boolean(),
    push: Joi.boolean(),
    marketing: Joi.boolean()
  }),
  language: Joi.string().valid('zh-CN', 'en-US', 'ja-JP'),
  timezone: Joi.string().max(50)
});

// 更新用户状态验证
export const updateUserStatusSchema = Joi.object({
  isActive: Joi.boolean(),
  isEmailVerified: Joi.boolean()
}).or('isActive', 'isEmailVerified');

// 更新用户角色验证
export const updateUserRoleSchema = Joi.object({
  role: Joi.string().valid('user', 'instructor', 'admin', 'super_admin').required()
});

// 批量操作验证
export const batchUserOperationSchema = Joi.object({
  action: Joi.string().valid('activate', 'deactivate', 'delete', 'verifyEmail').required(),
  userIds: Joi.array().items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/)).min(1).max(100).required()
});

// ObjectId验证
export const objectIdSchema = Joi.object({
  userId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required().messages({
    'string.pattern.base': '用户ID格式无效'
  })
});

// ========== 教练员验证 ==========

// 创建教练员验证
export const createInstructorSchema = Joi.object({
  userId: Joi.string().required().messages({
    'string.empty': '用户ID不能为空',
    'any.required': '用户ID是必填项'
  }),
  displayName: Joi.string().min(2).max(100).required().messages({
    'string.empty': '显示名称不能为空',
    'string.min': '显示名称至少需要2个字符',
    'string.max': '显示名称最多100个字符',
    'any.required': '显示名称是必填项'
  }),
  bio: Joi.string().max(500).optional().allow('').messages({
    'string.max': '个人简介最多500个字符'
  }),
  experience: Joi.number().min(0).max(100).optional().messages({
    'number.min': '教学经验不能为负数',
    'number.max': '教学经验不能超过100年'
  }),
  specialties: Joi.array().items(Joi.string().min(2).max(50)).max(10).optional().messages({
    'array.max': '专长不能超过10个',
    'string.min': '专长名称至少需要2个字符',
    'string.max': '专长名称最多50个字符'
  }),
  profileImage: Joi.string().uri().optional().allow('').messages({
    'string.uri': '头像必须是有效的URL'
  })
});

// 更新教练员验证
export const updateInstructorSchema = Joi.object({
  displayName: Joi.string().min(2).max(100).optional().messages({
    'string.min': '显示名称至少需要2个字符',
    'string.max': '显示名称最多100个字符'
  }),
  bio: Joi.string().max(500).optional().allow('').messages({
    'string.max': '个人简介最多500个字符'
  }),
  experience: Joi.number().min(0).max(100).optional().messages({
    'number.min': '教学经验不能为负数',
    'number.max': '教学经验不能超过100年'
  }),
  specialties: Joi.array().items(Joi.string().min(2).max(50)).max(10).optional().messages({
    'array.max': '专长不能超过10个',
    'string.min': '专长名称至少需要2个字符',
    'string.max': '专长名称最多50个字符'
  }),
  profileImage: Joi.string().uri().optional().allow('').messages({
    'string.uri': '头像必须是有效的URL'
  }),
  isActive: Joi.boolean().optional()
});

// 教练员查询验证
export const instructorQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1).messages({
    'number.min': '页码必须大于0'
  }),
  limit: Joi.number().integer().min(1).max(100).default(10).messages({
    'number.min': '每页数量必须大于0',
    'number.max': '每页数量不能超过100'
  }),
  search: Joi.string().max(100).optional().allow('').messages({
    'string.max': '搜索关键词最多100个字符'
  }),
  specialty: Joi.string().max(50).optional().allow('').messages({
    'string.max': '专长名称最多50个字符'
  }),
  isActive: Joi.boolean().optional(),
  sortBy: Joi.string().valid(
    'displayName', 'experience', 'averageRating', 'totalClasses', 'totalReviews', 'createdAt', 'updatedAt'
  ).default('createdAt').messages({
    'any.only': '排序字段必须是: displayName, experience, averageRating, totalClasses, totalReviews, createdAt, updatedAt'
  }),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
    'any.only': '排序方向必须是: asc 或 desc'
  }),
  minRating: Joi.number().min(0).max(5).optional().messages({
    'number.min': '最低评分不能小于0',
    'number.max': '最低评分不能大于5'
  }),
  maxRating: Joi.number().min(0).max(5).optional().messages({
    'number.min': '最高评分不能小于0',
    'number.max': '最高评分不能大于5'
  }),
  minExperience: Joi.number().min(0).optional().messages({
    'number.min': '最低经验不能小于0'
  }),
  maxExperience: Joi.number().min(0).optional().messages({
    'number.min': '最高经验不能小于0'
  })
}).custom((value, helpers) => {
  // 验证评分范围
  if (value.minRating !== undefined && value.maxRating !== undefined && value.minRating > value.maxRating) {
    return helpers.error('custom.invalidRatingRange');
  }
  
  // 验证经验范围
  if (value.minExperience !== undefined && value.maxExperience !== undefined && value.minExperience > value.maxExperience) {
    return helpers.error('custom.invalidExperienceRange');
  }
  
  return value;
}, '评分和经验范围验证').messages({
  'custom.invalidRatingRange': '最低评分不能大于最高评分',
  'custom.invalidExperienceRange': '最低经验不能大于最高经验'
});

// 教练员状态更新验证
export const instructorStatusSchema = Joi.object({
  isActive: Joi.boolean().required().messages({
    'any.required': '状态是必填项'
  })
});

// 批量操作验证
export const instructorBatchSchema = Joi.object({
  ids: Joi.array().items(Joi.string().regex(/^[0-9a-fA-F]{24}$/)).min(1).max(100).required().messages({
    'array.min': '至少选择一个教练员',
    'array.max': '最多选择100个教练员',
    'string.pattern.base': '教练员ID格式不正确',
    'any.required': '教练员ID列表是必填项'
  }),
  operation: Joi.string().valid('activate', 'deactivate', 'delete').required().messages({
    'any.only': '操作类型必须是: activate, deactivate, delete',
    'any.required': '操作类型是必填项'
  })
});

// 教练员统计更新验证
export const instructorStatsSchema = Joi.object({
  newRating: Joi.number().min(0).max(5).optional().messages({
    'number.min': '评分不能小于0',
    'number.max': '评分不能大于5'
  }),
  newClassCount: Joi.number().integer().min(0).optional().messages({
    'number.min': '课程数量不能小于0',
    'number.integer': '课程数量必须是整数'
  })
}).or('newRating', 'newClassCount').messages({
  'object.missing': '至少需要提供评分或课程数量中的一个'
});

// 个人资料更新验证模式
export const profileUpdateSchema = Joi.object({
  displayName: Joi.string().trim().max(50).optional(),
  firstName: Joi.string().trim().max(50).optional(),
  lastName: Joi.string().trim().max(50).optional(),
  phone: Joi.string().pattern(/^[+]?[1-9]\d{1,14}$/).optional(),
  gender: Joi.string().valid('male', 'female', 'other').optional(),
  dateOfBirth: Joi.date().max('now').optional(),
  address: Joi.object({
    street: Joi.string().trim().max(100).optional(),
    city: Joi.string().trim().max(50).optional(),
    state: Joi.string().trim().max(50).optional(),
    zipCode: Joi.string().trim().max(20).optional(),
    country: Joi.string().trim().max(50).optional()
  }).optional(),
  yogaExperience: Joi.string().valid('beginner', 'intermediate', 'advanced', 'expert').optional(),
  preferredStyles: Joi.array().items(Joi.string().trim().max(50)).max(10).optional(),
  healthConditions: Joi.array().items(Joi.string().trim().max(100)).max(20).optional(),
  goals: Joi.array().items(Joi.string().trim().max(100)).max(10).optional(),
  language: Joi.string().trim().max(10).optional(),
  timezone: Joi.string().trim().max(50).optional()
});

// 偏好设置更新验证模式
export const preferencesUpdateSchema = Joi.object({
  notificationPreferences: Joi.object({
    email: Joi.boolean().optional(),
    sms: Joi.boolean().optional(),
    push: Joi.boolean().optional(),
    marketing: Joi.boolean().optional()
  }).optional(),
  language: Joi.string().trim().max(10).optional(),
  timezone: Joi.string().trim().max(50).optional(),
  preferredStyles: Joi.array().items(Joi.string().trim().max(50)).max(10).optional(),
  goals: Joi.array().items(Joi.string().trim().max(100)).max(10).optional()
});

// 删除账户验证模式
export const deleteAccountSchema = Joi.object({
  password: Joi.string().required().messages({
    'any.required': '请输入密码以确认删除账户',
    'string.empty': '密码不能为空'
  })
});

// 活动历史查询验证模式
export const activitiesQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(20),
  type: Joi.string().valid(
    'register', 'login', 'logout', 'password_change', 'email_verify', 'phone_verify',
    'profile_update', 'avatar_upload', 'preferences_update',
    'course_view', 'course_book', 'course_cancel', 'course_complete',
    'instructor_view', 'instructor_follow', 'instructor_unfollow',
    'review_create', 'review_update', 'review_delete',
    'payment_success', 'payment_failed', 'refund_request',
    'other'
  ).optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().min(Joi.ref('startDate')).optional()
});

// 课程类型和难度枚举
const courseTypes = ['hatha', 'vinyasa', 'yin', 'ashtanga', 'power', 'restorative', 'hot', 'meditation', 'pranayama'];
const courseDifficulties = ['beginner', 'intermediate', 'advanced', 'expert'];
const courseStatuses = ['draft', 'published', 'cancelled', 'completed'];
const locationTypes = ['online', 'offline', 'hybrid'];

// 创建课程验证模式
export const createCourseSchema = Joi.object({
  title: Joi.string().trim().min(5).max(200).required().messages({
    'string.min': '课程标题至少需要5个字符',
    'string.max': '课程标题不能超过200个字符',
    'any.required': '课程标题是必填项'
  }),
  type: Joi.string().valid(...courseTypes).required().messages({
    'any.only': `课程类型必须是: ${courseTypes.join(', ')}`,
    'any.required': '课程类型是必填项'
  }),
  difficulty: Joi.string().valid(...courseDifficulties).required().messages({
    'any.only': `课程难度必须是: ${courseDifficulties.join(', ')}`,
    'any.required': '课程难度是必填项'
  }),
  content: Joi.object({
    description: Joi.string().trim().min(20).max(2000).required().messages({
      'string.min': '课程描述至少需要20个字符',
      'string.max': '课程描述不能超过2000个字符',
      'any.required': '课程描述是必填项'
    }),
    outline: Joi.array().items(Joi.string().trim().max(500)).min(1).max(20).required().messages({
      'array.min': '至少需要一个课程大纲项目',
      'array.max': '课程大纲项目不能超过20个',
      'string.max': '大纲项目不能超过500个字符'
    }),
    benefits: Joi.array().items(Joi.string().trim().max(200)).max(10).optional().messages({
      'array.max': '课程益处不能超过10个',
      'string.max': '益处描述不能超过200个字符'
    }),
    requirements: Joi.array().items(Joi.string().trim().max(200)).max(10).optional().messages({
      'array.max': '参与要求不能超过10个',
      'string.max': '要求描述不能超过200个字符'
    }),
    equipment: Joi.array().items(Joi.string().trim().max(100)).max(15).optional().messages({
      'array.max': '所需设备不能超过15个',
      'string.max': '设备名称不能超过100个字符'
    }),
    contraindications: Joi.array().items(Joi.string().trim().max(200)).max(10).optional().messages({
      'array.max': '禁忌症不能超过10个',
      'string.max': '禁忌症描述不能超过200个字符'
    })
  }).required(),
  schedule: Joi.object({
    startTime: Joi.date().min('now').required().messages({
      'date.min': '课程开始时间不能早于当前时间',
      'any.required': '课程开始时间是必填项'
    }),
    endTime: Joi.date().min(Joi.ref('startTime')).required().messages({
      'date.min': '课程结束时间必须晚于开始时间',
      'any.required': '课程结束时间是必填项'
    }),
    duration: Joi.number().integer().min(15).max(480).required().messages({
      'number.min': '课程时长不能少于15分钟',
      'number.max': '课程时长不能超过8小时',
      'any.required': '课程时长是必填项'
    }),
    capacity: Joi.number().integer().min(1).max(100).required().messages({
      'number.min': '课程容量至少为1',
      'number.max': '课程容量不能超过100',
      'any.required': '课程容量是必填项'
    }),
    isRecurring: Joi.boolean().default(false),
    recurringPattern: Joi.when('isRecurring', {
      is: true,
      then: Joi.object({
        frequency: Joi.string().valid('daily', 'weekly', 'monthly').required(),
        interval: Joi.number().integer().min(1).max(365).required(),
        daysOfWeek: Joi.array().items(Joi.number().integer().min(0).max(6)).max(7).optional(),
        endDate: Joi.date().min(Joi.ref('....schedule.startTime')).optional()
      }).required(),
      otherwise: Joi.forbidden()
    })
  }).required(),
  price: Joi.object({
    basePrice: Joi.number().min(0).max(999999).required().messages({
      'number.min': '价格不能为负数',
      'number.max': '价格不能超过999999',
      'any.required': '课程价格是必填项'
    }),
    currency: Joi.string().valid('CNY', 'USD', 'EUR').default('CNY'),
    discountPrice: Joi.number().min(0).optional(),
    discountEndDate: Joi.date().min('now').optional(),
    isPackage: Joi.boolean().default(false),
    packageDetails: Joi.when('isPackage', {
      is: true,
      then: Joi.object({
        sessionsCount: Joi.number().integer().min(1).max(100).required(),
        validityDays: Joi.number().integer().min(1).max(365).required()
      }).required(),
      otherwise: Joi.forbidden()
    })
  }).required().custom((value, helpers) => {
    if (value.discountPrice && value.discountPrice >= value.basePrice) {
      return helpers.error('custom.discountPrice');
    }
    return value;
  }).messages({
    'custom.discountPrice': '折扣价格必须低于基础价格'
  }),
  location: Joi.object({
    type: Joi.string().valid(...locationTypes).required().messages({
      'any.only': `位置类型必须是: ${locationTypes.join(', ')}`,
      'any.required': '位置类型是必填项'
    }),
    address: Joi.string().trim().max(500).optional(),
    room: Joi.string().trim().max(100).optional(),
    platform: Joi.string().trim().max(100).optional(),
    meetingLink: Joi.string().uri().optional()
  }).required(),
  media: Joi.object({
    images: Joi.array().items(Joi.string().uri()).max(10).optional(),
    videos: Joi.array().items(Joi.string().uri()).max(5).optional(),
    thumbnail: Joi.string().uri().optional()
  }).optional(),
  instructorId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).optional().messages({
    'string.pattern.base': '教练员ID格式不正确'
  })
});

// 更新课程验证模式
export const updateCourseSchema = Joi.object({
  title: Joi.string().trim().min(5).max(200).optional(),
  type: Joi.string().valid(...courseTypes).optional(),
  difficulty: Joi.string().valid(...courseDifficulties).optional(),
  status: Joi.string().valid(...courseStatuses).optional(),
  content: Joi.object({
    description: Joi.string().trim().min(20).max(2000).optional(),
    outline: Joi.array().items(Joi.string().trim().max(500)).min(1).max(20).optional(),
    benefits: Joi.array().items(Joi.string().trim().max(200)).max(10).optional(),
    requirements: Joi.array().items(Joi.string().trim().max(200)).max(10).optional(),
    equipment: Joi.array().items(Joi.string().trim().max(100)).max(15).optional(),
    contraindications: Joi.array().items(Joi.string().trim().max(200)).max(10).optional()
  }).optional(),
  schedule: Joi.object({
    startTime: Joi.date().optional(),
    endTime: Joi.date().optional(),
    duration: Joi.number().integer().min(15).max(480).optional(),
    capacity: Joi.number().integer().min(1).max(100).optional(),
    isRecurring: Joi.boolean().optional(),
    recurringPattern: Joi.object({
      frequency: Joi.string().valid('daily', 'weekly', 'monthly').optional(),
      interval: Joi.number().integer().min(1).max(365).optional(),
      daysOfWeek: Joi.array().items(Joi.number().integer().min(0).max(6)).max(7).optional(),
      endDate: Joi.date().optional()
    }).optional()
  }).optional(),
  price: Joi.object({
    basePrice: Joi.number().min(0).max(999999).optional(),
    currency: Joi.string().valid('CNY', 'USD', 'EUR').optional(),
    discountPrice: Joi.number().min(0).optional(),
    discountEndDate: Joi.date().optional(),
    isPackage: Joi.boolean().optional(),
    packageDetails: Joi.object({
      sessionsCount: Joi.number().integer().min(1).max(100).optional(),
      validityDays: Joi.number().integer().min(1).max(365).optional()
    }).optional()
  }).optional(),
  location: Joi.object({
    type: Joi.string().valid(...locationTypes).optional(),
    address: Joi.string().trim().max(500).optional(),
    room: Joi.string().trim().max(100).optional(),
    platform: Joi.string().trim().max(100).optional(),
    meetingLink: Joi.string().uri().optional()
  }).optional(),
  media: Joi.object({
    images: Joi.array().items(Joi.string().uri()).max(10).optional(),
    videos: Joi.array().items(Joi.string().uri()).max(5).optional(),
    thumbnail: Joi.string().uri().optional()
  }).optional(),
  isActive: Joi.boolean().optional(),
  isFeatured: Joi.boolean().optional(),
  allowWaitlist: Joi.boolean().optional()
});

// 课程列表查询验证模式
export const getCourseListSchema = Joi.object({
  search: Joi.string().trim().max(100).optional(),
  type: Joi.string().valid(...courseTypes).optional(),
  difficulty: Joi.string().valid(...courseDifficulties).optional(),
  status: Joi.string().valid(...courseStatuses).optional(),
  instructorId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).optional(),
  minPrice: Joi.number().min(0).optional(),
  maxPrice: Joi.number().min(0).optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().min(Joi.ref('startDate')).optional(),
  location: Joi.string().valid(...locationTypes).optional(),
  isActive: Joi.boolean().optional(),
  isFeatured: Joi.boolean().optional(),
  availableOnly: Joi.boolean().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sortBy: Joi.string().valid(
    'title', 'type', 'difficulty', 'price.basePrice', 'schedule.startTime',
    'stats.averageRating', 'stats.totalEnrollments', 'createdAt', 'updatedAt'
  ).default('createdAt'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
}).custom((value, helpers) => {
  if (value.minPrice && value.maxPrice && value.minPrice > value.maxPrice) {
    return helpers.error('custom.priceRange');
  }
  return value;
}).messages({
  'custom.priceRange': '最低价格不能大于最高价格'
});

// 课程搜索验证模式
export const searchCoursesSchema = Joi.object({
  q: Joi.string().trim().min(1).max(100).required().messages({
    'string.min': '搜索关键词不能为空',
    'string.max': '搜索关键词不能超过100个字符',
    'any.required': '搜索关键词是必填项'
  }),
  type: Joi.string().valid(...courseTypes).optional(),
  difficulty: Joi.string().valid(...courseDifficulties).optional(),
  location: Joi.string().valid(...locationTypes).optional(),
  minPrice: Joi.number().min(0).optional(),
  maxPrice: Joi.number().min(0).optional(),
  availableOnly: Joi.boolean().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sortBy: Joi.string().valid(
    'title', 'stats.averageRating', 'price.basePrice', 'schedule.startTime', 'createdAt'
  ).default('stats.averageRating'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
}).custom((value, helpers) => {
  if (value.minPrice && value.maxPrice && value.minPrice > value.maxPrice) {
    return helpers.error('custom.priceRange');
  }
  return value;
}).messages({
  'custom.priceRange': '最低价格不能大于最高价格'
});

// =============== 预约系统相关验证模式 ===============

// 时间段状态和取消原因枚举
const scheduleStatuses = ['scheduled', 'in_progress', 'completed', 'cancelled'];
const bookingStatuses = ['pending', 'confirmed', 'cancelled', 'completed', 'no_show'];
const cancellationReasons = ['user_request', 'instructor_cancelled', 'schedule_changed', 'emergency', 'weather', 'other'];

// 创建课程时间段验证模式
export const createScheduleSchema = Joi.object({
  courseId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).required().messages({
    'string.pattern.base': '课程ID格式不正确',
    'any.required': '课程ID是必填项'
  }),
  instructorId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).required().messages({
    'string.pattern.base': '教练员ID格式不正确',
    'any.required': '教练员ID是必填项'
  }),
  startTime: Joi.date().min('now').required().messages({
    'date.min': '开始时间不能早于当前时间',
    'any.required': '开始时间是必填项'
  }),
  endTime: Joi.date().min(Joi.ref('startTime')).required().messages({
    'date.min': '结束时间必须晚于开始时间',
    'any.required': '结束时间是必填项'
  }),
  maxCapacity: Joi.number().integer().min(1).max(100).required().messages({
    'number.min': '最大容量至少为1',
    'number.max': '最大容量不能超过100',
    'any.required': '最大容量是必填项'
  }),
  location: Joi.string().trim().min(1).max(200).required().messages({
    'string.min': '地点不能为空',
    'string.max': '地点描述不能超过200个字符',
    'any.required': '地点是必填项'
  }),
  description: Joi.string().trim().max(1000).optional().messages({
    'string.max': '描述不能超过1000个字符'
  }),
  specialInstructions: Joi.string().trim().max(500).optional().messages({
    'string.max': '特殊说明不能超过500个字符'
  }),
  isRecurring: Joi.boolean().default(false),
  recurringPattern: Joi.when('isRecurring', {
    is: true,
    then: Joi.object({
      frequency: Joi.string().valid('daily', 'weekly', 'monthly').required().messages({
        'any.only': '频率必须是: daily, weekly, monthly',
        'any.required': '循环频率是必填项'
      }),
      interval: Joi.number().integer().min(1).max(52).required().messages({
        'number.min': '间隔至少为1',
        'number.max': '间隔不能超过52',
        'any.required': '间隔是必填项'
      }),
      daysOfWeek: Joi.array().items(Joi.number().integer().min(0).max(6)).max(7).optional().messages({
        'number.min': '星期几必须是0-6之间的数字',
        'number.max': '星期几必须是0-6之间的数字',
        'array.max': '星期几不能超过7个'
      }),
      endDate: Joi.date().min(Joi.ref('...startTime')).optional().messages({
        'date.min': '结束日期必须晚于开始时间'
      })
    }).required(),
    otherwise: Joi.forbidden()
  })
});

// 更新课程时间段验证模式
export const updateScheduleSchema = Joi.object({
  startTime: Joi.date().optional(),
  endTime: Joi.date().optional(),
  maxCapacity: Joi.number().integer().min(1).max(100).optional(),
  location: Joi.string().trim().min(1).max(200).optional(),
  description: Joi.string().trim().max(1000).optional(),
  specialInstructions: Joi.string().trim().max(500).optional(),
  status: Joi.string().valid(...scheduleStatuses).optional().messages({
    'any.only': `状态必须是: ${scheduleStatuses.join(', ')}`
  })
}).custom((value, helpers) => {
  if (value.startTime && value.endTime && value.startTime >= value.endTime) {
    return helpers.error('custom.timeRange');
  }
  return value;
}).messages({
  'custom.timeRange': '开始时间必须早于结束时间'
});

// 时间段列表查询验证模式
export const getScheduleListSchema = Joi.object({
  courseId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).optional(),
  instructorId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().min(Joi.ref('startDate')).optional(),
  status: Joi.string().valid(...scheduleStatuses).optional(),
  location: Joi.string().trim().max(100).optional(),
  availableOnly: Joi.boolean().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sortBy: Joi.string().valid('startTime', 'endTime', 'maxCapacity', 'currentBookings', 'createdAt').default('startTime'),
  sortOrder: Joi.string().valid('asc', 'desc').default('asc')
});

// 批量更新时间段状态验证模式
export const batchUpdateScheduleStatusSchema = Joi.object({
  scheduleIds: Joi.array().items(
    Joi.string().regex(/^[0-9a-fA-F]{24}$/).messages({
      'string.pattern.base': '时间段ID格式不正确'
    })
  ).min(1).max(100).required().messages({
    'array.min': '至少需要选择一个时间段',
    'array.max': '最多只能选择100个时间段',
    'any.required': '时间段ID列表是必填项'
  }),
  status: Joi.string().valid(...scheduleStatuses).required().messages({
    'any.only': `状态必须是: ${scheduleStatuses.join(', ')}`,
    'any.required': '状态是必填项'
  })
});

// 创建预约验证模式
export const createBookingSchema = Joi.object({
  scheduleId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).required().messages({
    'string.pattern.base': '时间段ID格式不正确',
    'any.required': '时间段ID是必填项'
  }),
  notes: Joi.string().trim().max(500).optional().messages({
    'string.max': '备注不能超过500个字符'
  }),
  specialRequests: Joi.string().trim().max(1000).optional().messages({
    'string.max': '特殊要求不能超过1000个字符'
  })
});

// 更新预约验证模式
export const updateBookingSchema = Joi.object({
  notes: Joi.string().trim().max(500).optional(),
  specialRequests: Joi.string().trim().max(1000).optional(),
  status: Joi.string().valid(...bookingStatuses).optional().messages({
    'any.only': `状态必须是: ${bookingStatuses.join(', ')}`
  })
});

// 取消预约验证模式
export const cancelBookingSchema = Joi.object({
  reason: Joi.string().valid(...cancellationReasons).required().messages({
    'any.only': `取消原因必须是: ${cancellationReasons.join(', ')}`,
    'any.required': '取消原因是必填项'
  }),
  note: Joi.string().trim().max(500).optional().messages({
    'string.max': '取消说明不能超过500个字符'
  })
});

// 标记出席验证模式
export const markAttendanceSchema = Joi.object({
  attended: Joi.boolean().required().messages({
    'any.required': '出席状态是必填项'
  })
});

// 添加评价验证模式
export const addRatingSchema = Joi.object({
  score: Joi.number().integer().min(1).max(5).required().messages({
    'number.min': '评分不能低于1',
    'number.max': '评分不能高于5',
    'number.integer': '评分必须是整数',
    'any.required': '评分是必填项'
  }),
  comment: Joi.string().trim().max(1000).optional().messages({
    'string.max': '评价不能超过1000个字符'
  })
});

// 预约列表查询验证模式
export const getBookingListSchema = Joi.object({
  userId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).optional(),
  instructorId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).optional(),
  courseId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).optional(),
  scheduleId: Joi.string().regex(/^[0-9a-fA-F]{24}$/).optional(),
  status: Joi.string().valid(...bookingStatuses).optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().min(Joi.ref('startDate')).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  sortBy: Joi.string().valid('bookingTime', 'createdAt', 'updatedAt', 'status').default('createdAt'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
});

// 批量更新预约状态验证模式
export const batchUpdateBookingStatusSchema = Joi.object({
  bookingIds: Joi.array().items(
    Joi.string().regex(/^[0-9a-fA-F]{24}$/).messages({
      'string.pattern.base': '预约ID格式不正确'
    })
  ).min(1).max(100).required().messages({
    'array.min': '至少需要选择一个预约',
    'array.max': '最多只能选择100个预约',
    'any.required': '预约ID列表是必填项'
  }),
  status: Joi.string().valid(...bookingStatuses).required().messages({
    'any.only': `状态必须是: ${bookingStatuses.join(', ')}`,
    'any.required': '状态是必填项'
  })
}); 