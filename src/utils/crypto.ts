import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { config } from '../config';

/**
 * 密码加密
 */
export const hashPassword = async (password: string): Promise<string> => {
  try {
    const saltRounds = config.security.bcryptRounds;
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    return hashedPassword;
  } catch (error) {
    throw new Error('Password encryption failed');
  }
};

/**
 * 密码验证
 */
export const comparePassword = async (
  plainPassword: string,
  hashedPassword: string
): Promise<boolean> => {
  try {
    return await bcrypt.compare(plainPassword, hashedPassword);
  } catch (error) {
    throw new Error('Password comparison failed');
  }
};

/**
 * 生成随机字符串
 */
export const generateRandomString = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * 生成安全的验证码
 */
export const generateVerificationCode = (length: number = 6): string => {
  const digits = '0123456789';
  let code = '';
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, digits.length);
    code += digits[randomIndex];
  }
  
  return code;
};

/**
 * 数据加密 (AES-256-GCM)
 */
export const encryptData = (data: string, key?: string): { 
  encrypted: string; 
  iv: string; 
  tag: string; 
} => {
  try {
    const secretKey = key || config.jwt.secret;
    const algorithm = 'aes-256-gcm';
    const iv = crypto.randomBytes(16);
    
    // 创建hash作为密钥
    const keyHash = crypto.createHash('sha256').update(secretKey).digest();
    
    const cipher = crypto.createCipheriv(algorithm, keyHash, iv);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  } catch (error) {
    throw new Error('Data encryption failed');
  }
};

/**
 * 数据解密 (AES-256-GCM)
 */
export const decryptData = (
  encryptedData: string, 
  iv: string, 
  tag: string, 
  key?: string
): string => {
  try {
    const secretKey = key || config.jwt.secret;
    const algorithm = 'aes-256-gcm';
    
    // 创建hash作为密钥
    const keyHash = crypto.createHash('sha256').update(secretKey).digest();
    
    const decipher = crypto.createDecipheriv(algorithm, keyHash, Buffer.from(iv, 'hex'));
    decipher.setAuthTag(Buffer.from(tag, 'hex'));
    
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    throw new Error('Data decryption failed');
  }
}; 