<template>
  <div class="yoga-login">
    <!-- 背景装饰 -->
    <div class="yoga-bg-decoration">
      <div class="yoga-circle yoga-circle-1"></div>
      <div class="yoga-circle yoga-circle-2"></div>
      <div class="yoga-circle yoga-circle-3"></div>
    </div>
    
    <!-- 左侧品牌区域 -->
    <div class="yoga-brand-section">
      <div class="yoga-brand-content">
        <div class="yoga-logo">
          🧘‍♀️
        </div>
        <h1 class="yoga-brand-title">瑜伽平台</h1>
        <p class="yoga-brand-subtitle">管理后台系统</p>
        <div class="yoga-features">
          <div class="yoga-feature-item">
            <i class="el-icon-user"></i>
            <span>用户管理</span>
          </div>
          <div class="yoga-feature-item">
            <i class="el-icon-reading"></i>
            <span>课程管理</span>
          </div>
          <div class="yoga-feature-item">
            <i class="el-icon-date"></i>
            <span>预约管理</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧登录表单区域 -->
    <div class="yoga-login-section">
      <div class="yoga-login-container">
        <div class="yoga-login-header">
          <h2 class="yoga-login-title">欢迎回来</h2>
          <p class="yoga-login-subtitle">请登录您的管理员账户</p>
        </div>
        
        <el-form 
          ref="loginRef" 
          :model="loginForm" 
          :rules="loginRules" 
          class="yoga-login-form"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              size="large"
              auto-complete="off"
              placeholder="用户名"
              class="yoga-input"
            >
              <template #prefix>
                <i class="el-icon-user yoga-input-icon"></i>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              size="large"
              auto-complete="off"
              placeholder="密码"
              class="yoga-input"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <i class="el-icon-lock yoga-input-icon"></i>
              </template>
            </el-input>
          </el-form-item>
          
          <div class="yoga-login-options">
            <el-checkbox v-model="loginForm.rememberMe" class="yoga-remember">
              记住我
            </el-checkbox>
            <a href="#" class="yoga-forgot-link">忘记密码？</a>
          </div>
          
          <el-form-item style="width: 100%; margin-bottom: 0;">
            <el-button
              :loading="loading"
              size="large"
              type="primary"
              class="yoga-login-btn"
              @click.prevent="handleLogin"
            >
              <span v-if="!loading">立即登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>
        
        <!-- 错误提示 -->
        <div v-if="errorMessage" class="yoga-error-message">
          <i class="el-icon-warning-outline"></i>
          {{ errorMessage }}
        </div>
        
        <!-- 底部链接 -->
        <div class="yoga-login-footer">
          <span>还没有账户？</span>
          <router-link to="/register" class="yoga-register-link">立即注册</router-link>
        </div>
      </div>
    </div>
    
    <!-- 页面底部版权信息 -->
    <div class="yoga-copyright">
      <span>© 2024 瑜伽平台管理系统. All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup>
import { login } from "@/api/login"
import Cookies from "js-cookie"
import { encrypt, decrypt } from "@/utils/jsencrypt"
import useUserStore from '@/store/modules/user'
import { ElMessage } from 'element-plus'

const title = import.meta.env.VITE_APP_TITLE
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

// 表单数据
const loginForm = ref({
  username: "admin",  // 使用标准的username字段
  password: "admin123",
  rememberMe: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, trigger: "blur", message: "请输入用户名" },
    { min: 3, max: 50, message: "长度在 3 到 50 个字符", trigger: "blur" }
  ],
  password: [
    { required: true, trigger: "blur", message: "请输入密码" },
    { min: 6, max: 20, message: "密码长度在 6 到 20 个字符", trigger: "blur" }
  ]
}

const loading = ref(false)
const errorMessage = ref('')
const redirect = ref(undefined)

// 监听路由变化
watch(route, (newRoute) => {
  redirect.value = newRoute.query && newRoute.query.redirect
}, { immediate: true })

// 登录处理
function handleLogin() {
  proxy.$refs.loginRef.validate(async (valid) => {
    if (valid) {
      loading.value = true
      errorMessage.value = ''
      
      try {
        // 保存记住密码设置
        if (loginForm.value.rememberMe) {
          Cookies.set("username", loginForm.value.username, { expires: 30 })
          Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 })
          Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 })
        } else {
          Cookies.remove("username")
          Cookies.remove("password")
          Cookies.remove("rememberMe")
        }
        
        // 调用登录API - 使用标准的若依登录流程
        await userStore.login(loginForm.value)
        
        ElMessage.success('登录成功')
        
        // 跳转到目标页面
        const query = route.query
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur]
          }
          return acc
        }, {})
        
        router.push({ path: redirect.value || "/", query: otherQueryParams })
        
      } catch (error) {
        console.error('登录错误:', error)
        // 显示具体的错误信息
        const errorMsg = error.message || error.msg || '登录失败，请检查用户名和密码'
        errorMessage.value = errorMsg
        ElMessage.error(errorMsg)
      } finally {
        loading.value = false
      }
    }
  })
}

// 获取cookie中保存的登录信息
function getCookie() {
  const username = Cookies.get("username")
  const password = Cookies.get("password")
  const rememberMe = Cookies.get("rememberMe")
  
  if (username) {
    loginForm.value.username = username
  }
  if (password) {
    loginForm.value.password = decrypt(password)
  }
  if (rememberMe) {
    loginForm.value.rememberMe = Boolean(rememberMe)
  }
}

// 页面加载时执行
onMounted(() => {
  getCookie()
})
</script>

<style lang='scss' scoped>
.yoga-login {
  display: flex;
  min-height: 100vh;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 背景装饰 */
.yoga-bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.yoga-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.yoga-circle-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.yoga-circle-2 {
  width: 80px;
  height: 80px;
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.yoga-circle-3 {
  width: 150px;
  height: 150px;
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* 左侧品牌区域 */
.yoga-brand-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  z-index: 1;
}

.yoga-brand-content {
  text-align: center;
  color: white;
}

.yoga-logo {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
  from { transform: scale(1); }
  to { transform: scale(1.05); }
}

.yoga-brand-title {
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 0.5rem;
  letter-spacing: 2px;
}

.yoga-brand-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 2rem;
}

.yoga-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 2rem;
}

.yoga-feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.8;
  
  i {
    font-size: 1.2rem;
  }
}

/* 右侧登录区域 */
.yoga-login-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  z-index: 1;
}

.yoga-login-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.yoga-login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.yoga-login-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.yoga-login-subtitle {
  color: #666;
  font-size: 0.9rem;
}

.yoga-login-form {
  .yoga-input {
    margin-bottom: 1rem;
    
    :deep(.el-input__wrapper) {
      border-radius: 12px;
      padding: 0 15px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e8e8e8;
      transition: all 0.3s ease;
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      &.is-focus {
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        border-color: #667eea;
      }
    }
    
    :deep(.el-input__inner) {
      font-size: 0.95rem;
    }
  }
  
  .yoga-input-icon {
    color: #667eea;
    font-size: 1.1rem;
  }
}

.yoga-login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  .yoga-remember {
    :deep(.el-checkbox__label) {
      color: #666;
      font-size: 0.9rem;
    }
  }
  
  .yoga-forgot-link {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: opacity 0.3s ease;
    
    &:hover {
      opacity: 0.8;
    }
  }
}

.yoga-login-btn {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.yoga-error-message {
  background: #fef0f0;
  color: #f56c6c;
  padding: 0.8rem;
  border-radius: 8px;
  margin-top: 1rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.yoga-login-footer {
  text-align: center;
  margin-top: 1.5rem;
  color: #666;
  font-size: 0.9rem;
  
  .yoga-register-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    margin-left: 0.5rem;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.yoga-copyright {
  position: absolute;
  bottom: 1rem;
  width: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .yoga-login {
    flex-direction: column;
  }
  
  .yoga-brand-section {
    flex: none;
    padding: 1rem;
  }
  
  .yoga-brand-title {
    font-size: 2rem;
  }
  
  .yoga-features {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .yoga-login-container {
    margin: 1rem;
    padding: 2rem;
  }
}
</style> 