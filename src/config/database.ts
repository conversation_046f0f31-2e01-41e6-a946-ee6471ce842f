import mongoose from 'mongoose';
import { createClient } from 'redis';
import { config } from './index';
import logger from '../utils/logger';

// MongoDB连接
export const connectMongoDB = async (): Promise<void> => {
  try {
    const mongoUri = config.database.mongoUri;
    
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    
    await mongoose.connect(mongoUri, {
      // 连接选项
      maxPoolSize: 10, // 连接池大小
      serverSelectionTimeoutMS: 5000, // 服务器选择超时
      socketTimeoutMS: 45000, // Socket超时
      bufferCommands: false // 禁用mongoose缓冲
    });

    // 获取实际连接的数据库名称
    const dbName = mongoose.connection.db?.databaseName;
    logger.info(`MongoDB connected successfully to database: ${dbName}`);

    // 监听连接事件
    mongoose.connection.on('error', (error: Error) => {
      logger.error('MongoDB connection error:', error);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected');
    });

  } catch (error) {
    logger.error('MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Redis连接
export const connectRedis = async () => {
  try {
    const redisClient = createClient({
      url: config.database.redisUrl
    });

    redisClient.on('error', (error: Error) => {
      logger.error('Redis connection error:', error);
    });

    redisClient.on('connect', () => {
      logger.info('Redis connected successfully');
    });

    redisClient.on('reconnecting', () => {
      logger.info('Redis reconnecting...');
    });

    await redisClient.connect();
    
    return redisClient;
  } catch (error) {
    logger.error('Redis connection failed:', error);
    throw error;
  }
};

// 数据库断开连接
export const disconnectDatabases = async (): Promise<void> => {
  try {
    await mongoose.disconnect();
    logger.info('MongoDB disconnected');
  } catch (error) {
    logger.error('Error disconnecting from MongoDB:', error);
  }
}; 