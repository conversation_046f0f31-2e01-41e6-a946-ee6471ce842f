import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@/utils/types';
import logger from '@/utils/logger';

/**
 * 全局错误处理中间件
 */
export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let statusCode = 500;
  let message = '服务器内部错误';
  let details: any = undefined;

  // 记录错误
  logger.error('Error occurred:', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // 处理不同类型的错误
  if (error.name === 'ValidationError') {
    // Mongoose验证错误
    statusCode = 400;
    message = '数据验证失败';
    if (error.errors) {
      details = Object.keys(error.errors).map(key => ({
        field: key,
        message: error.errors[key].message
      }));
    }
  } else if (error.name === 'CastError') {
    // Mongoose类型转换错误
    statusCode = 400;
    message = '无效的数据格式';
    details = `字段 ${error.path} 的值 ${error.value} 无效`;
  } else if (error.code === 11000) {
    // MongoDB重复键错误
    statusCode = 409;
    message = '数据冲突';
    const field = Object.keys(error.keyPattern)[0];
    details = `${field} 已存在`;
  } else if (error.name === 'JsonWebTokenError') {
    // JWT错误
    statusCode = 401;
    message = '无效的认证令牌';
  } else if (error.name === 'TokenExpiredError') {
    // JWT过期错误
    statusCode = 401;
    message = '认证令牌已过期';
  } else if (error.message) {
    // 自定义错误消息
    const customErrorMessages: { [key: string]: number } = {
      '用户不存在': 404,
      '邮箱已被注册': 409,
      '用户名已被使用': 409,
      '手机号已被注册': 409,
      '密码错误': 401,
      '账户已被锁定，请稍后再试': 423,
      '账户已被禁用': 403,
      '邮箱未验证，请先验证邮箱': 403,
      '权限不足': 403,
      '当前密码错误': 400,
      '无效的刷新令牌': 401,
      'Invalid refresh token': 401,
      'Refresh token expired': 401,
      'Invalid access token': 401,
      'Access token expired': 401,
      'Token verification failed': 401,
      'Refresh token verification failed': 401,
      '邮箱验证链接无效或已过期': 400,
      '密码重置链接无效或已过期': 400
    };

    statusCode = customErrorMessages[error.message] || 500;
    message = error.message;
  }

  // 构建响应
  const response: any = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  // 在开发环境中包含详细错误信息
  if (process.env.NODE_ENV === 'development') {
    response.error = error.message;
    if (details) {
      response.details = details;
    }
    if (error.stack) {
      response.stack = error.stack;
    }
  } else if (details) {
    // 在生产环境中只包含必要的详细信息
    response.details = details;
  }

  res.status(statusCode).json(response);
};

/**
 * 404错误处理中间件
 */
export const notFoundHandler = (
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  const response: ApiResponse = {
    success: false,
    message: `路径 ${req.originalUrl} 不存在`,
    timestamp: new Date().toISOString()
  };

  logger.warn(`404 Error: ${req.method} ${req.originalUrl} from IP: ${req.ip}`);
  res.status(404).json(response);
};

/**
 * 异步错误捕获装饰器
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}; 