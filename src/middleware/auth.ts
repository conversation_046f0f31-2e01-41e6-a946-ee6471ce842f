import { Request, Response, NextFunction } from 'express';
import { verifyAccessToken } from '@/utils/jwt-simple';
import logger from '@/utils/logger';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: string;
    isEmailVerified: boolean;
    isActive: boolean;
  };
}

declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
        isEmailVerified: boolean;
        isActive: boolean;
      };
    }
  }
}

export const authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        message: '访问令牌缺失或格式错误'
      });
      return;
    }

    const token = authHeader.substring(7);
    const decoded = verifyAccessToken(token);
    
    // 设置用户信息到请求对象
    req.user = {
      id: decoded.userId,
      email: decoded.email,
      role: decoded.role || 'user',
      isEmailVerified: decoded.isEmailVerified || false,
      isActive: decoded.isActive !== false // 默认为true，除非明确设置为false
    };

    next();
  } catch (error) {
    logger.error('认证中间件错误:', error);
    res.status(401).json({
      success: false,
      message: '认证失败'
    });
  }
};

// 可选认证中间件，用于不强制要求登录的接口
export const optionalAuthenticate = async (req: Request, _res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const decoded = verifyAccessToken(token);
        
        req.user = {
          id: decoded.userId,
          email: decoded.email,
          role: decoded.role || 'user',
          isEmailVerified: decoded.isEmailVerified || false,
          isActive: decoded.isActive !== false
        };
      } catch (tokenError) {
        // 可选认证中token错误不阻断请求
        logger.warn('可选认证token验证失败:', tokenError);
      }
    }
    next();
  } catch (error) {
    // 可选认证失败不阻断请求
    logger.warn('可选认证中间件警告:', error);
    next();
  }
};

// 角色授权中间件
export const authorize = (roles: string[] = []) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: '未认证用户'
      });
      return;
    }

    if (roles.length > 0 && !roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        message: '权限不足'
      });
      return;
    }

    next();
  };
};

// 检查用户是否为资源所有者或管理员
export const checkOwnershipOrAdmin = (userIdField: string = 'userId') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: '未认证用户'
      });
      return;
    }

    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    const isOwner = resourceUserId && resourceUserId === req.user.id;
    const isAdmin = req.user.role === 'admin';

    if (!isOwner && !isAdmin) {
      res.status(403).json({
        success: false,
        message: '权限不足：只能访问自己的资源'
      });
      return;
    }

    next();
  };
};

// 检查账户状态
export const checkAccountStatus = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      message: '未认证用户'
    });
    return;
  }

  if (!req.user.isActive) {
    res.status(403).json({
      success: false,
      message: '账户已被禁用'
    });
    return;
  }

  // 在测试环境跳过邮箱验证检查
  if (process.env.NODE_ENV !== 'test' && !req.user.isEmailVerified) {
    res.status(403).json({
      success: false,
      message: '邮箱未验证，请先验证邮箱'
    });
    return;
  }

  next();
};

// requireRole函数作为authorize的别名，保持API一致性
export const requireRole = authorize; 