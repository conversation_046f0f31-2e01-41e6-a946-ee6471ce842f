import { Request, Response } from 'express';
import rateLimit from 'express-rate-limit';
import { config } from '../config';
import { ApiResponse } from '../utils/types';
import logger from '../utils/logger';

/**
 * 通用限流配置
 */
export const generalRateLimit = rateLimit({
  windowMs: config.security.rateLimitWindowMs, // 15分钟
  max: config.security.rateLimitMaxRequests, // 限制每个IP在窗口期内最多100个请求
  message: {
    success: false,
    error: 'Too Many Requests',
    message: '请求过于频繁，请稍后再试',
    timestamp: new Date().toISOString()
  } as ApiResponse,
  standardHeaders: true, // 返回rate limit信息在 `RateLimit-*` headers
  legacyHeaders: false, // 禁用 `X-RateLimit-*` headers
  handler: (req: Request, res: Response) => {
    logger.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
    
    const response: ApiResponse = {
      success: false,
      error: 'Too Many Requests',
      message: '请求过于频繁，请稍后再试',
      timestamp: new Date().toISOString()
    };

    res.status(429).json(response);
  },
  skip: (req: Request) => {
    // 跳过健康检查等特定路径
    const skipPaths = ['/health', '/favicon.ico'];
    return skipPaths.includes(req.path);
  }
});

/**
 * 认证相关的限流（更严格）
 */
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: config.app.env === 'test' ? 1000 : 5, // 测试环境放宽限制
  message: {
    success: false,
    error: 'Too Many Authentication Attempts',
    message: '登录尝试过于频繁，请15分钟后再试',
    timestamp: new Date().toISOString()
  } as ApiResponse,
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    logger.warn(`Auth rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
    
    const response: ApiResponse = {
      success: false,
      error: 'Too Many Authentication Attempts',
      message: '登录尝试过于频繁，请15分钟后再试',
      timestamp: new Date().toISOString()
    };

    res.status(429).json(response);
  }
});

/**
 * 注册限流
 */
export const registerRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: config.app.env === 'test' ? 1000 : 3, // 测试环境放宽限制
  message: {
    success: false,
    error: 'Too Many Registration Attempts',
    message: '注册尝试过于频繁，请1小时后再试',
    timestamp: new Date().toISOString()
  } as ApiResponse,
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    logger.warn(`Registration rate limit exceeded for IP: ${req.ip}`);
    
    const response: ApiResponse = {
      success: false,
      error: 'Too Many Registration Attempts',
      message: '注册尝试过于频繁，请1小时后再试',
      timestamp: new Date().toISOString()
    };

    res.status(429).json(response);
  }
});

/**
 * 密码重置限流
 */
export const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 每个IP在1小时内最多3次密码重置尝试
  message: {
    success: false,
    error: 'Too Many Password Reset Attempts',
    message: '密码重置尝试过于频繁，请1小时后再试',
    timestamp: new Date().toISOString()
  } as ApiResponse,
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    logger.warn(`Password reset rate limit exceeded for IP: ${req.ip}`);
    
    const response: ApiResponse = {
      success: false,
      error: 'Too Many Password Reset Attempts',
      message: '密码重置尝试过于频繁，请1小时后再试',
      timestamp: new Date().toISOString()
    };

    res.status(429).json(response);
  }
});

/**
 * API调用限流（对已认证用户）
 */
export const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 已认证用户在15分钟内最多1000个API请求
  keyGenerator: (req: Request) => {
    // 使用用户ID作为key，如果没有用户ID则使用IP
    return req.user?.id || req.ip || 'anonymous';
  },
  message: {
    success: false,
    error: 'API Rate Limit Exceeded',
    message: 'API调用过于频繁，请稍后再试',
    timestamp: new Date().toISOString()
  } as ApiResponse,
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    const identifier = req.user?.id || req.ip;
    logger.warn(`API rate limit exceeded for user/IP: ${identifier}, Path: ${req.path}`);
    
    const response: ApiResponse = {
      success: false,
      error: 'API Rate Limit Exceeded',
      message: 'API调用过于频繁，请稍后再试',
      timestamp: new Date().toISOString()
    };

    res.status(429).json(response);
  }
});

/**
 * 文件上传限流
 */
export const uploadRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 20, // 每个用户在1小时内最多20次文件上传
  keyGenerator: (req: Request) => {
    return req.user?.id || req.ip || 'anonymous';
  },
  message: {
    success: false,
    error: 'Upload Rate Limit Exceeded',
    message: '文件上传过于频繁，请1小时后再试',
    timestamp: new Date().toISOString()
  } as ApiResponse,
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    const identifier = req.user?.id || req.ip;
    logger.warn(`Upload rate limit exceeded for user/IP: ${identifier}`);
    
    const response: ApiResponse = {
      success: false,
      error: 'Upload Rate Limit Exceeded',
      message: '文件上传过于频繁，请1小时后再试',
      timestamp: new Date().toISOString()
    };

    res.status(429).json(response);
  }
});

/**
 * 创建自定义限流中间件
 */
export const createRateLimit = (options: {
  windowMs: number;
  max: number;
  message: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: {
      success: false,
      error: 'Rate Limit Exceeded',
      message: options.message,
      timestamp: new Date().toISOString()
    } as ApiResponse,
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: options.skipSuccessfulRequests || false,
    skipFailedRequests: options.skipFailedRequests || false,
    handler: (req: Request, res: Response) => {
      logger.warn(`Custom rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
      
      const response: ApiResponse = {
        success: false,
        error: 'Rate Limit Exceeded',
        message: options.message,
        timestamp: new Date().toISOString()
      };

      res.status(429).json(response);
    }
  });
}; 