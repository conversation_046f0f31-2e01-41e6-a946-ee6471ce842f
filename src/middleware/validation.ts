import { Request, Response, NextFunction } from 'express';
import { Schema } from 'joi';
import logger from '@/utils/logger';

/**
 * 创建验证中间件
 */
export const validate = (schema: Schema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // 返回所有错误
      stripUnknown: true, // 移除未知字段
      allowUnknown: false // 不允许未知字段
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      logger.warn('Request validation failed:', errorDetails);

      res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: '请求数据验证失败',
        timestamp: new Date().toISOString(),
        details: errorDetails
      });
      return;
    }

    // 将验证后的数据替换原始数据
    req.body = value;
    next();
  };
};

/**
 * 验证查询参数
 */
export const validateQuery = (schema: Schema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
      allowUnknown: false
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      logger.warn('Query validation failed:', errorDetails);

      res.status(400).json({
        success: false,
        error: 'Query Validation Error',
        message: '查询参数验证失败',
        timestamp: new Date().toISOString(),
        details: errorDetails
      });
      return;
    }

    // 将验证后的数据赋值给req对象
    Object.assign(req.query, value);
    next();
  };
};

/**
 * 验证路径参数
 */
export const validateParams = (schema: Schema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true,
      allowUnknown: false
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      logger.warn('Params validation failed:', errorDetails);

      res.status(400).json({
        success: false,
        error: 'Params Validation Error',
        message: '路径参数验证失败',
        timestamp: new Date().toISOString(),
        details: errorDetails
      });
      return;
    }

    // 将验证后的数据赋值给req对象
    Object.assign(req.params, value);
    next();
  };
};

/**
 * 文件上传验证中间件
 */
export const validateFileUpload = (options: {
  maxSize?: number; // 最大文件大小(bytes)
  allowedMimeTypes?: string[]; // 允许的MIME类型
  required?: boolean; // 是否必须上传文件
} = {}) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const {
      maxSize = 5 * 1024 * 1024, // 默认5MB
      allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'],
      required = false
    } = options;

    const file = req.file;
    const files = req.files;

    // 检查是否需要文件但未上传
    if (required && !file && (!files || (Array.isArray(files) && files.length === 0))) {
      res.status(400).json({
        success: false,
        error: 'File Required',
        message: '请上传文件',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 如果没有文件且不是必需的，直接继续
    if (!file && (!files || (Array.isArray(files) && files.length === 0))) {
      next();
      return;
    }

    // 验证单个文件
    if (file) {
      if (file.size > maxSize) {
        res.status(400).json({
          success: false,
          message: `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`
        });
        return;
      }

      if (!allowedMimeTypes.includes(file.mimetype)) {
        res.status(400).json({
          success: false,
          message: `不支持的文件类型: ${file.mimetype}`
        });
        return;
      }
    }

    // 验证多个文件
    if (files && Array.isArray(files)) {
      for (const uploadedFile of files) {
        if (uploadedFile.size > maxSize) {
          res.status(400).json({
            success: false,
            message: `文件 ${uploadedFile.originalname} 大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`
          });
          return;
        }

        if (!allowedMimeTypes.includes(uploadedFile.mimetype)) {
          res.status(400).json({
            success: false,
            message: `文件 ${uploadedFile.originalname} 类型不支持: ${uploadedFile.mimetype}`
          });
          return;
        }
      }
    }

    logger.info('File validation passed');
    next();
  };
};

// validateRequest函数作为validate的别名，保持API一致性
export const validateRequest = validate; 