import { Router } from 'express';
import { UserController } from '@/controllers/userController';
import { authenticate, authorize, checkAccountStatus } from '@/middleware/auth';
import { validate, validateParams, validateQuery } from '@/middleware/validation';
import { generalRateLimit } from '@/middleware/rateLimit';
import { 
  userListQuerySchema,
  adminUpdateUserSchema,
  updateUserStatusSchema,
  updateUserRoleSchema,
  batchUserOperationSchema,
  objectIdSchema
} from '@/utils/validation';
import { UserRole } from '@/utils/types';

const router = Router();

/**
 * 用户管理路由 - 需要管理员权限
 */

// 获取用户列表（分页、搜索、筛选）
router.get('/',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]),
  validateQuery(userListQuerySchema),
  UserController.getUserList
);

// 获取用户详情
router.get('/:userId',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]),
  validateParams(objectIdSchema),
  UserController.getUserById
);

// 更新用户信息（管理员）
router.put('/:userId',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]),
  validateParams(objectIdSchema),
  validate(adminUpdateUserSchema),
  UserController.updateUser
);

// 更新用户状态（激活/禁用、邮箱验证）
router.patch('/:userId/status',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]),
  validateParams(objectIdSchema),
  validate(updateUserStatusSchema),
  UserController.updateUserStatus
);

// 更新用户角色
router.patch('/:userId/role',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  authorize([UserRole.SUPER_ADMIN]), // 只有超级管理员可以修改角色
  validateParams(objectIdSchema),
  validate(updateUserRoleSchema),
  UserController.updateUserRole
);

// 删除用户（软删除）
router.delete('/:userId',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  authorize([UserRole.SUPER_ADMIN]), // 只有超级管理员可以删除用户
  validateParams(objectIdSchema),
  UserController.deleteUser
);

// 批量操作用户
router.post('/batch',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]),
  validate(batchUserOperationSchema),
  UserController.batchUserOperation
);

// 获取用户统计信息
router.get('/statistics/overview',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]),
  UserController.getUserStatistics
);

export default router; 