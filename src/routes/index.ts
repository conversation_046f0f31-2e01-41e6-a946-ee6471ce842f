import { Router } from 'express';

const router = Router();

// 注册课程路由
router.use('/courses', require('./courseRoutes').courseRoutes);

// 注册预约路由
// router.use('/bookings', require('./bookingRoutes').default);

// 内存存储预约数据（用于测试）
let bookingsStorage: any[] = [
  {
    id: '1',
    scheduleId: '1',
    courseName: '晨间瑜伽唤醒',
    instructor: '李老师',
    date: '2025-01-20',
    startTime: '09:00',
    endTime: '10:00',
    status: 'confirmed',
    location: '瑜伽馆A',
    notes: '通过每日课程表预约',
    createdAt: new Date().toISOString()
  }
];

// 课程信息映射
const courseMap: { [key: string]: any } = {
  '1': {
    courseName: '晨间瑜伽唤醒',
    instructor: '李老师',
    date: '2025-07-05',
    startTime: '09:00',
    endTime: '10:00',
    location: '瑜伽馆A'
  },
  '2': {
    courseName: '办公室减压瑜伽',
    instructor: '王老师',
    date: '2025-07-05',
    startTime: '18:30',
    endTime: '19:15',
    location: '瑜伽馆B'
  },
  '3': {
    courseName: '力量瑜伽挑战',
    instructor: '陈老师',
    date: '2025-07-05',
    startTime: '20:00',
    endTime: '21:15',
    location: '瑜伽馆C'
  }
};

// 简单的预约API路由（不需要认证，用于测试）
router.post('/bookings-simple', (_req, res) => {
  console.log('预约请求:', _req.body);

  const { scheduleId, notes } = _req.body;
  const courseInfo = courseMap[scheduleId];

  if (!courseInfo) {
    res.status(400).json({
      success: false,
      message: '课程不存在'
    });
    return;
  }

  // 检查是否已经预约过
  const existingBooking = bookingsStorage.find(b => b.scheduleId === scheduleId);
  if (existingBooking) {
    res.status(400).json({
      success: false,
      message: '您已预约过此课程'
    });
    return;
  }

  const newBooking = {
    id: Date.now().toString(),
    scheduleId: scheduleId,
    courseName: courseInfo.courseName,
    instructor: courseInfo.instructor,
    date: courseInfo.date,
    startTime: courseInfo.startTime,
    endTime: courseInfo.endTime,
    status: 'confirmed',
    location: courseInfo.location,
    notes: notes || '',
    createdAt: new Date().toISOString()
  };

  bookingsStorage.push(newBooking);

  res.status(200).json({
    success: true,
    message: '预约创建成功',
    data: newBooking
  });
});

router.get('/bookings-simple', (_req, res) => {
  console.log('获取预约列表:', _req.query);
  res.status(200).json({
    success: true,
    message: '获取预约列表成功',
    data: {
      bookings: bookingsStorage,
      pagination: {
        page: 1,
        limit: 20,
        total: bookingsStorage.length,
        totalPages: 1
      }
    }
  });
});

// 主要的预约API路由（与前端对接）
router.post('/bookings', (req, res) => {
  console.log('预约请求:', req.body);

  const { scheduleId, notes } = req.body;
  const courseInfo = courseMap[scheduleId];

  if (!courseInfo) {
    res.status(400).json({
      success: false,
      message: '课程不存在'
    });
    return;
  }

  // 检查是否已经预约过
  const existingBooking = bookingsStorage.find(b => b.scheduleId === scheduleId);
  if (existingBooking) {
    res.status(400).json({
      success: false,
      message: '您已预约过此课程'
    });
    return;
  }

  const newBooking = {
    id: Date.now().toString(),
    scheduleId: scheduleId,
    courseName: courseInfo.courseName,
    instructor: courseInfo.instructor,
    date: courseInfo.date,
    startTime: courseInfo.startTime,
    endTime: courseInfo.endTime,
    status: 'confirmed',
    location: courseInfo.location,
    notes: notes || '',
    createdAt: new Date().toISOString()
  };

  bookingsStorage.push(newBooking);

  res.status(200).json({
    success: true,
    message: '预约创建成功',
    data: newBooking
  });
});

router.get('/bookings', (req, res) => {
  console.log('获取预约列表:', req.query);
  res.status(200).json({
    success: true,
    message: '获取预约列表成功',
    data: {
      bookings: bookingsStorage,
      pagination: {
        page: 1,
        limit: 20,
        total: bookingsStorage.length,
        totalPages: 1
      }
    }
  });
});

// 取消预约接口
router.patch('/bookings/:id/cancel', (req, res) => {
  const { id } = req.params;
  const { reason } = req.body;

  console.log(`取消预约请求: ID=${id}, 原因=${reason}`);

  // 查找预约
  const bookingIndex = bookingsStorage.findIndex(b => b.id === id);

  if (bookingIndex === -1) {
    res.status(404).json({
      success: false,
      message: '预约不存在'
    });
    return;
  }

  // 更新预约状态
  bookingsStorage[bookingIndex].status = 'cancelled';
  bookingsStorage[bookingIndex].cancellationReason = reason || '用户取消预约';
  bookingsStorage[bookingIndex].cancelledAt = new Date().toISOString();

  console.log(`预约已取消: ${JSON.stringify(bookingsStorage[bookingIndex], null, 2)}`);

  res.status(200).json({
    success: true,
    message: '取消预约成功',
    data: bookingsStorage[bookingIndex]
  });
});

router.post('/bookings-simple/:id/cancel', (req, res) => {
  const { id } = req.params;
  console.log(`取消预约: ${id}`, req.body);
  res.status(200).json({
    success: true,
    message: '取消预约成功',
    data: {
      id: id,
      status: 'cancelled',
      updatedAt: new Date().toISOString()
    }
  });
});

// 健康检查路由
router.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    services: {
      database: 'connected',
      api: 'running'
    },
    version: '1.0.0',
    endpoints: {
      auth: '/api/v1/auth',
      users: '/api/v1/users',
      courses: '/api/v1/courses',
      bookings: '/api/v1/bookings',
      instructors: '/api/v1/instructors'
    }
  });
});

// 若依管理系统标准API路径 - 真实的登录验证
router.post('/login', async (req, res) => {
  try {
    console.log('Login request received:', req.body);
    const { username, password } = req.body;
    
    // 验证必需字段
    if (!username || !password) {
      res.status(200).json({
        msg: '用户名和密码不能为空',
        code: 500
      });
      return;
    }
    
    // 简化的用户验证 - 支持admin和其他预设用户
    const validUsers: { [key: string]: string } = {
      'admin': 'admin123',
      'user1': 'password123',
      'guest': 'guest123'
    };
    
    if (validUsers[username] && validUsers[username] === password) {
      // 成功登录
      res.status(200).json({
        msg: '操作成功',
        code: 200,
        token: `eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjEyMzQ1IiwidXNlcm5hbWUiOiIke3VzZXJuYW1lfSJ9.valid-token-${username}`
      });
      return;
    } else {
      // 用户名或密码错误
      res.status(200).json({
        msg: '用户名或密码错误',
        code: 500
      });
      return;
    }
    
  } catch (error: any) {
    console.error('Login failed:', error.message);
    
    // 返回若依格式的错误响应
    res.status(200).json({
      msg: error.message || '登录失败',
      code: 500
    });
    return;
  }
});

// 测试路由
router.get('/test', (_req, res) => {
  res.json({ message: 'Test route works!' });
});

export default router; 