import { Router } from 'express';
import { AuthController } from '@/controllers/authController';
import { validate, validateParams } from '@/middleware/validation';
import { authenticate, authorize, checkAccountStatus } from '@/middleware/auth';
import { 
  authRateLimit, 
  registerRateLimit, 
  passwordResetRateLimit,
  generalRateLimit 
} from '@/middleware/rateLimit';
import { 
  registerSchema, 
  loginSchema, 
  forgotPasswordSchema, 
  resetPasswordSchema,
  changePasswordSchema,
  updateProfileSchema
} from '@/utils/validation';
import { UserRole } from '@/utils/types';

const router = Router();

/**
 * 公开路由（无需认证）
 */

// 用户注册
router.post('/register', 
  registerRateLimit,
  validate(registerSchema),
  AuthController.register
);

// 用户登录
router.post('/login', 
  authRateLimit,
  validate(loginSchema),
  AuthController.login
);

// 刷新访问令牌
router.post('/refresh-token', 
  generalRateLimit,
  AuthController.refreshToken
);

// 验证邮箱
router.get('/verify-email/:token', 
  generalRateLimit,
  validateParams(resetPasswordSchema), // 复用schema验证token参数
  AuthController.verifyEmail
);

// 发送密码重置邮件
router.post('/forgot-password', 
  passwordResetRateLimit,
  validate(forgotPasswordSchema),
  AuthController.forgotPassword
);

// 重置密码
router.post('/reset-password/:token', 
  passwordResetRateLimit,
  validateParams(resetPasswordSchema),
  validate(resetPasswordSchema),
  AuthController.resetPassword
);

/**
 * 需要认证的路由
 */

// 用户退出登录
router.post('/logout', 
  generalRateLimit,
  authenticate,
  AuthController.logout
);

// 修改密码
router.post('/change-password', 
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  validate(changePasswordSchema),
  AuthController.changePassword
);

// 获取当前用户信息
router.get('/profile', 
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  AuthController.getProfile
);

// 更新用户信息
router.put('/profile', 
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  validate(updateProfileSchema),
  AuthController.updateProfile
);

// 删除用户账户
router.delete('/account', 
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  AuthController.deleteAccount
);

/**
 * 管理员路由
 */

// 获取用户统计信息（仅管理员）
router.get('/admin/stats', 
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]),
  AuthController.getUserStats
);

export default router; 