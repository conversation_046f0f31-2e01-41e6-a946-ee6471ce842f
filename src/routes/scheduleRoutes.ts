import { Router } from 'express';
import { ScheduleController } from '../controllers/scheduleController';
import { authenticate, requireRole, checkAccountStatus } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { 
  createScheduleSchema, 
  updateScheduleSchema, 
  getScheduleListSchema,
  batchUpdateScheduleStatusSchema 
} from '../utils/validation';

const router = Router();

// 公开路由（不需要认证）
router.get('/available', ScheduleController.getAvailableSchedules);
router.get('/list', validateRequest(getScheduleListSchema), ScheduleController.getScheduleList);
router.get('/:id', ScheduleController.getScheduleById);

// 需要认证的路由
router.use(authenticate);
router.use(checkAccountStatus);

// 教练员和管理员可以创建时间段
router.post('/', 
  requireRole(['instructor', 'admin']),
  validateRequest(createScheduleSchema),
  ScheduleController.createSchedule
);

// 教练员和管理员可以更新时间段
router.put('/:id',
  requireRole(['instructor', 'admin']),
  validateRequest(updateScheduleSchema),
  ScheduleController.updateSchedule
);

// 教练员和管理员可以删除时间段
router.delete('/:id',
  requireRole(['instructor', 'admin']),
  ScheduleController.deleteSchedule
);

// 获取教练员时间段
router.get('/instructor/:instructorId', ScheduleController.getInstructorSchedules);

// 批量更新状态（管理员专用）
router.patch('/batch/status',
  requireRole(['admin']),
  validateRequest(batchUpdateScheduleStatusSchema),
  ScheduleController.batchUpdateStatus
);

// 获取统计信息
router.get('/stats/overview', ScheduleController.getScheduleStats);

export default router; 