import { Router } from 'express';
import { CourseController } from '@/controllers/courseController';
import { authenticate } from '@/middleware/auth';
import { validate, validateQuery } from '@/middleware/validation';
import { createCourseSchema, updateCourseSchema, getCourseListSchema, searchCoursesSchema } from '@/utils/validation';

const router = Router();

// 公开路由
router.get('/', CourseController.getCourseList); // 添加根路径路由
router.get('/search', validateQuery(searchCoursesSchema), CourseController.searchCourses);
router.get('/featured', CourseController.getFeaturedCourses);
router.get('/list', validateQuery(getCourseListSchema), CourseController.getCourseList);
router.get('/:id', CourseController.getCourseById);

// 需要认证的路由
router.use(authenticate);

// 创建课程（教练员或管理员）
router.post('/', validate(createCourseSchema), CourseController.createCourse);

// 更新课程（课程所有者或管理员）
router.put('/:id', validate(updateCourseSchema), CourseController.updateCourse);

// 删除课程（课程所有者或管理员）
router.delete('/:id', CourseController.deleteCourse);

// 获取我的课程（教练员）
router.get('/my/courses', CourseController.getMyCourses);

// 获取课程统计
router.get('/stats/overview', CourseController.getCourseStats);

// 获取特定教练员的课程列表
router.get('/instructor/:instructorId', CourseController.getInstructorCourses);

export { router as courseRoutes }; 