import { Router } from 'express';
import { InstructorController } from '@/controllers/instructorController';
import { authenticate, authorize, checkAccountStatus } from '@/middleware/auth';
import { generalRateLimit } from '@/middleware/rateLimit';
import { UserRole } from '@/utils/types';

const router = Router();

// ========== 公开路由 ==========

// 获取活跃教练员列表 (无需认证)
router.get('/active', InstructorController.getActiveInstructors);

// ========== 需要认证的路由 ==========

// 根据用户ID获取教练员信息 (用户可查看自己的教练员信息)
router.get('/user/:userId', 
  generalRateLimit,
  authenticate, 
  InstructorController.getInstructorByUserId
);

// ========== 管理员路由 ==========

// 获取教练员统计信息 (管理员) - 必须在 /:id 之前定义
router.get('/stats', 
  generalRateLimit,
  authenticate, 
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]), 
  InstructorController.getInstructorStats
);

// 获取教练员列表 (管理员)
router.get('/', 
  generalRateLimit,
  authenticate, 
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]), 
  InstructorController.getInstructors
);

// 获取教练员详情 (管理员)
router.get('/:id', 
  generalRateLimit,
  authenticate, 
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]), 
  InstructorController.getInstructorById
);

// 创建教练员 (管理员)
router.post('/', 
  generalRateLimit,
  authenticate, 
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]), 
  InstructorController.createInstructor
);

// 批量操作 (管理员) - 必须在其他POST路由之前定义
router.post('/batch', 
  generalRateLimit,
  authenticate, 
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]), 
  InstructorController.batchOperation
);

// 更新教练员信息 (管理员)
router.put('/:id', 
  generalRateLimit,
  authenticate, 
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]), 
  InstructorController.updateInstructor
);

// 更新教练员状态 (管理员)
router.patch('/:id/status', 
  generalRateLimit,
  authenticate, 
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]), 
  InstructorController.updateInstructorStatus
);

// 更新教练员统计信息 (管理员)
router.patch('/:id/stats', 
  generalRateLimit,
  authenticate, 
  checkAccountStatus,
  authorize([UserRole.ADMIN, UserRole.SUPER_ADMIN]), 
  InstructorController.updateInstructorStats
);

// ========== 超级管理员路由 ==========

// 删除教练员 (超级管理员)
router.delete('/:id', 
  generalRateLimit,
  authenticate, 
  checkAccountStatus,
  authorize([UserRole.SUPER_ADMIN]), 
  InstructorController.deleteInstructor
);

export default router; 