import { Router } from 'express';
import { ProfileController, avatarUpload } from '@/controllers/profileController';
import { authenticate, checkAccountStatus } from '@/middleware/auth';
import { validate, validateQuery } from '@/middleware/validation';
import { generalRateLimit, authRateLimit } from '@/middleware/rateLimit';
import { 
  profileUpdateSchema,
  preferencesUpdateSchema,
  changePasswordSchema,
  deleteAccountSchema,
  activitiesQuerySchema
} from '@/utils/validation';

const router = Router();

/**
 * 个人资料路由 - 需要用户登录
 */

// 获取个人资料
router.get('/',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  ProfileController.getProfile
);

// 更新个人资料
router.put('/',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  validate(profileUpdateSchema),
  ProfileController.updateProfile
);

// 更新偏好设置
router.patch('/preferences',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  validate(preferencesUpdateSchema),
  ProfileController.updatePreferences
);

// 修改密码
router.patch('/password',
  authRateLimit, // 使用更严格的限流
  authenticate,
  checkAccountStatus,
  validate(changePasswordSchema),
  ProfileController.changePassword
);

// 上传头像
router.post('/avatar',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  avatarUpload.single('avatar'),
  ProfileController.uploadAvatar
);

// 获取活动历史
router.get('/activities',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  validateQuery(activitiesQuerySchema),
  ProfileController.getActivities
);

// 获取个人统计信息
router.get('/stats',
  generalRateLimit,
  authenticate,
  checkAccountStatus,
  ProfileController.getStats
);

// 删除个人账户
router.delete('/',
  authRateLimit, // 使用更严格的限流
  authenticate,
  checkAccountStatus,
  validate(deleteAccountSchema),
  ProfileController.deleteAccount
);

export default router; 