import { Router } from 'express';
import { BookingController } from '../controllers/bookingController';
import { authenticate, requireRole, checkAccountStatus } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { 
  createBookingSchema, 
  updateBookingSchema, 
  cancelBookingSchema,
  markAttendanceSchema,
  addRatingSchema,
  getBookingListSchema,
  batchUpdateBookingStatusSchema 
} from '../utils/validation';

const router = Router();

// 所有预约路由都需要认证
router.use(authenticate);
router.use(checkAccountStatus);

// 用户预约相关路由
router.post('/', 
  requireRole(['user']),
  validateRequest(createBookingSchema),
  BookingController.createBooking
);

router.get('/', 
  validateRequest(getBookingListSchema),
  BookingController.getBookingList
);

router.get('/:id', BookingController.getBookingById);

router.put('/:id',
  validateRequest(updateBookingSchema),
  BookingController.updateBooking
);

router.patch('/:id/cancel',
  validateRequest(cancelBookingSchema),
  BookingController.cancelBooking
);

// 教练员专用路由
router.patch('/:id/confirm',
  requireRole(['instructor']),
  BookingController.confirmBooking
);

router.patch('/:id/attendance',
  requireRole(['instructor']),
  validateRequest(markAttendanceSchema),
  BookingController.markAttendance
);

// 用户评价路由
router.post('/:id/rating',
  requireRole(['user']),
  validateRequest(addRatingSchema),
  BookingController.addRating
);

// 获取用户预约历史
router.get('/user/:userId', BookingController.getUserBookings);
router.get('/user', BookingController.getUserBookings); // 不传userId时获取当前用户的预约

// 获取教练员预约
router.get('/instructor/:instructorId', 
  requireRole(['instructor', 'admin']),
  BookingController.getInstructorBookings
);
router.get('/instructor', 
  requireRole(['instructor', 'admin']),
  BookingController.getInstructorBookings
); // 不传instructorId时获取当前教练员的预约

// 获取预约统计
router.get('/stats/overview', BookingController.getBookingStats);

// 管理员专用路由
router.patch('/batch/status',
  requireRole(['admin']),
  validateRequest(batchUpdateBookingStatusSchema),
  BookingController.batchUpdateStatus
);

export default router; 