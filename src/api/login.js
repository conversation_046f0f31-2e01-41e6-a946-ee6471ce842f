import request from '@/utils/request'

// 登录方法 - 适配瑜伽平台Node.js后端
export function login(emailOrUsername, password) {
  const data = {
    emailOrUsername,
    password
  }
  return request({
    url: '/auth/login',
    headers: {
      isToken: false,
      repeatSubmit: false
    },
    method: 'post',
    data: data
  })
}

// 注册方法 - 适配瑜伽平台Node.js后端
export function register(data) {
  return request({
    url: '/auth/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息 - 适配瑜伽平台Node.js后端
export function getInfo() {
  return request({
    url: '/auth/profile',
    method: 'get'
  })
}

// 退出方法 - 适配瑜伽平台Node.js后端
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 刷新Token - 瑜伽平台特有功能
export function refreshToken(refreshToken) {
  return request({
    url: '/auth/refresh-token',
    headers: {
      isToken: false
    },
    method: 'post',
    data: { refreshToken }
  })
}

// 获取验证码 - 暂时禁用，瑜伽平台暂不需要验证码
export function getCodeImg() {
  // 返回空的验证码，或者可以对接后端的验证码接口
  return Promise.resolve({
    code: 200,
    img: '',
    uuid: '',
    captchaEnabled: false
  })
} 