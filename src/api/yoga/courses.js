import request from '@/utils/request'

// 获取课程列表
export function getCourseList(params) {
  return request({
    url: '/courses',
    method: 'get',
    params: params
  })
}

// 获取课程详情
export function getCourseDetail(id) {
  return request({
    url: `/courses/${id}`,
    method: 'get'
  })
}

// 创建课程
export function createCourse(data) {
  return request({
    url: '/courses',
    method: 'post',
    data: data
  })
}

// 更新课程
export function updateCourse(id, data) {
  return request({
    url: `/courses/${id}`,
    method: 'put',
    data: data
  })
}

// 删除课程
export function deleteCourse(id) {
  return request({
    url: `/courses/${id}`,
    method: 'delete'
  })
}

// 获取教练员的课程
export function getInstructorCourses(instructorId, params) {
  return request({
    url: `/courses/instructor/${instructorId}`,
    method: 'get',
    params: params
  })
}

// 搜索课程
export function searchCourses(params) {
  return request({
    url: '/courses/search',
    method: 'get',
    params: params
  })
}

// 获取推荐课程
export function getFeaturedCourses(limit = 10) {
  return request({
    url: '/courses/featured',
    method: 'get',
    params: { limit }
  })
}

// 获取课程统计
export function getCourseStats(instructorId) {
  return request({
    url: '/courses/stats',
    method: 'get',
    params: instructorId ? { instructorId } : {}
  })
} 