import request from '@/utils/request'

// 获取可用时间段
export function getAvailableSchedules(params) {
  return request({
    url: '/schedules/available',
    method: 'get',
    params: params
  })
}

// 获取时间段列表
export function getScheduleList(params) {
  return request({
    url: '/schedules/list',
    method: 'get',
    params: params
  })
}

// 获取时间段详情
export function getScheduleDetail(id) {
  return request({
    url: `/schedules/${id}`,
    method: 'get'
  })
}

// 创建时间段
export function createSchedule(data) {
  return request({
    url: '/schedules',
    method: 'post',
    data: data
  })
}

// 更新时间段
export function updateSchedule(id, data) {
  return request({
    url: `/schedules/${id}`,
    method: 'put',
    data: data
  })
}

// 删除时间段
export function deleteSchedule(id) {
  return request({
    url: `/schedules/${id}`,
    method: 'delete'
  })
}

// 获取教练员时间段
export function getInstructorSchedules(instructorId, params) {
  return request({
    url: `/schedules/instructor/${instructorId}`,
    method: 'get',
    params: params
  })
}

// 批量更新时间段状态
export function batchUpdateScheduleStatus(data) {
  return request({
    url: '/schedules/batch/status',
    method: 'patch',
    data: data
  })
}

// 获取时间段统计
export function getScheduleStats(params) {
  return request({
    url: '/schedules/stats/overview',
    method: 'get',
    params: params
  })
} 