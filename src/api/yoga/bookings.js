import request from '@/utils/request'

// 获取预约列表
export function getBookingList(params) {
  return request({
    url: '/bookings',
    method: 'get',
    params: params
  })
}

// 获取预约详情
export function getBookingDetail(id) {
  return request({
    url: `/bookings/${id}`,
    method: 'get'
  })
}

// 创建预约
export function createBooking(data) {
  return request({
    url: '/bookings',
    method: 'post',
    data: data
  })
}

// 更新预约
export function updateBooking(id, data) {
  return request({
    url: `/bookings/${id}`,
    method: 'put',
    data: data
  })
}

// 取消预约
export function cancelBooking(id, data) {
  return request({
    url: `/bookings/${id}/cancel`,
    method: 'patch',
    data: data
  })
}

// 确认预约（教练员）
export function confirmBooking(id) {
  return request({
    url: `/bookings/${id}/confirm`,
    method: 'patch'
  })
}

// 标记出席（教练员）
export function markAttendance(id, data) {
  return request({
    url: `/bookings/${id}/attendance`,
    method: 'patch',
    data: data
  })
}

// 添加评价
export function addRating(id, data) {
  return request({
    url: `/bookings/${id}/rating`,
    method: 'post',
    data: data
  })
}

// 获取用户预约历史
export function getUserBookings(userId, params) {
  return request({
    url: userId ? `/bookings/user/${userId}` : '/bookings/user',
    method: 'get',
    params: params
  })
}

// 获取教练员预约
export function getInstructorBookings(instructorId, params) {
  return request({
    url: instructorId ? `/bookings/instructor/${instructorId}` : '/bookings/instructor',
    method: 'get',
    params: params
  })
}

// 获取预约统计
export function getBookingStats(params) {
  return request({
    url: '/bookings/stats/overview',
    method: 'get',
    params: params
  })
}

// 批量更新预约状态（管理员）
export function batchUpdateBookingStatus(data) {
  return request({
    url: '/bookings/batch/status',
    method: 'patch',
    data: data
  })
} 