<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瑜伽平台登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .login-container {
            background: white;
            color: #333;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        button:hover {
            background: #5a67d8;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .captcha-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .captcha-input {
            flex: 1;
        }
        .captcha-img {
            width: 120px;
            height: 40px;
            border: 1px solid #ddd;
            cursor: pointer;
            border-radius: 3px;
        }
        h1 {
            text-align: center;
            color: #667eea;
            margin-bottom: 30px;
        }
        .status {
            text-align: center;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>🧘‍♀️ 瑜伽平台登录测试</h1>
        
        <div class="status">
            <p>前端服务: <span id="frontendStatus">检测中...</span></p>
            <p>后端服务: <span id="backendStatus">检测中...</span></p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            
            <div class="form-group">
                <label for="code">验证码:</label>
                <div class="captcha-container">
                    <input type="text" id="code" class="captcha-input" placeholder="点击图片获取验证码">
                    <img id="captchaImg" class="captcha-img" src="" alt="验证码" onclick="getCaptcha()">
                </div>
            </div>
            
            <button type="submit" id="loginBtn">登录</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:3001';
        let captchaUuid = '';

        // 检查服务状态
        async function checkServices() {
            // 检查前端服务
            try {
                const response = await fetch(`${API_BASE}/`);
                document.getElementById('frontendStatus').innerHTML = '<span style="color: green;">✅ 正常</span>';
            } catch (error) {
                document.getElementById('frontendStatus').innerHTML = '<span style="color: red;">❌ 异常</span>';
            }

            // 检查后端API
            try {
                const response = await fetch(`${API_BASE}/captchaImage`);
                const data = await response.json();
                document.getElementById('backendStatus').innerHTML = '<span style="color: green;">✅ 正常</span>';
                return true;
            } catch (error) {
                document.getElementById('backendStatus').innerHTML = '<span style="color: red;">❌ 异常</span>';
                return false;
            }
        }

        // 获取验证码
        async function getCaptcha() {
            try {
                const response = await fetch(`${API_BASE}/captchaImage`);
                const data = await response.json();
                
                if (data.code === 200) {
                    document.getElementById('captchaImg').src = data.img;
                    captchaUuid = data.uuid;
                    showResult('验证码获取成功', 'info');
                } else {
                    showResult('验证码获取失败: ' + data.msg, 'error');
                }
            } catch (error) {
                showResult('验证码获取失败: ' + error.message, 'error');
            }
        }

        // 登录函数
        async function login(username, password, code, uuid) {
            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username,
                        password,
                        code,
                        uuid
                    })
                });

                const data = await response.json();
                
                if (data.code === 200) {
                    showResult(`登录成功！Token: ${data.token.substring(0, 30)}...`, 'success');
                    
                    // 获取用户信息
                    setTimeout(() => getUserInfo(data.token), 1000);
                } else {
                    showResult('登录失败: ' + data.msg, 'error');
                    getCaptcha(); // 重新获取验证码
                }
            } catch (error) {
                showResult('登录失败: ' + error.message, 'error');
            }
        }

        // 获取用户信息
        async function getUserInfo(token) {
            try {
                const response = await fetch(`${API_BASE}/getInfo`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();
                
                if (data.code === 200) {
                    const user = data.user;
                    showResult(`欢迎您，${user.nickName}！用户名: ${user.userName}，角色: ${data.roles.join(', ')}`, 'success');
                } else {
                    showResult('获取用户信息失败: ' + data.msg, 'error');
                }
            } catch (error) {
                showResult('获取用户信息失败: ' + error.message, 'error');
            }
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = message;
            resultDiv.className = `result ${type}`;
        }

        // 表单提交事件
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const code = document.getElementById('code').value;
            
            if (!captchaUuid) {
                showResult('请先获取验证码', 'error');
                return;
            }

            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';

            await login(username, password, code, captchaUuid);

            loginBtn.disabled = false;
            loginBtn.textContent = '登录';
        });

        // 页面加载时检查服务状态并获取验证码
        window.addEventListener('load', async () => {
            const servicesOk = await checkServices();
            if (servicesOk) {
                await getCaptcha();
            }
        });
    </script>
</body>
</html> 