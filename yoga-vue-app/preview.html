<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瑜伽空间 - Vue App 预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
            background-color: #F2F2F7;
        }
        
        .apple-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            padding: 24px;
        }
        
        .apple-btn-primary {
            background: #007AFF;
            color: white;
            font-weight: 500;
            padding: 12px 24px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.2s;
            border: none;
            cursor: pointer;
        }
        
        .apple-btn-primary:active {
            transform: scale(0.95);
        }
        
        .apple-input {
            width: 100%;
            padding: 12px 16px;
            background: white;
            border: 1px solid #D1D1D6;
            border-radius: 12px;
            outline: none;
            transition: all 0.2s;
        }
        
        .apple-input:focus {
            border-color: #007AFF;
            box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
        }
        
        .floating-animation {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .glass-morphism {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .mobile-frame {
            width: 390px;
            height: 844px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            margin: 20px auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .mobile-screen {
            width: 100%;
            height: 100%;
            background: #F2F2F7;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        
        .page {
            display: none;
            height: 100%;
            overflow-y: auto;
        }
        
        .page.active {
            display: block;
        }
        
        .nav-tab {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #8E8E93;
            transition: color 0.2s;
            cursor: pointer;
        }
        
        .nav-tab.active {
            color: #007AFF;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">瑜伽空间 Vue App</h1>
        <p class="text-gray-600">苹果设计风格的瑜伽Web应用</p>
    </div>

    <div class="mobile-frame">
        <div class="mobile-screen">
            <!-- 登录页面 -->
            <div id="login" class="page active">
                <div class="h-full bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800 flex items-center justify-center p-4 relative">
                    <!-- 浮动装饰 -->
                    <div class="absolute top-20 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full floating-animation"></div>
                    <div class="absolute top-40 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full floating-animation" style="animation-delay: 1s;"></div>
                    
                    <div class="w-full max-w-sm relative z-10">
                        <!-- Logo -->
                        <div class="text-center mb-8">
                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl mx-auto mb-4 flex items-center justify-center backdrop-blur-sm">
                                <i class="fas fa-heart text-white text-3xl"></i>
                            </div>
                            <h1 class="text-3xl font-bold text-white mb-2">瑜伽空间</h1>
                            <p class="text-white text-opacity-80">找到内心的平静</p>
                        </div>
                        
                        <!-- 登录表单 -->
                        <div class="glass-morphism rounded-2xl p-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">用户名或邮箱</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                        <input type="text" class="apple-input pl-10" placeholder="请输入用户名或邮箱" />
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <input type="password" class="apple-input pl-10 pr-10" placeholder="请输入密码" />
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                            <i class="fas fa-eye text-gray-400 cursor-pointer"></i>
                                        </div>
                                    </div>
                                </div>
                                
                                <button onclick="showPage('home')" class="apple-btn-primary w-full text-lg font-semibold">
                                    登录
                                </button>
                            </div>
                            
                            <div class="mt-6 text-center">
                                <span class="text-sm text-gray-600">还没有账户？</span>
                                <button onclick="showPage('register')" class="text-sm text-blue-600 font-medium ml-1">
                                    立即注册
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 注册页面 -->
            <div id="register" class="page">
                <div class="h-full bg-gradient-to-br from-green-600 via-green-700 to-teal-800 flex items-center justify-center p-4 relative">
                    <!-- 浮动装饰 -->
                    <div class="absolute top-16 right-10 w-24 h-24 bg-white bg-opacity-10 rounded-full floating-animation"></div>
                    
                    <div class="w-full max-w-sm relative z-10">
                        <!-- Logo -->
                        <div class="text-center mb-8">
                            <div class="w-20 h-20 bg-white bg-opacity-20 rounded-2xl mx-auto mb-4 flex items-center justify-center backdrop-blur-sm">
                                <i class="fas fa-heart text-white text-3xl"></i>
                            </div>
                            <h1 class="text-3xl font-bold text-white mb-2">加入瑜伽空间</h1>
                            <p class="text-white text-opacity-80">开启你的瑜伽之旅</p>
                        </div>
                        
                        <!-- 注册表单 -->
                        <div class="glass-morphism rounded-2xl p-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                        <input type="text" class="apple-input pl-10" placeholder="请输入用户名" />
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                            <i class="fas fa-envelope text-gray-400"></i>
                                        </div>
                                        <input type="email" class="apple-input pl-10" placeholder="请输入邮箱地址" />
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <input type="password" class="apple-input pl-10" placeholder="请输入密码" />
                                    </div>
                                </div>
                                
                                <button onclick="showPage('home')" class="apple-btn-primary w-full text-lg font-semibold bg-green-600">
                                    注册
                                </button>
                            </div>
                            
                            <div class="mt-6 text-center">
                                <span class="text-sm text-gray-600">已有账户？</span>
                                <button onclick="showPage('login')" class="text-sm text-green-600 font-medium ml-1">
                                    立即登录
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主页 -->
            <div id="home" class="page">
                <!-- 状态栏 -->
                <div class="bg-white h-11 flex items-center justify-between px-4 text-sm font-medium">
                    <span>9:41</span>
                    <div class="flex items-center space-x-1">
                        <div class="flex space-x-1">
                            <div class="w-1 h-1 bg-black rounded-full"></div>
                            <div class="w-1 h-1 bg-black rounded-full"></div>
                            <div class="w-1 h-1 bg-black rounded-full"></div>
                            <div class="w-1 h-1 bg-black bg-opacity-30 rounded-full"></div>
                        </div>
                        <div class="ml-2 w-6 h-3 border border-black rounded-sm">
                            <div class="w-4 h-2 bg-green-500 rounded-sm m-0.5"></div>
                        </div>
                    </div>
                </div>

                <!-- 导航栏 -->
                <div class="bg-white bg-opacity-95 backdrop-blur border-b border-gray-100 px-4 py-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-heart text-white text-sm"></i>
                            </div>
                            <div>
                                <h1 class="font-bold text-lg text-gray-900">瑜伽空间</h1>
                                <p class="text-xs text-gray-500">早上好，用户</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-bell text-gray-600 text-sm"></i>
                            </button>
                            <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full"></div>
                        </div>
                    </div>
                </div>

                <!-- Hero区域 -->
                <div class="px-4 py-6">
                    <div class="relative bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800 rounded-2xl overflow-hidden">
                        <div class="relative z-10 p-6 text-white">
                            <h2 class="text-2xl font-bold mb-2">今日练习</h2>
                            <p class="text-white text-opacity-90 mb-4">开始你的瑜伽之旅，找到内心的平静</p>
                            
                            <div class="flex items-center space-x-4 mb-6">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-clock text-white text-opacity-80"></i>
                                    <span class="text-sm">30分钟</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <span class="text-sm">初级</span>
                                </div>
                            </div>
                            
                            <button onclick="showPage('course-detail')" class="bg-white text-purple-700 px-6 py-3 rounded-lg font-medium flex items-center space-x-2">
                                <i class="fas fa-play text-sm"></i>
                                <span>开始练习</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 快捷功能 -->
                <div class="px-4 pb-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">快捷功能</h3>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div onclick="showPage('courses')" class="apple-card cursor-pointer hover:shadow-lg transition-shadow">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-play text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">课程浏览</h4>
                                    <p class="text-xs text-gray-500">50+ 专业课程</p>
                                </div>
                            </div>
                            <p class="text-sm text-gray-600">探索丰富的瑜伽课程库</p>
                        </div>

                        <div onclick="showPage('bookings')" class="apple-card cursor-pointer hover:shadow-lg transition-shadow">
                            <div class="flex items-center space-x-3 mb-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-calendar text-white text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">预约课程</h4>
                                    <p class="text-xs text-gray-500">即时预约</p>
                                </div>
                            </div>
                            <p class="text-sm text-gray-600">预约线下瑜伽课程</p>
                        </div>
                    </div>
                </div>

                <!-- 底部导航 -->
                <div class="fixed bottom-0 left-0 right-0 bg-white bg-opacity-95 backdrop-blur border-t border-gray-100 px-4 py-2">
                    <div class="flex items-center justify-around max-w-sm mx-auto">
                        <div class="nav-tab active py-2 px-3" onclick="showPage('home')">
                            <i class="fas fa-home text-lg mb-1"></i>
                            <span class="text-xs font-medium">首页</span>
                        </div>
                        <div class="nav-tab py-2 px-3" onclick="showPage('courses')">
                            <i class="fas fa-play text-lg mb-1"></i>
                            <span class="text-xs font-medium">课程</span>
                        </div>
                        <div class="nav-tab py-2 px-3" onclick="showPage('bookings')">
                            <i class="fas fa-calendar text-lg mb-1"></i>
                            <span class="text-xs font-medium">预约</span>
                        </div>
                        <div class="nav-tab py-2 px-3" onclick="showPage('profile')">
                            <i class="fas fa-user text-lg mb-1"></i>
                            <span class="text-xs font-medium">我的</span>
                        </div>
                    </div>
                    <div class="flex justify-center mt-2">
                        <div class="w-32 h-1 bg-black rounded-full"></div>
                    </div>
                </div>
            </div>

            <!-- 课程列表页 -->
            <div id="courses" class="page">
                <!-- 状态栏和导航栏 -->
                <div class="bg-white h-11 flex items-center justify-between px-4 text-sm font-medium">
                    <span>9:41</span>
                    <div class="flex items-center space-x-1">
                        <div class="flex space-x-1">
                            <div class="w-1 h-1 bg-black rounded-full"></div>
                            <div class="w-1 h-1 bg-black rounded-full"></div>
                            <div class="w-1 h-1 bg-black rounded-full"></div>
                            <div class="w-1 h-1 bg-black bg-opacity-30 rounded-full"></div>
                        </div>
                        <div class="ml-2 w-6 h-3 border border-black rounded-sm">
                            <div class="w-4 h-2 bg-green-500 rounded-sm m-0.5"></div>
                        </div>
                    </div>
                </div>

                <div class="bg-white bg-opacity-95 backdrop-blur border-b border-gray-100 px-4 py-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <button onclick="showPage('home')" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-chevron-left text-gray-600"></i>
                            </button>
                            <h1 class="font-bold text-lg text-gray-900">瑜伽课程</h1>
                        </div>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="px-4 py-4">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" class="apple-input pl-10" placeholder="搜索瑜伽课程..." />
                    </div>
                </div>

                <!-- 课程卡片 -->
                <div class="px-4 pb-20">
                    <div class="space-y-4">
                        <div onclick="showPage('course-detail')" class="bg-white rounded-2xl overflow-hidden shadow-lg cursor-pointer">
                            <div class="h-48 bg-gradient-to-br from-purple-400 to-pink-400 relative">
                                <div class="absolute top-3 right-3">
                                    <button class="w-8 h-8 bg-white bg-opacity-20 backdrop-blur-sm rounded-full flex items-center justify-center">
                                        <i class="far fa-heart text-white"></i>
                                    </button>
                                </div>
                                <div class="absolute bottom-3 left-3">
                                    <span class="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs font-medium">初级</span>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-bold text-lg text-gray-900 mb-2">晨间瑜伽唤醒</h3>
                                <p class="text-sm text-gray-600 mb-3">温和的晨间瑜伽序列，帮助你开启活力的一天</p>
                                
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full"></div>
                                        <span class="text-sm text-gray-700 font-medium">李老师</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-star text-yellow-400 text-sm"></i>
                                        <span class="text-sm text-gray-600">4.8</span>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-clock"></i>
                                        <span>20分钟</span>
                                    </div>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-users"></i>
                                        <span>1250 学员</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部导航 -->
                <div class="fixed bottom-0 left-0 right-0 bg-white bg-opacity-95 backdrop-blur border-t border-gray-100 px-4 py-2">
                    <div class="flex items-center justify-around max-w-sm mx-auto">
                        <div class="nav-tab py-2 px-3" onclick="showPage('home')">
                            <i class="fas fa-home text-lg mb-1"></i>
                            <span class="text-xs font-medium">首页</span>
                        </div>
                        <div class="nav-tab active py-2 px-3" onclick="showPage('courses')">
                            <i class="fas fa-play text-lg mb-1"></i>
                            <span class="text-xs font-medium">课程</span>
                        </div>
                        <div class="nav-tab py-2 px-3" onclick="showPage('bookings')">
                            <i class="fas fa-calendar text-lg mb-1"></i>
                            <span class="text-xs font-medium">预约</span>
                        </div>
                        <div class="nav-tab py-2 px-3" onclick="showPage('profile')">
                            <i class="fas fa-user text-lg mb-1"></i>
                            <span class="text-xs font-medium">我的</span>
                        </div>
                    </div>
                    <div class="flex justify-center mt-2">
                        <div class="w-32 h-1 bg-black rounded-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-8 text-gray-600">
        <p class="mb-4">🎯 <strong>特色功能</strong></p>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto">
            <div class="bg-white p-4 rounded-lg shadow">
                <h3 class="font-semibold mb-2">苹果设计规范</h3>
                <p class="text-sm">严格遵循Apple Human Interface Guidelines，提供最佳的用户体验</p>
            </div>
            <div class="bg-white p-4 rounded-lg shadow">
                <h3 class="font-semibold mb-2">Vue 3 + TypeScript</h3>
                <p class="text-sm">使用最新的Vue 3 Composition API和TypeScript，确保代码质量</p>
            </div>
            <div class="bg-white p-4 rounded-lg shadow">
                <h3 class="font-semibold mb-2">Tailwind CSS</h3>
                <p class="text-sm">使用Tailwind CSS快速构建美观的界面，支持深色模式</p>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            
            // 更新导航状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            showPage('login');
        });
    </script>
</body>
</html> 