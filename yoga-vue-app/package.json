{"name": "yoga-vue-app", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.5.0", "@fortawesome/free-regular-svg-icons": "^6.5.0", "@fortawesome/free-solid-svg-icons": "^6.5.0", "@fortawesome/vue-fontawesome": "^3.0.6", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^18.19.0", "@vitejs/plugin-vue": "^4.6.2", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "~5.3.0", "vite": "^4.5.0", "vue-tsc": "^1.8.27"}}