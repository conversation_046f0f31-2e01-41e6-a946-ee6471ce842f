import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// FontAwesome配置
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { 
  faUser, 
  faLock, 
  faEye, 
  faEyeSlash, 
  faEnvelope, 
  faPhone,
  faHome,
  faCalendar,
  faCalendarDays,
  faCalendarTimes,
  faSearch,
  faHeart,
  faPlay,
  faClock,
  faMapMarkerAlt,
  faStar,
  faChevronLeft,
  faChevronRight,
  faBars,
  faPlus,
  faMinus,
  faCheck,
  faTimes,
  faFilter,
  faSort,
  faBell,
  faShoppingCart,
  faBookmark,
  faShare,
  faDownload,
  faUpload,
  faEdit,
  faTrash,
  faInfo,
  faExclamationTriangle,
  faCheckCircle,
  faTimesCircle,
  faSpinner,
  faUsers,
  faChartLine,
  faSignOutAlt,
  faRefresh,
  faExclamationCircle,
  faStickyNote
} from '@fortawesome/free-solid-svg-icons'

import {
  faHeart as faHeartRegular,
  faBookmark as faBookmarkRegular,
  faUser as faUserRegular,
  faCalendar as faCalendarRegular
} from '@fortawesome/free-regular-svg-icons'

// 添加图标到库
library.add(
  faUser, faLock, faEye, faEyeSlash, faEnvelope, faPhone,
  faHome, faCalendar, faCalendarDays, faCalendarTimes, faSearch, faHeart, faPlay, faClock,
  faMapMarkerAlt, faStar, faChevronLeft, faChevronRight,
  faBars, faPlus, faMinus, faCheck, faTimes, faFilter,
  faSort, faBell, faShoppingCart, faBookmark, faShare,
  faDownload, faUpload, faEdit, faTrash, faInfo,
  faExclamationTriangle, faCheckCircle, faTimesCircle,
  faSpinner, faUsers, faChartLine, faSignOutAlt, faRefresh, faExclamationCircle,
  faStickyNote, faHeartRegular, faBookmarkRegular, faUserRegular, faCalendarRegular
)

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.component('font-awesome-icon', FontAwesomeIcon)

app.mount('#app')
