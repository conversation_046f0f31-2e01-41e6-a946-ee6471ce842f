<template>
  <div class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-apple border-t border-apple-gray-100 px-4 py-2 safe-area-inset-bottom">
    <div class="flex items-center justify-around">
      <!-- 首页 -->
      <router-link
        to="/"
        class="apple-nav-tab py-2 px-3"
        :class="{ active: $route.path === '/' }"
      >
        <font-awesome-icon icon="home" class="text-lg mb-1" />
        <span class="text-xs font-medium">首页</span>
      </router-link>

      <!-- 课程 -->
      <router-link
        to="/courses"
        class="apple-nav-tab py-2 px-3"
        :class="{ active: $route.path === '/courses' }"
      >
        <font-awesome-icon icon="play" class="text-lg mb-1" />
        <span class="text-xs font-medium">课程</span>
      </router-link>

      <!-- 每日课表 -->
      <router-link
        to="/schedule"
        class="apple-nav-tab py-2 px-3"
        :class="{ active: $route.path === '/schedule' }"
      >
        <font-awesome-icon icon="calendar-alt" class="text-lg mb-1" />
        <span class="text-xs font-medium">课程表</span>
      </router-link>

      <!-- 预约 -->
      <router-link
        to="/bookings"
        class="apple-nav-tab py-2 px-3"
        :class="{ active: $route.path === '/bookings' }"
      >
        <font-awesome-icon icon="calendar" class="text-lg mb-1" />
        <span class="text-xs font-medium">预约</span>
      </router-link>

      <!-- 我的 -->
      <router-link
        to="/profile"
        class="apple-nav-tab py-2 px-3"
        :class="{ active: $route.path === '/profile' }"
      >
        <font-awesome-icon icon="user" class="text-lg mb-1" />
        <span class="text-xs font-medium">我的</span>
      </router-link>
    </div>
    
    <!-- Home指示器 -->
    <div class="flex justify-center mt-2">
      <div class="w-32 h-1 bg-black rounded-full"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 底部导航组件，无需额外逻辑
</script>

<style scoped>
.safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
</style> 