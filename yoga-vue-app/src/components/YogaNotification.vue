<template>
  <Teleport to="body">
    <Transition name="notification-slide" appear>
      <div 
        v-if="visible" 
        class="yoga-notification"
        :class="[`notification-${type}`, { 'notification-floating': floating }]"
        @click="handleClick"
      >
        <!-- 图标区域 -->
        <div class="notification-icon">
          <div v-if="type === 'success'" class="icon-success">
            ✓
          </div>
          <div v-else-if="type === 'error'" class="icon-error">
            ✕
          </div>
          <div v-else-if="type === 'warning'" class="icon-warning">
            ⚠
          </div>
          <div v-else-if="type === 'info'" class="icon-info">
            ℹ
          </div>
          <div v-else-if="type === 'yoga'" class="icon-yoga">
            🧘
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="notification-content">
          <div v-if="title" class="notification-title">{{ title }}</div>
          <div class="notification-message">{{ message }}</div>
        </div>

        <!-- 关闭按钮 -->
        <button 
          v-if="closable" 
          @click.stop="close"
          class="notification-close"
        >
          ×
        </button>

        <!-- 操作按钮 -->
        <div v-if="action" class="notification-action">
          <button 
            @click.stop="handleAction"
            class="action-button"
          >
            {{ action.text }}
          </button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface ActionButton {
  text: string
  handler: () => void
}

interface Props {
  type?: 'success' | 'error' | 'warning' | 'info' | 'yoga'
  title?: string
  message: string
  duration?: number // 自动关闭时间（毫秒），0表示不自动关闭
  closable?: boolean
  floating?: boolean // 是否浮动样式
  action?: ActionButton
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  duration: 3000,
  closable: true,
  floating: false
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(false)
let timer: number | null = null

const show = () => {
  visible.value = true
  startTimer()
}

const close = () => {
  visible.value = false
  clearTimer()
  emit('close')
}

const handleClick = () => {
  if (!props.closable) return
  close()
}

const handleAction = () => {
  if (props.action) {
    props.action.handler()
  }
}

const startTimer = () => {
  if (props.duration > 0) {
    timer = setTimeout(() => {
      close()
    }, props.duration)
  }
}

const clearTimer = () => {
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
}

onMounted(() => {
  show()
})

onUnmounted(() => {
  clearTimer()
})

// 暴露方法供外部调用
defineExpose({
  show,
  close
})
</script>

<style scoped>
.yoga-notification {
  position: fixed;
  top: 60px;
  right: 20px;
  max-width: 360px;
  min-width: 280px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  z-index: 10000;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.yoga-notification:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
}

.notification-floating {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-4px); }
}

/* 图标样式 */
.notification-icon {
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 14px;
}

.icon-success {
  background: linear-gradient(135deg, #34d399, #10b981);
  color: white;
  box-shadow: 0 2px 8px rgba(52, 211, 153, 0.3);
}

.icon-error {
  background: linear-gradient(135deg, #f87171, #ef4444);
  color: white;
  box-shadow: 0 2px 8px rgba(248, 113, 113, 0.3);
}

.icon-warning {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.icon-info {
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  color: white;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
}

.icon-yoga {
  background: linear-gradient(135deg, #a855f7, #8b5cf6);
  color: white;
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.3);
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 内容样式 */
.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  line-height: 1.3;
}

.notification-message {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 关闭按钮 */
.notification-close {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-close:hover {
  background: rgba(107, 114, 128, 0.2);
  color: #374151;
  transform: scale(1.1);
}

/* 操作按钮 */
.notification-action {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

.action-button {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: #0056b3;
  transform: scale(1.05);
}

.action-button:active {
  transform: scale(0.95);
}

/* 类型特定样式 */
.notification-success {
  border-left: 4px solid #34d399;
}

.notification-error {
  border-left: 4px solid #f87171;
}

.notification-warning {
  border-left: 4px solid #fbbf24;
}

.notification-info {
  border-left: 4px solid #60a5fa;
}

.notification-yoga {
  border-left: 4px solid #a855f7;
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.05), rgba(139, 92, 246, 0.05));
}

/* 过渡动画 */
.notification-slide-enter-active {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.notification-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 1, 1);
}

.notification-slide-enter-from {
  transform: translateX(100%) scale(0.9);
  opacity: 0;
}

.notification-slide-leave-to {
  transform: translateX(100%) scale(0.9);
  opacity: 0;
}

/* 移动端适配 */
@media (max-width: 640px) {
  .yoga-notification {
    left: 16px;
    right: 16px;
    max-width: none;
    min-width: auto;
  }
  
  .notification-slide-enter-from,
  .notification-slide-leave-to {
    transform: translateY(-100%) scale(0.9);
  }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .yoga-notification {
    background: rgba(28, 28, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .notification-title {
    color: #f2f2f7;
  }
  
  .notification-message {
    color: #a1a1aa;
  }
  
  .notification-close {
    background: rgba(255, 255, 255, 0.1);
    color: #a1a1aa;
  }
  
  .notification-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #f2f2f7;
  }
}
</style> 