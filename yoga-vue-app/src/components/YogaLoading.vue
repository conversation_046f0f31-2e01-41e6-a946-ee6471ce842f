<template>
  <div 
    v-if="show" 
    class="yoga-loading-overlay"
    :class="{ 'backdrop-blur': useBackdrop }"
  >
    <div class="yoga-loading-container">
      <!-- 瑜伽姿势加载动画 -->
      <div v-if="type === 'yoga-pose'" class="yoga-pose-loader">
        <div class="yoga-figure">
          <div class="head"></div>
          <div class="body"></div>
          <div class="arms">
            <div class="arm left"></div>
            <div class="arm right"></div>
          </div>
          <div class="legs">
            <div class="leg left"></div>
            <div class="leg right"></div>
          </div>
        </div>
        <div class="breathing-circle"></div>
      </div>

      <!-- 莲花加载动画 -->
      <div v-else-if="type === 'lotus'" class="lotus-loader">
        <div class="lotus-petals">
          <div class="petal" v-for="i in 8" :key="i" :style="{ transform: `rotate(${i * 45}deg)` }"></div>
        </div>
        <div class="lotus-center"></div>
      </div>

      <!-- 呼吸球加载动画 -->
      <div v-else-if="type === 'breathing'" class="breathing-loader">
        <div class="breathing-sphere">
          <div class="sphere-layer" v-for="i in 3" :key="i"></div>
        </div>
      </div>

      <!-- 禅意波纹加载动画 -->
      <div v-else-if="type === 'ripple'" class="ripple-loader">
        <div class="ripple" v-for="i in 3" :key="i"></div>
      </div>

      <!-- 脉轮旋转加载动画 -->
      <div v-else-if="type === 'chakra'" class="chakra-loader">
        <div class="chakra-ring" v-for="i in 7" :key="i" :class="`chakra-${i}`"></div>
      </div>

      <!-- 默认苹果风格旋转器 -->
      <div v-else class="apple-spinner">
        <div class="spinner-ring"></div>
      </div>

      <!-- 加载文本 -->
      <div v-if="text" class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Props {
  show: boolean
  type?: 'yoga-pose' | 'lotus' | 'breathing' | 'ripple' | 'chakra' | 'apple'
  text?: string
  useBackdrop?: boolean
  duration?: number // 自动隐藏时间（毫秒）
}

const props = withDefaults(defineProps<Props>(), {
  type: 'yoga-pose',
  text: '正在加载...',
  useBackdrop: true,
  duration: 0
})

const emit = defineEmits<{
  hide: []
}>()

let timer: NodeJS.Timeout | null = null

onMounted(() => {
  if (props.duration > 0) {
    timer = setTimeout(() => {
      emit('hide')
    }, props.duration)
  }
})

onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
})
</script>

<style scoped>
.yoga-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 250, 252, 0.95);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.yoga-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 瑜伽姿势加载动画 */
.yoga-pose-loader {
  position: relative;
  width: 80px;
  height: 100px;
}

.yoga-figure {
  position: relative;
  width: 100%;
  height: 100%;
  animation: yogaPose 3s ease-in-out infinite;
}

.head {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.body {
  width: 8px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  position: absolute;
  top: 22px;
  left: 50%;
  transform: translateX(-50%);
}

.arms {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
}

.arm {
  width: 20px;
  height: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  position: absolute;
}

.arm.left {
  left: -25px;
  transform: rotate(-45deg);
  transform-origin: right center;
}

.arm.right {
  right: -25px;
  transform: rotate(45deg);
  transform-origin: left center;
}

.legs {
  position: absolute;
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
}

.leg {
  width: 25px;
  height: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3px;
  position: absolute;
}

.leg.left {
  left: -20px;
  transform: rotate(-60deg);
  transform-origin: right center;
}

.leg.right {
  right: -20px;
  transform: rotate(60deg);
  transform-origin: left center;
}

.breathing-circle {
  position: absolute;
  width: 120px;
  height: 120px;
  border: 2px solid rgba(102, 126, 234, 0.3);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: breathe 4s ease-in-out infinite;
}

@keyframes yogaPose {
  0%, 100% { transform: scale(1) rotate(0deg); }
  33% { transform: scale(1.05) rotate(-2deg); }
  66% { transform: scale(0.95) rotate(2deg); }
}

@keyframes breathe {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
}

/* 莲花加载动画 */
.lotus-loader {
  position: relative;
  width: 80px;
  height: 80px;
}

.lotus-petals {
  position: relative;
  width: 100%;
  height: 100%;
  animation: lotusRotate 4s linear infinite;
}

.petal {
  position: absolute;
  width: 8px;
  height: 25px;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  top: 5px;
  left: 50%;
  transform-origin: 50% 35px;
  opacity: 0.8;
  animation: petalPulse 2s ease-in-out infinite;
}

.lotus-center {
  position: absolute;
  width: 16px;
  height: 16px;
  background: radial-gradient(circle, #ffd89b 0%, #19547b 100%);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: centerGlow 3s ease-in-out infinite;
}

@keyframes lotusRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes petalPulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

@keyframes centerGlow {
  0%, 100% { box-shadow: 0 0 10px rgba(255, 216, 155, 0.5); }
  50% { box-shadow: 0 0 20px rgba(255, 216, 155, 0.8); }
}

/* 呼吸球加载动画 */
.breathing-loader {
  position: relative;
  width: 80px;
  height: 80px;
}

.breathing-sphere {
  position: relative;
  width: 100%;
  height: 100%;
}

.sphere-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid transparent;
}

.sphere-layer:nth-child(1) {
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.3), transparent);
  animation: sphereBreathe 3s ease-in-out infinite;
}

.sphere-layer:nth-child(2) {
  background: linear-gradient(135deg, rgba(255, 154, 158, 0.3), transparent);
  animation: sphereBreathe 3s ease-in-out infinite 0.5s;
}

.sphere-layer:nth-child(3) {
  background: linear-gradient(225deg, rgba(255, 206, 84, 0.3), transparent);
  animation: sphereBreathe 3s ease-in-out infinite 1s;
}

@keyframes sphereBreathe {
  0%, 100% { transform: scale(0.8); opacity: 0.3; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

/* 禅意波纹加载动画 */
.ripple-loader {
  position: relative;
  width: 80px;
  height: 80px;
}

.ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  border: 2px solid rgba(102, 126, 234, 0.6);
  transform: translate(-50%, -50%);
  animation: rippleExpand 2s ease-out infinite;
}

.ripple:nth-child(2) {
  animation-delay: 0.5s;
}

.ripple:nth-child(3) {
  animation-delay: 1s;
}

@keyframes rippleExpand {
  0% {
    width: 0;
    height: 0;
    opacity: 0.8;
  }
  70% {
    width: 80px;
    height: 80px;
    opacity: 0.3;
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

/* 脉轮旋转加载动画 */
.chakra-loader {
  position: relative;
  width: 80px;
  height: 80px;
}

.chakra-ring {
  position: absolute;
  border-radius: 50%;
  border: 2px solid transparent;
  animation: chakraRotate 3s linear infinite;
}

.chakra-1 { 
  width: 80px; height: 80px; top: 0; left: 0;
  border-top-color: #ff4757; /* 红色 - 海底轮 */
  animation-duration: 3s;
}

.chakra-2 { 
  width: 70px; height: 70px; top: 5px; left: 5px;
  border-top-color: #ff7f27; /* 橙色 - 脐轮 */
  animation-duration: 2.7s;
}

.chakra-3 { 
  width: 60px; height: 60px; top: 10px; left: 10px;
  border-top-color: #ffa502; /* 黄色 - 太阳轮 */
  animation-duration: 2.4s;
}

.chakra-4 { 
  width: 50px; height: 50px; top: 15px; left: 15px;
  border-top-color: #3ed400; /* 绿色 - 心轮 */
  animation-duration: 2.1s;
}

.chakra-5 { 
  width: 40px; height: 40px; top: 20px; left: 20px;
  border-top-color: #007AFF; /* 蓝色 - 喉轮 */
  animation-duration: 1.8s;
}

.chakra-6 { 
  width: 30px; height: 30px; top: 25px; left: 25px;
  border-top-color: #5352ed; /* 靛蓝 - 眉轮 */
  animation-duration: 1.5s;
}

.chakra-7 { 
  width: 20px; height: 20px; top: 30px; left: 30px;
  border-top-color: #a55eea; /* 紫色 - 顶轮 */
  animation-duration: 1.2s;
}

@keyframes chakraRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 苹果风格旋转器 */
.apple-spinner {
  width: 60px;
  height: 60px;
  position: relative;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 4px solid rgba(0, 122, 255, 0.2);
  border-left-color: #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载文本 */
.loading-text {
  margin-top: 24px;
  color: #1a1a1a;
  font-size: 16px;
  font-weight: 500;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', sans-serif;
  animation: textPulse 2s ease-in-out infinite;
}

@keyframes textPulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .yoga-loading-overlay {
    background: rgba(28, 28, 30, 0.95);
  }
  
  .loading-text {
    color: #f2f2f7;
  }
}
</style> 