@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 苹果设计系统基础样式 */
@layer base {
  * {
    -webkit-tap-highlight-color: transparent;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    background-color: #F2F2F7;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  input, button, textarea {
    font-family: inherit;
  }
}

/* 苹果风格组件样式 */
@layer components {
  .apple-btn-primary {
    @apply bg-apple-blue text-white font-medium py-3 px-6 rounded-apple shadow-apple transition-all duration-200 active:scale-95;
  }
  
  .apple-btn-secondary {
    @apply bg-white text-apple-blue border border-apple-blue font-medium py-3 px-6 rounded-apple shadow-apple transition-all duration-200 active:scale-95;
  }
  
  .apple-input {
    @apply w-full px-4 py-3 bg-white border border-apple-gray-200 rounded-apple focus:outline-none focus:ring-2 focus:ring-apple-blue focus:border-transparent transition-all duration-200;
  }
  
  /* 日期选择器样式 */
  .apple-date-picker {
    @apply px-4 py-3 bg-white border border-apple-gray-200 rounded-apple focus:outline-none focus:ring-2 focus:ring-apple-blue focus:border-transparent transition-all duration-200 text-center font-medium text-apple-gray-900;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23007AFF' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3e%3c/rect%3e%3cline x1='16' y1='2' x2='16' y2='6'%3e%3c/line%3e%3cline x1='8' y1='2' x2='8' y2='6'%3e%3c/line%3e%3cline x1='3' y1='10' x2='21' y2='10'%3e%3c/line%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 20px;
    padding-right: 44px;
  }
  
  .apple-date-picker::-webkit-calendar-picker-indicator {
    opacity: 0;
    position: absolute;
    right: 12px;
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
  
  /* 日期选择器容器 */
  .date-picker-container {
    @apply bg-white rounded-apple shadow-apple-card px-4 py-4 mb-4;
  }
  
  /* 日期选择器按钮 */
  .date-picker-btn {
    @apply p-3 text-apple-gray-600 hover:text-apple-blue hover:bg-apple-gray-50 rounded-full transition-all duration-200 active:scale-95;
  }
  
  .apple-card {
    @apply bg-white rounded-apple-lg shadow-apple-card p-6;
  }
  
  .apple-nav-tab {
    @apply flex flex-col items-center justify-center text-apple-gray-500 transition-colors duration-200 min-w-0 flex-1;
  }
  
  .apple-nav-tab.active {
    @apply text-apple-blue;
  }
  
  .glass-morphism {
    @apply bg-white/80 backdrop-blur-apple border border-white/20;
  }
  
  .floating-animation {
    animation: float 3s ease-in-out infinite;
  }
  
  .slide-enter-active,
  .slide-leave-active {
    transition: all 0.3s ease-out;
  }
  
  .slide-enter-from {
    transform: translateX(100%);
    opacity: 0;
  }
  
  .slide-leave-to {
    transform: translateX(-100%);
    opacity: 0;
  }
  
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }
  
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
}

/* 动画定义 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .mobile-container {
    @apply px-4 py-2;
  }
}
