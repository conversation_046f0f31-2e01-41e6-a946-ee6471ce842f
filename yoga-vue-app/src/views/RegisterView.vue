<template>
  <div class="min-h-screen bg-gradient-to-br from-green-600 via-green-700 to-teal-800 flex items-center justify-center p-4">
    <!-- 瑜伽加载动画 -->
    <YogaLoading 
      :show="loading" 
      type="pose"
      text="注册中..." 
    />
    
    <!-- 浮动装饰元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full floating-animation"></div>
      <div class="absolute top-40 right-20 w-16 h-16 bg-white/10 rounded-full floating-animation" style="animation-delay: 1s;"></div>
      <div class="absolute bottom-32 left-20 w-12 h-12 bg-white/10 rounded-full floating-animation" style="animation-delay: 2s;"></div>
    </div>
    
    <div class="w-full max-w-md relative z-10">
      <!-- Logo区域 -->
      <div class="text-center mb-8">
        <div class="w-20 h-20 bg-white/20 rounded-apple-xl mx-auto mb-4 flex items-center justify-center backdrop-blur-sm">
          <font-awesome-icon icon="heart" class="text-white text-3xl" />
        </div>
        <h1 class="text-3xl font-bold text-white mb-2">加入瑜伽空间</h1>
        <p class="text-white/80">开启你的瑜伽之旅</p>
      </div>
      
      <!-- 注册表单 -->
      <div class="glass-morphism rounded-apple-xl p-8 shadow-apple-float">
        <form @submit.prevent="handleRegister">
          <div class="space-y-6">
            <!-- 用户名输入 -->
            <div>
              <label class="block text-sm font-medium text-apple-gray-700 mb-2">用户名</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <font-awesome-icon icon="user" class="text-apple-gray-400" />
                </div>
                <input 
                  type="text" 
                  v-model="registerForm.username"
                  class="apple-input pl-10" 
                  placeholder="请输入用户名"
                  required
                />
              </div>
            </div>
            
            <!-- 邮箱输入 -->
            <div>
              <label class="block text-sm font-medium text-apple-gray-700 mb-2">邮箱</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <font-awesome-icon icon="envelope" class="text-apple-gray-400" />
                </div>
                <input 
                  type="email" 
                  v-model="registerForm.email"
                  class="apple-input pl-10" 
                  placeholder="请输入邮箱地址"
                  required
                />
              </div>
            </div>
            
            <!-- 手机号输入 -->
            <div>
              <label class="block text-sm font-medium text-apple-gray-700 mb-2">手机号</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <font-awesome-icon icon="phone" class="text-apple-gray-400" />
                </div>
                <input 
                  type="tel" 
                  v-model="registerForm.phone"
                  class="apple-input pl-10" 
                  placeholder="请输入手机号"
                  required
                />
              </div>
            </div>
            
            <!-- 密码输入 -->
            <div>
              <label class="block text-sm font-medium text-apple-gray-700 mb-2">密码</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <font-awesome-icon icon="lock" class="text-apple-gray-400" />
                </div>
                <input 
                  :type="showPassword ? 'text' : 'password'" 
                  v-model="registerForm.password"
                  class="apple-input pl-10 pr-10" 
                  placeholder="请输入密码"
                  required
                />
                <button 
                  type="button"
                  @click="showPassword = !showPassword"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <font-awesome-icon 
                    :icon="showPassword ? 'eye-slash' : 'eye'" 
                    class="text-apple-gray-400 hover:text-apple-gray-600 transition-colors" 
                  />
                </button>
              </div>
            </div>
            
            <!-- 确认密码输入 -->
            <div>
              <label class="block text-sm font-medium text-apple-gray-700 mb-2">确认密码</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <font-awesome-icon icon="lock" class="text-apple-gray-400" />
                </div>
                <input 
                  :type="showConfirmPassword ? 'text' : 'password'" 
                  v-model="registerForm.confirmPassword"
                  class="apple-input pl-10 pr-10" 
                  placeholder="请再次输入密码"
                  required
                />
                <button 
                  type="button"
                  @click="showConfirmPassword = !showConfirmPassword"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <font-awesome-icon 
                    :icon="showConfirmPassword ? 'eye-slash' : 'eye'" 
                    class="text-apple-gray-400 hover:text-apple-gray-600 transition-colors" 
                  />
                </button>
              </div>
            </div>
            
            <!-- 服务条款 -->
            <div class="flex items-start">
              <input 
                type="checkbox" 
                v-model="registerForm.agreeTerms"
                class="w-4 h-4 text-apple-green bg-white border-apple-gray-300 rounded focus:ring-apple-green focus:ring-2 mt-1"
                required
              />
              <div class="ml-3 text-sm">
                <span class="text-apple-gray-600">我已阅读并同意</span>
                <button type="button" class="text-apple-green hover:text-apple-green/80 transition-colors font-medium mx-1">
                  服务条款
                </button>
                <span class="text-apple-gray-600">和</span>
                <button type="button" class="text-apple-green hover:text-apple-green/80 transition-colors font-medium ml-1">
                  隐私政策
                </button>
              </div>
            </div>
            
            <!-- 注册按钮 -->
            <button 
              type="submit"
              :disabled="loading || !isFormValid"
              class="apple-btn-primary w-full text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed bg-apple-green hover:bg-apple-green/90 focus:ring-apple-green"
            >
              <font-awesome-icon v-if="loading" icon="spinner" class="animate-spin mr-2" />
              {{ loading ? '注册中...' : '注册' }}
            </button>
          </div>
        </form>
        
        <!-- 登录链接 -->
        <div class="mt-8 text-center">
          <span class="text-sm text-apple-gray-600">已有账户？</span>
          <router-link 
            to="/login"
            class="text-sm text-apple-green hover:text-apple-green/80 transition-colors font-medium ml-1"
          >
            立即登录
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { authAPI } from '../services/api'
import YogaLoading from '../components/YogaLoading.vue'
import { notification } from '../services/notification'

const router = useRouter()

// 表单数据
const registerForm = ref({
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  agreeTerms: false
})

const showPassword = ref(false)
const showConfirmPassword = ref(false)
const loading = ref(false)

// 表单验证
const isFormValid = computed(() => {
  return registerForm.value.username &&
         registerForm.value.email &&
         registerForm.value.password &&
         registerForm.value.confirmPassword &&
         registerForm.value.password === registerForm.value.confirmPassword &&
         registerForm.value.agreeTerms
})

// 注册处理
const handleRegister = async () => {
  if (!isFormValid.value) {
    return
  }
  
  loading.value = true
  
  try {
    const response = await authAPI.register({
      username: registerForm.value.username,
      email: registerForm.value.email,
      password: registerForm.value.password,
      fullName: registerForm.value.username // 使用用户名作为全名
    })
    
    if (response.success) {
      notification.success('注册成功！请登录')
      
      // 2秒后跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 2000)
    } else {
      notification.error(response.message || '注册失败')
    }
  } catch (error: any) {
    console.error('注册失败:', error)
    notification.error(error.message || '注册失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}
</script> 