<template>
  <div class="min-h-screen bg-apple-gray-50 p-4">
    <div class="max-w-md mx-auto">
      <h1 class="text-2xl font-bold mb-6 text-center">瑜伽通知系统测试</h1>
      
      <div class="space-y-4">
        <button 
          @click="testSuccess"
          class="w-full bg-green-500 text-white py-3 px-4 rounded-lg hover:bg-green-600 transition-colors"
        >
          测试成功通知
        </button>
        
        <button 
          @click="testError"
          class="w-full bg-red-500 text-white py-3 px-4 rounded-lg hover:bg-red-600 transition-colors"
        >
          测试错误通知
        </button>
        
        <button 
          @click="testWarning"
          class="w-full bg-yellow-500 text-white py-3 px-4 rounded-lg hover:bg-yellow-600 transition-colors"
        >
          测试警告通知
        </button>
        
        <button 
          @click="testInfo"
          class="w-full bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 transition-colors"
        >
          测试信息通知
        </button>
        
        <button 
          @click="testYoga"
          class="w-full bg-purple-500 text-white py-3 px-4 rounded-lg hover:bg-purple-600 transition-colors"
        >
          测试瑜伽通知
        </button>
        
        <button 
          @click="testBookingSuccess"
          class="w-full bg-indigo-500 text-white py-3 px-4 rounded-lg hover:bg-indigo-600 transition-colors"
        >
          测试预约成功
        </button>
        
        <button 
          @click="testLoginSuccess"
          class="w-full bg-teal-500 text-white py-3 px-4 rounded-lg hover:bg-teal-600 transition-colors"
        >
          测试登录成功
        </button>
        
        <button 
          @click="testNetworkError"
          class="w-full bg-orange-500 text-white py-3 px-4 rounded-lg hover:bg-orange-600 transition-colors"
        >
          测试网络错误
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { notification } from '../services/notification'

const testSuccess = () => {
  notification.success('操作已成功完成！')
}

const testError = () => {
  notification.error('操作失败，请重试')
}

const testWarning = () => {
  notification.warning('请注意，这是一个警告信息')
}

const testInfo = () => {
  notification.info('这是一条普通的信息提示')
}

const testYoga = () => {
  notification.yoga('感受瑜伽的宁静与力量')
}

const testBookingSuccess = () => {
  notification.bookingSuccess('瑜伽初级班')
}

const testLoginSuccess = () => {
  notification.loginSuccess('瑜伽爱好者')
}

const testNetworkError = () => {
  notification.networkError()
}
</script> 