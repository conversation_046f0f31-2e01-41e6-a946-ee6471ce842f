<template>
  <div class="min-h-screen bg-apple-gray-50">
    <!-- 导航栏 -->
    <div class="bg-white/95 backdrop-blur-apple border-b border-apple-gray-100 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <button @click="$router.go(-1)" class="w-8 h-8 bg-apple-gray-100 rounded-full flex items-center justify-center">
            <font-awesome-icon icon="chevron-left" class="text-apple-gray-600" />
          </button>
          <h1 class="font-bold text-lg text-apple-gray-900">瑜伽课程</h1>
        </div>
        <div class="flex items-center space-x-3">
          <button class="w-8 h-8 bg-apple-gray-100 rounded-full flex items-center justify-center">
            <font-awesome-icon icon="search" class="text-apple-gray-600 text-sm" />
          </button>
          <button class="w-8 h-8 bg-apple-gray-100 rounded-full flex items-center justify-center">
            <font-awesome-icon icon="filter" class="text-apple-gray-600 text-sm" />
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="px-4 py-4">
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
          <font-awesome-icon icon="search" class="text-apple-gray-400" />
        </div>
        <input 
          type="text" 
          v-model="searchQuery"
          @input="searchCourses"
          @keyup.enter="searchCourses"
          class="apple-input pl-10" 
          placeholder="搜索瑜伽课程..."
        />
      </div>
    </div>

    <!-- 分类标签 -->
    <div class="px-4 pb-4">
      <div class="flex space-x-3 overflow-x-auto">
        <button 
          v-for="category in categories" 
          :key="category.id"
          @click="selectedCategory = category.id"
          class="flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200"
          :class="selectedCategory === category.id 
            ? 'bg-apple-blue text-white' 
            : 'bg-white text-apple-gray-600 hover:bg-apple-gray-100'"
        >
          {{ category.name }}
        </button>
      </div>
    </div>

    <!-- 课程列表 -->
    <div class="px-4 pb-20">
      <!-- 瑜伽加载动画 -->
      <YogaLoading 
        :show="loading" 
        type="chakra"
        text="加载课程中..." 
      />
      
      <!-- 课程列表 -->
      <div v-if="!loading" class="space-y-4">
        <div 
          v-for="course in filteredCourses" 
          :key="course.id"
          @click="$router.push(`/course/${course.id}`)"
          class="bg-white rounded-apple-lg overflow-hidden shadow-apple cursor-pointer hover:shadow-apple-card transition-all duration-200 active:scale-98"
        >
          <!-- 课程图片 -->
          <div 
            class="h-48 bg-cover bg-center relative"
            :style="`background-image: url('${course.image}')`"
          >
            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
            <div class="absolute top-3 right-3">
              <button class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                <font-awesome-icon :icon="course.isFavorite ? 'heart' : ['far', 'heart']" 
                  :class="course.isFavorite ? 'text-red-500' : 'text-white'" />
              </button>
            </div>
            <div class="absolute bottom-3 left-3">
              <span class="bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-apple text-xs font-medium">
                {{ course.level }}
              </span>
            </div>
          </div>

          <!-- 课程信息 -->
          <div class="p-4">
            <h3 class="font-bold text-lg text-apple-gray-900 mb-2">{{ course.title }}</h3>
            <p class="text-sm text-apple-gray-600 mb-3">{{ course.description }}</p>
            
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full"></div>
                <span class="text-sm text-apple-gray-700 font-medium">{{ course.instructor }}</span>
              </div>
              <div class="flex items-center space-x-1">
                <font-awesome-icon icon="star" class="text-yellow-400 text-sm" />
                <span class="text-sm text-apple-gray-600">{{ course.rating }}</span>
              </div>
            </div>
            
            <div class="flex items-center space-x-4 text-xs text-apple-gray-500">
              <div class="flex items-center space-x-1">
                <font-awesome-icon icon="clock" />
                <span>{{ course.duration }}</span>
              </div>
              <div class="flex items-center space-x-1">
                <font-awesome-icon icon="users" />
                <span>{{ course.students }} 学员</span>
              </div>
              <div class="flex items-center space-x-1">
                <font-awesome-icon icon="play" />
                <span>{{ course.lessons }} 节课</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { courseAPI } from '../services/api'
import BottomNavigation from '../components/BottomNavigation.vue'
import YogaLoading from '../components/YogaLoading.vue'
import { notification } from '../services/notification'

// 搜索查询
const searchQuery = ref('')
const selectedCategory = ref('all')
const loading = ref(true)

// 分类数据
const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'beginner', name: '初级' },
  { id: 'intermediate', name: '中级' },
  { id: 'advanced', name: '高级' },
  { id: 'relaxation', name: '放松' },
  { id: 'strength', name: '力量' }
])

// 课程数据
const courses = ref<any[]>([])

// 获取课程列表
const fetchCourses = async () => {
  try {
    loading.value = true
    const response = await courseAPI.getCourses({ limit: 20 })
    
    if (response.success) {
      // 处理并转换后端数据结构到前端期望的格式
      const rawCourses = response.data.courses || []
      courses.value = rawCourses.map((course: any) => ({
        id: course.id,
        title: course.title,
        description: course.description,
        level: course.level,
        duration: `${course.duration}分钟`, // 转换为字符串格式
        price: course.price,
        image: course.imageUrl, // 映射 imageUrl 到 image
        instructor: course.instructor?.name || '专业教练', // 从对象中提取姓名
        rating: '4.8', // 后端暂无此字段，使用默认值
        students: Math.floor(Math.random() * 1000) + 100, // 后端暂无此字段，使用随机值
        lessons: Math.floor(Math.random() * 15) + 5, // 后端暂无此字段，使用随机值
        category: course.level === '初级' ? 'beginner' : 
                 course.level === '中级' ? 'intermediate' : 
                 course.level === '高级' ? 'advanced' : 'relaxation', // 根据级别映射分类
        isFavorite: false // 默认未收藏
      }))
      
      if (courses.value.length === 0) {
        notification.info('暂无课程数据')
      }
    } else {
      throw new Error(response.message || '获取课程列表失败')
    }
  } catch (error: any) {
    console.error('获取课程列表失败:', error)
    
    // 根据错误类型显示不同的通知
    if (error.message?.includes('网络') || error.code === 'NETWORK_ERROR') {
      notification.networkError()
    } else {
      notification.error(error.message || '获取课程列表失败')
    }
    
    // 使用模拟数据作为fallback
    courses.value = [
      {
        id: 1,
        title: '瑜伽基础入门',
        description: '适合初学者的瑜伽课程，学习基本瑜伽姿势',
        instructor: '李老师',
        duration: '45分钟',
        rating: '4.8',
        students: 1234,
        lessons: 12,
        level: '初级',
        category: 'beginner',
        isFavorite: false,
        image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },
      {
        id: 2,
        title: '办公室瑜伽',
        description: '缓解工作压力，改善身体姿态的瑜伽练习',
        instructor: '王老师',
        duration: '30分钟',
        rating: '4.7',
        students: 892,
        lessons: 8,
        level: '初级',
        category: 'relaxation',
        isFavorite: true,
        image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },
      {
        id: 3,
        title: '流瑜伽进阶',
        description: '动态连贯的瑜伽序列，提升力量和柔韧性',
        instructor: '张老师',
        duration: '60分钟',
        rating: '4.9',
        students: 567,
        lessons: 15,
        level: '中级',
        category: 'intermediate',
        isFavorite: false,
        image: 'https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },
      {
        id: 4,
        title: '深度放松瑜伽',
        description: '通过瑜伽练习达到深度放松和冥想状态',
        instructor: '陈老师',
        duration: '50分钟',
        rating: '4.6',
        students: 345,
        lessons: 10,
        level: '初级',
        category: 'relaxation',
        isFavorite: false,
        image: 'https://images.unsplash.com/photo-1599901860904-17e6ed7083a0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },
      {
        id: 5,
        title: '高级流瑜伽',
        description: '流畅的瑜伽序列，适合有经验的练习者',
        instructor: '刘老师',
        duration: '60分钟',
        rating: '4.9',
        students: 432,
        lessons: 20,
        level: '高级',
        category: 'advanced',
        isFavorite: false,
        image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      }
    ]
  } finally {
    loading.value = false
  }
}

// 搜索课程
const searchCourses = async () => {
  if (!searchQuery.value.trim()) {
    await fetchCourses()
    return
  }
  
  try {
    loading.value = true
    const response = await courseAPI.getCourses({ 
      search: searchQuery.value,
      limit: 20 
    })
    
    if (response.success) {
      // 同样处理搜索结果的数据转换
      const rawCourses = response.data.courses || []
      courses.value = rawCourses.map((course: any) => ({
        id: course.id,
        title: course.title,
        description: course.description,
        level: course.level,
        duration: `${course.duration}分钟`,
        price: course.price,
        image: course.imageUrl,
        instructor: course.instructor?.name || '专业教练',
        rating: '4.8',
        students: Math.floor(Math.random() * 1000) + 100,
        lessons: Math.floor(Math.random() * 15) + 5,
        category: course.level === '初级' ? 'beginner' : 
                 course.level === '中级' ? 'intermediate' : 
                 course.level === '高级' ? 'advanced' : 'relaxation',
        isFavorite: false
      }))
      
      if (courses.value.length === 0) {
        notification.info(`未找到"${searchQuery.value}"相关的课程`)
      }
    }
  } catch (error: any) {
    console.error('搜索课程失败:', error)
    notification.error('搜索失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 过滤课程
const filteredCourses = computed(() => {
  let filtered = courses.value

  // 按分类过滤
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(course => 
      course.category === selectedCategory.value ||
      course.level === categories.value.find(c => c.id === selectedCategory.value)?.name
    )
  }

  // 本地搜索过滤（如果有搜索词但还没调用API）
  if (searchQuery.value && !loading.value) {
    filtered = filtered.filter(course => 
      course.title?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      course.description?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      course.instructor?.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  return filtered
})

// 页面初始化
onMounted(async () => {
  await fetchCourses()
})
</script> 