<template>
  <div class="min-h-screen bg-apple-gray-50">
    <!-- 瑜伽加载动画 -->
    <YogaLoading
      :show="loading && schedules.length === 0"
      type="breathing"
      text="加载每日课程..."
    />

    <!-- 头部 -->
    <div class="bg-white px-4 py-4 shadow-sm">
      <div class="flex items-center justify-between">
        <h1 class="text-xl font-semibold text-apple-gray-900">每日课程</h1>
        <button @click="refreshSchedules" class="p-2 text-apple-blue">
          <font-awesome-icon icon="refresh" :class="{ 'animate-spin': loading }" />
        </button>
      </div>
    </div>

    <!-- 日期选择器 -->
    <div class="date-picker-container mx-4">
      <div class="flex items-center space-x-3">
        <button
          @click="previousDay"
          class="date-picker-btn"
        >
          <font-awesome-icon icon="chevron-left" class="text-lg" />
        </button>

        <div class="flex-1 text-center">
          <input
            type="date"
            v-model="selectedDate"
            @change="fetchDailySchedules"
            class="apple-date-picker w-full"
          />
        </div>

        <button
          @click="nextDay"
          class="date-picker-btn"
        >
          <font-awesome-icon icon="chevron-right" class="text-lg" />
        </button>
      </div>

      <!-- 显示选中日期的友好格式 -->
      <div class="text-center mt-3 text-sm text-apple-gray-600">
        {{ formatSelectedDate() }}
      </div>
    </div>

    <!-- 课程时间表 -->
    <div class="px-4 pb-20">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center py-8">
        <font-awesome-icon icon="spinner" class="animate-spin text-apple-blue text-2xl" />
        <span class="ml-2 text-apple-gray-600">加载中...</span>
      </div>

      <!-- 错误信息 -->
      <div v-else-if="errorMessage" class="text-center py-8">
        <font-awesome-icon icon="exclamation-circle" class="text-red-500 text-2xl mb-2" />
        <p class="text-red-600 mb-4">{{ errorMessage }}</p>
        <button
          @click="fetchDailySchedules"
          class="apple-btn-primary px-4 py-2 text-sm"
        >
          重新加载
        </button>
      </div>

      <!-- 课程列表 -->
      <div v-else-if="schedules.length > 0" class="space-y-4 mt-4">
        <div
          v-for="schedule in schedules"
          :key="schedule.id"
          class="glass-morphism rounded-apple-xl p-4 shadow-apple-card"
        >
          <!-- 课程信息 -->
          <div class="flex items-start justify-between mb-3">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-apple-gray-900 mb-1">
                {{ schedule.course?.title || '瑜伽课程' }}
              </h3>
              <p class="text-sm text-apple-gray-600 mb-2">
                {{ schedule.course?.description || '专业瑜伽训练' }}
              </p>
              <div class="flex items-center space-x-4 text-xs text-apple-gray-500">
                <span class="flex items-center">
                  <font-awesome-icon icon="user" class="mr-1" />
                  {{ schedule.instructor?.name || '专业教练' }}
                </span>
                <span class="flex items-center">
                  <font-awesome-icon icon="clock" class="mr-1" />
                  {{ schedule.course?.duration || '60分钟' }}
                </span>
                <span class="flex items-center">
                  <font-awesome-icon icon="star" class="mr-1" />
                  {{ schedule.course?.rating || '4.8' }}
                </span>
              </div>
            </div>
          </div>

          <!-- 时间和状态 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-apple-blue">
                {{ formatTime(schedule.startTime) }} - {{ formatTime(schedule.endTime) }}
              </span>
              <span
                :class="getStatusClass(schedule.status)"
                class="px-2 py-1 rounded-full text-xs font-medium"
              >
                {{ getStatusText(schedule.status) }}
              </span>
            </div>

            <!-- 预约按钮 -->
            <button
              v-if="schedule.status === 'available'"
              @click="bookCourse(schedule)"
              :disabled="bookingLoading === schedule.id"
              class="apple-btn-primary text-sm px-4 py-2 disabled:opacity-50"
            >
              <font-awesome-icon
                v-if="bookingLoading === schedule.id"
                icon="spinner"
                class="animate-spin mr-1"
              />
              {{ bookingLoading === schedule.id ? '预约中...' : '立即预约' }}
            </button>

            <button
              v-else-if="schedule.status === 'booked'"
              @click="cancelBooking(schedule)"
              :disabled="cancelLoading === schedule.id"
              class="bg-red-500 text-white px-4 py-2 rounded-apple text-sm hover:bg-red-600 transition-colors disabled:opacity-50"
            >
              <font-awesome-icon
                v-if="cancelLoading === schedule.id"
                icon="spinner"
                class="animate-spin mr-1"
              />
              {{ cancelLoading === schedule.id ? '取消中...' : '取消预约' }}
            </button>

            <span
              v-else
              class="text-sm text-apple-gray-500"
            >
              {{ schedule.status === 'full' ? '已满员' : '不可预约' }}
            </span>
          </div>

          <!-- 剩余名额 -->
          <div v-if="schedule.availableSpots !== undefined" class="mt-2 text-xs text-apple-gray-500">
            剩余名额：{{ schedule.availableSpots }}/{{ schedule.totalSpots }}
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <font-awesome-icon icon="calendar" class="text-apple-gray-300 text-4xl mb-4" />
        <h3 class="text-lg font-medium text-apple-gray-600 mb-2">今日暂无课程</h3>
        <p class="text-sm text-apple-gray-500">请选择其他日期查看课程安排</p>
      </div>
    </div>

    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { scheduleAPI, bookingAPI } from '../services/api'
import BottomNavigation from '../components/BottomNavigation.vue'
import YogaLoading from '../components/YogaLoading.vue'
import { notification } from '../services/notification'

// 响应式数据
const loading = ref(true)
const errorMessage = ref('')
const selectedDate = ref(new Date().toISOString().split('T')[0])
const schedules = ref<any[]>([])
const bookingLoading = ref<string | null>(null)
const cancelLoading = ref<string | null>(null)

// 格式化时间
const formatTime = (timeString: string) => {
  const time = new Date(timeString)
  return time.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })
}

// 格式化选定的日期
const formatSelectedDate = () => {
  return formatDate(selectedDate.value);
}

// 格式化日期为更友好的显示
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const today = new Date()
  const todayStr = today.toISOString().split('T')[0]

  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  const yesterdayStr = yesterday.toISOString().split('T')[0]

  if (dateString === todayStr) {
    return '今天 · ' + date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  } else if (dateString === yesterdayStr) {
    return '昨天 · ' + date.toLocaleDateString('zh-CN', {
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  }
}

// 获取状态样式
const getStatusClass = (status: string) => {
  switch (status) {
    case 'available':
      return 'bg-green-100 text-green-800'
    case 'booked':
      return 'bg-blue-100 text-blue-800'
    case 'full':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'available':
      return '可预约'
    case 'booked':
      return '已预约'
    case 'full':
      return '已满员'
    default:
      return '不可用'
  }
}

// 获取每日课程安排
const fetchDailySchedules = async () => {
  try {
    loading.value = true
    errorMessage.value = ''

    console.log(`获取日期 ${selectedDate.value} 的课程安排`)

    // 不再发送实际请求，而是直接使用模拟数据
    schedules.value = [
      {
        id: 1,
        course: {
          title: '晨间瑜伽唤醒',
          description: '温和的晨间瑜伽序列，帮助你开启活力的一天',
          duration: '60分钟',
          rating: '4.8'
        },
        instructor: {
          name: '李老师'
        },
        startTime: `${selectedDate.value}T09:00:00`,
        endTime: `${selectedDate.value}T10:00:00`,
        status: 'available',
        availableSpots: 8,
        totalSpots: 12,
        location: '瑜伽馆A',
        price: 88
      },
      {
        id: 2,
        course: {
          title: '办公室减压瑜伽',
          description: '专为上班族设计，缓解肩颈疲劳和工作压力',
          duration: '45分钟',
          rating: '4.9'
        },
        instructor: {
          name: '王老师'
        },
        startTime: `${selectedDate.value}T18:30:00`,
        endTime: `${selectedDate.value}T19:15:00`,
        status: 'available',
        availableSpots: 5,
        totalSpots: 10,
        location: '瑜伽馆B',
        price: 128
      },
      {
        id: 3,
        course: {
          title: '力量瑜伽挑战',
          description: '通过动态瑜伽序列增强核心力量和肌肉耐力',
          duration: '75分钟',
          rating: '4.6'
        },
        instructor: {
          name: '陈老师'
        },
        startTime: `${selectedDate.value}T20:00:00`,
        endTime: `${selectedDate.value}T21:15:00`,
        status: 'full',
        availableSpots: 0,
        totalSpots: 8,
        location: '瑜伽馆C',
        price: 98
      }
    ]

    // 检查用户的预约状态
    await checkUserBookingStatus()

  } catch (error: any) {
    console.error('获取每日课程失败:', error)
    errorMessage.value = error.message || '获取课程安排失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 检查用户预约状态
const checkUserBookingStatus = async () => {
  try {
    // 获取用户的预约列表
    const response = await fetch('http://localhost:3000/api/bookings', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('access_token') || ''}`
      },
      mode: 'cors',
      credentials: 'same-origin'
    })

    if (response.ok) {
      const data = await response.json()
      if (data.success && data.data.bookings) {
        console.log('获取到的预约记录:', data.data.bookings)
        console.log('当前课程列表:', schedules.value.map(s => ({ id: s.id, title: s.course.title })))

        // 遍历用户的预约记录，更新课程状态
        data.data.bookings.forEach((booking: any) => {
          console.log(`检查预约: scheduleId=${booking.scheduleId}, status=${booking.status}`)
          const schedule = schedules.value.find(s => s.id === parseInt(booking.scheduleId))
          console.log(`找到匹配的课程:`, schedule ? schedule.course.title : '未找到')

          if (schedule && booking.status === 'confirmed') {
            console.log(`更新课程状态: ${schedule.course.title} -> 已预约`)
            schedule.status = 'booked'
            schedule.isBooked = true
            schedule.userBookingId = booking.id
            // 减少可用名额
            if (schedule.availableSpots !== undefined && schedule.availableSpots > 0) {
              schedule.availableSpots--
            }
          }
        })
        console.log('预约状态检查完成，已更新课程状态')
      }
    }
  } catch (error) {
    console.error('检查预约状态失败:', error)
    // 不影响主要功能，静默处理错误
  }
}

// 预约课程
const bookCourse = async (schedule: any) => {
  try {
    bookingLoading.value = schedule.id
    console.log(`预约课程: ID=${schedule.id}`)

    // 调用API服务预约课程
    try {
      const response = await bookingAPI.createBooking({
        scheduleId: String(schedule.id),
        notes: '通过每日课程表预约'
      })

      console.log('预约API响应:', response)

      // 更新本地状态
      schedule.status = 'booked'
      schedule.isBooked = true
      schedule.userBookingId = response.data?.id || Date.now() // 优先使用服务器返回的ID
      if (schedule.availableSpots !== undefined && schedule.availableSpots > 0) {
        schedule.availableSpots--
      }

      notification.success(response.message || '预约成功！')
    } catch (apiError: any) {
      console.error('API调用失败:', apiError)

      if (apiError.message && apiError.message.includes('已预约')) {
        // 如果是重复预约错误，仍然更新UI状态
        schedule.status = 'booked'
        schedule.isBooked = true
        notification.info('您已成功预约此课程')
      } else {
        // 其他API错误
        throw apiError
      }
    }
  } catch (error: any) {
    console.error('预约失败:', error)
    notification.error(error.message || '预约失败，请稍后重试')
  } finally {
    bookingLoading.value = null
  }
}

// 取消预约
const cancelBooking = async (schedule: any) => {
  try {
    cancelLoading.value = schedule.id

    const bookingId = schedule.userBookingId || schedule.id

    console.log(`开始取消预约: ID=${bookingId}, 类型: ${typeof bookingId}`)
    console.log(`预约详情:`, JSON.stringify(schedule, null, 2))

    try {
      const response = await bookingAPI.cancelBooking(String(bookingId), '用户取消预约')
      console.log('取消预约响应:', response)

      // 更新本地状态
      schedule.status = 'available'
      schedule.isBooked = false
      schedule.userBookingId = null
      if (schedule.availableSpots !== undefined) {
        schedule.availableSpots++
      }

      notification.success(response.message || '取消预约成功！')
    } catch (apiError: any) {
      console.error('API调用失败:', apiError)

      // 即使API返回错误，也更新UI状态
      // 这样可以确保UI反映课程可预约的状态
      if (apiError.message && (apiError.message.includes('已取消') || apiError.message.includes('不存在'))) {
        // 如果是"已取消"或"不存在"的错误，仍然更新UI状态
        schedule.status = 'available'
        schedule.isBooked = false
        schedule.userBookingId = null
        if (schedule.availableSpots !== undefined) {
          schedule.availableSpots++
        }
        notification.info(apiError.message || '此预约已被取消')
      } else {
        // 其他API错误
        throw apiError
      }
    }
  } catch (error: any) {
    console.error('取消预约失败:', error)
    notification.error(error.message || '取消预约失败，请稍后重试')
  } finally {
    cancelLoading.value = null
  }
}

// 上一天
const previousDay = () => {
  const date = new Date(selectedDate.value)
  date.setDate(date.getDate() - 1)
  selectedDate.value = date.toISOString().split('T')[0]
  fetchDailySchedules()
}

// 下一天
const nextDay = () => {
  const date = new Date(selectedDate.value)
  date.setDate(date.getDate() + 1)
  selectedDate.value = date.toISOString().split('T')[0]
  fetchDailySchedules()
}

// 刷新课程安排
const refreshSchedules = () => {
  fetchDailySchedules()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDailySchedules()
})
</script>
