<template>
  <div class="min-h-screen bg-apple-gray-50">
    <!-- 课程图片和导航 -->
    <div class="relative">
      <div 
        class="h-80 bg-cover bg-center"
        :style="`background-image: url('${course.image}')`"
      >
        <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"></div>
        
        <!-- 导航栏 -->
        <div class="absolute top-0 left-0 right-0 flex items-center justify-between p-4">
          <button @click="$router.go(-1)" class="w-10 h-10 bg-black/20 backdrop-blur-sm rounded-full flex items-center justify-center">
            <font-awesome-icon icon="chevron-left" class="text-white" />
          </button>
          <div class="flex items-center space-x-3">
            <button class="w-10 h-10 bg-black/20 backdrop-blur-sm rounded-full flex items-center justify-center">
              <font-awesome-icon icon="share" class="text-white text-sm" />
            </button>
            <button @click="toggleFavorite" class="w-10 h-10 bg-black/20 backdrop-blur-sm rounded-full flex items-center justify-center">
              <font-awesome-icon :icon="course.isFavorite ? 'heart' : ['far', 'heart']" 
                :class="course.isFavorite ? 'text-red-500' : 'text-white'" />
            </button>
          </div>
        </div>

        <!-- 播放按钮 -->
        <div class="absolute inset-0 flex items-center justify-center">
          <button 
            @click="startCourse"
            class="w-16 h-16 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-all duration-200 active:scale-95"
          >
            <font-awesome-icon icon="play" class="text-apple-blue text-xl ml-1" />
          </button>
        </div>

        <!-- 课程级别标签 -->
        <div class="absolute bottom-4 left-4">
          <span class="bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-apple text-sm font-medium">
            {{ course.level }}
          </span>
        </div>
      </div>
    </div>

    <!-- 课程信息 -->
    <div class="px-4 py-6">
      <!-- 标题和评分 -->
      <div class="mb-4">
        <h1 class="text-2xl font-bold text-apple-gray-900 mb-2">{{ course.title }}</h1>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-1">
            <font-awesome-icon icon="star" class="text-yellow-400" />
            <span class="font-semibold text-apple-gray-900">{{ course.rating }}</span>
            <span class="text-apple-gray-500">({{ course.reviews.length }} 评价)</span>
          </div>
          <div class="flex items-center space-x-1">
            <font-awesome-icon icon="users" class="text-apple-gray-400" />
            <span class="text-apple-gray-500">{{ course.students }} 学员</span>
          </div>
        </div>
      </div>

      <!-- 教练信息 -->
      <div class="apple-card mb-6">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold">{{ course.instructor.name.charAt(0) }}</span>
          </div>
          <div class="flex-1">
            <h3 class="font-semibold text-apple-gray-900">{{ course.instructor.name }}</h3>
            <p class="text-sm text-apple-gray-600">{{ course.instructor.bio }}</p>
          </div>
          <button class="apple-btn-secondary text-sm px-4 py-2">
            关注
          </button>
        </div>
      </div>

      <!-- 课程统计 -->
      <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="text-center">
          <div class="w-12 h-12 bg-purple-100 rounded-apple mx-auto mb-2 flex items-center justify-center">
            <font-awesome-icon icon="clock" class="text-purple-600" />
          </div>
          <div class="text-sm font-semibold text-apple-gray-900">{{ course.duration }}</div>
          <div class="text-xs text-apple-gray-500">总时长</div>
        </div>
        <div class="text-center">
          <div class="w-12 h-12 bg-green-100 rounded-apple mx-auto mb-2 flex items-center justify-center">
            <font-awesome-icon icon="play" class="text-green-600" />
          </div>
          <div class="text-sm font-semibold text-apple-gray-900">{{ course.lessons }}</div>
          <div class="text-xs text-apple-gray-500">节课程</div>
        </div>
        <div class="text-center">
          <div class="w-12 h-12 bg-orange-100 rounded-apple mx-auto mb-2 flex items-center justify-center">
            <font-awesome-icon icon="download" class="text-orange-600" />
          </div>
          <div class="text-sm font-semibold text-apple-gray-900">离线</div>
          <div class="text-xs text-apple-gray-500">可下载</div>
        </div>
      </div>

      <!-- 课程描述 -->
      <div class="mb-6">
        <h3 class="font-bold text-lg text-apple-gray-900 mb-3">课程介绍</h3>
        <p class="text-apple-gray-600 leading-relaxed">{{ course.description }}</p>
      </div>

      <!-- 课程目标 -->
      <div class="mb-6">
        <h3 class="font-bold text-lg text-apple-gray-900 mb-3">你将学到</h3>
        <div class="space-y-2">
          <div v-for="benefit in course.benefits" :key="benefit" class="flex items-start space-x-3">
            <font-awesome-icon icon="check-circle" class="text-apple-green mt-1 flex-shrink-0" />
            <span class="text-apple-gray-600">{{ benefit }}</span>
          </div>
        </div>
      </div>

      <!-- 课程安排 -->
      <div class="mb-20">
        <h3 class="font-bold text-lg text-apple-gray-900 mb-3">课程安排</h3>
        <div class="space-y-2">
          <div 
            v-for="(scheduleItem, index) in course.schedule" 
            :key="scheduleItem.id || index"
            class="bg-white rounded-apple p-4 flex items-center space-x-4"
          >
            <div class="w-8 h-8 bg-apple-blue/10 rounded-full flex items-center justify-center flex-shrink-0">
              <span class="text-apple-blue font-semibold text-sm">{{ index + 1 }}</span>
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-apple-gray-900">{{ scheduleItem.dayOfWeek }}</h4>
              <p class="text-sm text-apple-gray-500">{{ scheduleItem.startTime }} - {{ scheduleItem.endTime }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-apple-gray-600">剩余 {{ scheduleItem.availableSpots }} 个名额</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-apple border-t border-apple-gray-100 p-4">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <div class="text-sm text-apple-gray-500">免费课程</div>
          <div class="text-lg font-bold text-apple-gray-900">¥{{ course.price }}</div>
        </div>
        <button 
          @click="startCourse"
          class="apple-btn-primary px-8 py-3 text-lg font-semibold"
        >
          立即开始
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { courseAPI } from '../services/api'
import { notification } from '../services/notification'

const route = useRoute()
const router = useRouter()

// 课程数据
const course = ref({
  id: '1',
  title: '加载中...',
  description: '',
  longDescription: '',
  instructor: {
    name: '加载中...',
    bio: '',
    specialties: []
  },
  duration: '0分钟',
  rating: '4.8',
  reviews: [],
  students: 0,
  lessons: 12,
  level: '初级',
  isFavorite: false,
  image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
  benefits: [],
  schedule: [],
  price: 0
})

const loading = ref(true)

// 获取课程详情
const fetchCourseDetail = async (courseId: string) => {
  try {
    loading.value = true
    const response = await courseAPI.getCourseById(courseId)
    
    if (response.success && response.data.course) {
      const courseData = response.data.course
      
      // 转换后端数据结构到前端期望的格式
      course.value = {
        id: courseData.id,
        title: courseData.title,
        description: courseData.description,
        longDescription: courseData.longDescription || courseData.description,
        instructor: {
          name: courseData.instructor?.name || '专业教练',
          bio: courseData.instructor?.bio || '经验丰富的瑜伽教练',
          specialties: courseData.instructor?.specialties || []
        },
        duration: `${courseData.duration}分钟`,
        rating: '4.8', // 可以基于 reviews 计算平均分
        reviews: courseData.reviews || [],
        students: Math.floor(Math.random() * 1000) + 100, // 后端暂无此字段
        lessons: Math.floor(courseData.duration / 5), // 基于时长估算课节数
        level: courseData.level,
        isFavorite: false,
        image: courseData.imageUrl,
        benefits: courseData.benefits || [
          '增强身体柔韧性',
          '改善姿势',
          '减轻压力',
          '增强核心力量',
          '提高身体平衡'
        ],
        schedule: courseData.schedule || [],
        price: courseData.price
      }
      
      // 如果有评价，计算真实评分
      if (course.value.reviews.length > 0) {
        const avgRating = course.value.reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / course.value.reviews.length
        course.value.rating = avgRating.toFixed(1)
      }
    } else {
      throw new Error(response.message || '获取课程详情失败')
    }
  } catch (error: any) {
    console.error('获取课程详情失败:', error)
    notification.error(error.message || '获取课程详情失败')
    
    // 使用默认数据，但保持courseId
    course.value.id = courseId
    course.value.title = '课程详情加载失败'
    course.value.description = '暂时无法加载课程详情，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 切换收藏状态
const toggleFavorite = () => {
  course.value.isFavorite = !course.value.isFavorite
  // 这里可以调用API保存收藏状态
  notification.success(course.value.isFavorite ? '已添加到收藏' : '已取消收藏')
}

// 开始课程
const startCourse = () => {
  // 这里可以跳转到视频播放页面或开始课程
  notification.success('开始课程练习！')
}

onMounted(() => {
  // 根据路由参数获取课程ID并获取课程详情
  const courseId = route.params.id as string
  console.log('课程ID:', courseId)
  
  if (courseId) {
    fetchCourseDetail(courseId)
  } else {
    notification.error('课程ID不存在')
    router.push('/courses')
  }
})
</script> 