<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { authAPI, courseAPI, instructorAPI } from '../services/api'
import BottomNavigation from '../components/BottomNavigation.vue'
import YogaLoading from '../components/YogaLoading.vue'
import { notification } from '../services/notification'

const router = useRouter()

// 用户信息
const user = ref<any>(null)
const loading = ref(true)
const errorMessage = ref('')

// 问候语
const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
})

// 推荐课程数据
const recommendedCourses = ref<any[]>([])
const popularCourses = ref<any[]>([])
const activeInstructors = ref<any[]>([])

// 统计数据
const stats = ref({
  totalCourses: 0,
  totalInstructors: 0,
  userProgress: 85
})

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const response = await authAPI.getProfile()
    if (response.success) {
      user.value = response.data
    }
  } catch (error: any) {
    console.error('获取用户信息失败:', error)
    errorMessage.value = '获取用户信息失败'
  }
}

// 获取推荐课程
const fetchRecommendedCourses = async () => {
  try {
    // 由于后端可能没有推荐课程接口，我们可以获取所有课程然后取前3个
    const response = await courseAPI.getCourses({ limit: 3 })
    if (response.success) {
      // 处理并转换后端数据结构到前端期望的格式
      const rawCourses = response.data.courses || []
      recommendedCourses.value = rawCourses.map((course: any) => ({
        id: course.id,
        title: course.title,
        instructor: course.instructor?.name || '专业教练',
        duration: `${course.duration}分钟`,
        rating: '4.8',
        image: course.imageUrl
      }))
    }
  } catch (error: any) {
    console.error('获取推荐课程失败:', error)
    // 使用模拟数据作为fallback
    recommendedCourses.value = [
      {
        id: 1,
        title: '晨间瑜伽唤醒',
        instructor: '李老师',
        duration: '20分钟',
        rating: '4.8',
        image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
      },
      {
        id: 2,
        title: '办公室减压瑜伽',
        instructor: '王老师', 
        duration: '15分钟',
        rating: '4.9',
        image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
      },
      {
        id: 3,
        title: '睡前放松瑜伽',
        instructor: '张老师',
        duration: '25分钟',
        rating: '4.7',
        image: 'https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80'
      }
    ]
  }
}

// 获取活跃教练员
const fetchActiveInstructors = async () => {
  try {
    const response = await instructorAPI.getActiveInstructors()
    if (response.success) {
      activeInstructors.value = response.data.instructors || []
      stats.value.totalInstructors = activeInstructors.value.length
    }
  } catch (error: any) {
    console.error('获取教练员信息失败:', error)
  }
}

// 开始练习 - 跳转到每日课表
const startPractice = () => {
  router.push('/schedule')
}

// 页面初始化
onMounted(async () => {
  // 检查登录状态
  const isAuthenticated = localStorage.getItem('isAuthenticated')
  if (!isAuthenticated) {
    router.push('/login')
    return
  }

  loading.value = true
  try {
    // 并行获取数据
    await Promise.all([
      fetchUserInfo(),
      fetchRecommendedCourses(),
      fetchActiveInstructors()
    ])
  } catch (error) {
    console.error('初始化页面数据失败:', error)
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <div class="min-h-screen bg-apple-gray-50">
    <!-- 瑜伽加载动画 -->
    <YogaLoading 
      :show="loading" 
      type="yoga"
      text="加载中..." 
    />
    
    <!-- 主导航栏 -->
    <div v-if="!loading" class="bg-white/95 backdrop-blur-apple border-b border-apple-gray-100 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
            <font-awesome-icon icon="heart" class="text-white text-sm" />
          </div>
          <div>
            <h1 class="font-bold text-lg text-apple-gray-900">瑜伽空间</h1>
            <p class="text-xs text-apple-gray-500">{{ greeting }}，{{ user?.username || '用户' }}</p>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <button class="w-8 h-8 bg-apple-gray-100 rounded-full flex items-center justify-center">
            <font-awesome-icon icon="bell" class="text-apple-gray-600 text-sm" />
          </button>
          <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full"></div>
        </div>
      </div>
    </div>

    <!-- Hero区域 -->
    <div class="px-4 py-6">
      <div class="relative bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800 rounded-apple-xl overflow-hidden">
        <!-- 背景图片 -->
        <div 
          class="absolute inset-0 bg-cover bg-center opacity-30"
          style="background-image: url('https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80')"
        ></div>
        
        <!-- 内容 -->
        <div class="relative z-10 p-6 text-white">
          <h2 class="text-2xl font-bold mb-2">今日练习</h2>
          <p class="text-white/90 mb-4">开始你的瑜伽之旅，找到内心的平静</p>
          
          <div class="flex items-center space-x-4 mb-6">
            <div class="flex items-center space-x-2">
              <font-awesome-icon icon="clock" class="text-white/80" />
              <span class="text-sm">30分钟</span>
            </div>
            <div class="flex items-center space-x-2">
              <font-awesome-icon icon="star" class="text-yellow-400" />
              <span class="text-sm">初级</span>
            </div>
          </div>
          
          <button 
            @click="startPractice"
            class="bg-white text-purple-700 px-6 py-3 rounded-apple font-medium flex items-center space-x-2 hover:bg-white/90 transition-all duration-200 active:scale-95"
          >
            <font-awesome-icon icon="play" class="text-sm" />
            <span>开始练习</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 快捷功能卡片 -->
    <div class="px-4 pb-6">
      <h3 class="text-lg font-bold text-apple-gray-900 mb-4">快捷功能</h3>
      
      <div class="grid grid-cols-2 gap-4">
        <!-- 课程浏览 -->
        <div 
          @click="$router.push('/courses')"
          class="apple-card cursor-pointer hover:shadow-apple-card transition-all duration-200 active:scale-95"
        >
          <div class="flex items-center space-x-3 mb-3">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="play" class="text-white text-sm" />
            </div>
            <div>
              <h4 class="font-semibold text-apple-gray-900">课程浏览</h4>
              <p class="text-xs text-apple-gray-500">50+ 专业课程</p>
            </div>
          </div>
          <p class="text-sm text-apple-gray-600">探索丰富的瑜伽课程库</p>
        </div>

        <!-- 预约课程 -->
        <div 
          @click="$router.push('/bookings')"
          class="apple-card cursor-pointer hover:shadow-apple-card transition-all duration-200 active:scale-95"
        >
          <div class="flex items-center space-x-3 mb-3">
            <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="calendar" class="text-white text-sm" />
            </div>
            <div>
              <h4 class="font-semibold text-apple-gray-900">预约课程</h4>
              <p class="text-xs text-apple-gray-500">即时预约</p>
            </div>
          </div>
          <p class="text-sm text-apple-gray-600">预约线下瑜伽课程</p>
        </div>

        <!-- 个人进度 -->
        <div 
          @click="$router.push('/profile')"
          class="apple-card cursor-pointer hover:shadow-apple-card transition-all duration-200 active:scale-95"
        >
          <div class="flex items-center space-x-3 mb-3">
            <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="chart-line" class="text-white text-sm" />
            </div>
            <div>
              <h4 class="font-semibold text-apple-gray-900">学习进度</h4>
              <p class="text-xs text-apple-gray-500">85% 完成</p>
            </div>
          </div>
          <p class="text-sm text-apple-gray-600">查看你的练习记录</p>
        </div>

        <!-- 社区分享 -->
        <div class="apple-card cursor-pointer hover:shadow-apple-card transition-all duration-200 active:scale-95">
          <div class="flex items-center space-x-3 mb-3">
            <div class="w-10 h-10 bg-gradient-to-br from-pink-500 to-pink-600 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="users" class="text-white text-sm" />
            </div>
            <div>
              <h4 class="font-semibold text-apple-gray-900">瑜伽社区</h4>
              <p class="text-xs text-apple-gray-500">2.3k 成员</p>
            </div>
          </div>
          <p class="text-sm text-apple-gray-600">与瑜伽爱好者交流</p>
        </div>
      </div>
    </div>

    <!-- 今日推荐 -->
    <div class="px-4 pb-20">
      <h3 class="text-lg font-bold text-apple-gray-900 mb-4">今日推荐</h3>
      
      <div class="space-y-3">
        <div 
          v-for="course in recommendedCourses" 
          :key="course.id"
          @click="$router.push(`/course/${course.id}`)"
          class="bg-white rounded-apple-lg p-4 shadow-apple cursor-pointer hover:shadow-apple-card transition-all duration-200 active:scale-98"
        >
          <div class="flex items-center space-x-4">
            <div 
              class="w-16 h-16 bg-cover bg-center rounded-apple"
              :style="`background-image: url('${course.image}')`"
            ></div>
            <div class="flex-1">
              <h4 class="font-semibold text-apple-gray-900 mb-1">{{ course.title }}</h4>
              <p class="text-sm text-apple-gray-600 mb-2">{{ course.instructor }}</p>
              <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-1">
                  <font-awesome-icon icon="clock" class="text-apple-gray-400 text-xs" />
                  <span class="text-xs text-apple-gray-500">{{ course.duration }}</span>
                </div>
                <div class="flex items-center space-x-1">
                  <font-awesome-icon icon="star" class="text-yellow-400 text-xs" />
                  <span class="text-xs text-apple-gray-500">{{ course.rating }}</span>
                </div>
              </div>
            </div>
            <font-awesome-icon icon="chevron-right" class="text-apple-gray-300" />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>
