<template>
  <div class="min-h-screen bg-apple-gray-50">
    <!-- 导航栏 -->
    <div class="bg-white/95 backdrop-blur-apple border-b border-apple-gray-100 px-4 py-3">
      <div class="flex items-center justify-between">
        <h1 class="font-bold text-lg text-apple-gray-900">个人中心</h1>
        <button class="w-8 h-8 bg-apple-gray-100 rounded-full flex items-center justify-center">
          <font-awesome-icon icon="edit" class="text-apple-gray-600 text-sm" />
        </button>
      </div>
    </div>

    <!-- 用户信息卡片 -->
    <div class="px-4 py-6">
      <div class="apple-card">
        <div class="flex items-center space-x-4 mb-4">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-apple-xl flex items-center justify-center">
            <span class="text-white font-bold text-xl">{{ userInfo.name.charAt(0) }}</span>
          </div>
          <div class="flex-1">
            <h2 class="text-xl font-bold text-apple-gray-900">{{ userInfo.name }}</h2>
            <p class="text-apple-gray-600">{{ userInfo.email }}</p>
            <div class="flex items-center space-x-2 mt-1">
              <span class="bg-apple-blue/10 text-apple-blue px-2 py-1 rounded-apple text-xs font-medium">
                {{ userInfo.level }}
              </span>
              <span class="text-xs text-apple-gray-500">加入 {{ userInfo.joinDays }} 天</span>
            </div>
          </div>
        </div>
        
        <!-- 统计数据 -->
        <div class="grid grid-cols-3 gap-4 pt-4 border-t border-apple-gray-100">
          <div class="text-center">
            <div class="text-lg font-bold text-apple-gray-900">{{ userInfo.totalCourses }}</div>
            <div class="text-xs text-apple-gray-500">完成课程</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-apple-gray-900">{{ userInfo.totalHours }}</div>
            <div class="text-xs text-apple-gray-500">练习时长</div>
          </div>
          <div class="text-center">
            <div class="text-lg font-bold text-apple-gray-900">{{ userInfo.streak }}</div>
            <div class="text-xs text-apple-gray-500">连续天数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 本周成就 -->
    <div class="px-4 pb-6">
      <h3 class="font-bold text-lg text-apple-gray-900 mb-4">本周成就</h3>
      <div class="apple-card">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-apple flex items-center justify-center">
            <font-awesome-icon icon="star" class="text-white text-xl" />
          </div>
          <div class="flex-1">
            <h4 class="font-semibold text-apple-gray-900">练习达人</h4>
            <p class="text-sm text-apple-gray-600">本周完成 5 次瑜伽练习</p>
          </div>
          <div class="text-right">
            <div class="text-sm font-semibold text-apple-blue">+50</div>
            <div class="text-xs text-apple-gray-500">积分</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="px-4 pb-20">
      <div class="space-y-3">
        <!-- 我的课程 -->
        <div class="apple-card">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-blue-100 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="play" class="text-blue-600" />
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-apple-gray-900">我的课程</h4>
              <p class="text-sm text-apple-gray-500">查看已购买的课程</p>
            </div>
            <font-awesome-icon icon="chevron-right" class="text-apple-gray-300" />
          </div>
        </div>

        <!-- 学习记录 -->
        <div class="apple-card">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-green-100 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="chart-line" class="text-green-600" />
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-apple-gray-900">学习记录</h4>
              <p class="text-sm text-apple-gray-500">查看练习历史和进度</p>
            </div>
            <font-awesome-icon icon="chevron-right" class="text-apple-gray-300" />
          </div>
        </div>

        <!-- 收藏夹 -->
        <div class="apple-card">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-red-100 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="heart" class="text-red-600" />
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-apple-gray-900">我的收藏</h4>
              <p class="text-sm text-apple-gray-500">收藏的课程和教练</p>
            </div>
            <font-awesome-icon icon="chevron-right" class="text-apple-gray-300" />
          </div>
        </div>

        <!-- 设置 -->
        <div class="apple-card">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-gray-100 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="edit" class="text-gray-600" />
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-apple-gray-900">设置</h4>
              <p class="text-sm text-apple-gray-500">账户设置和偏好</p>
            </div>
            <font-awesome-icon icon="chevron-right" class="text-apple-gray-300" />
          </div>
        </div>

        <!-- 帮助与反馈 -->
        <div class="apple-card">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-orange-100 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="info" class="text-orange-600" />
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-apple-gray-900">帮助与反馈</h4>
              <p class="text-sm text-apple-gray-500">获取帮助或提供建议</p>
            </div>
            <font-awesome-icon icon="chevron-right" class="text-apple-gray-300" />
          </div>
        </div>

        <!-- 退出登录 -->
        <div @click="logout" class="apple-card cursor-pointer hover:bg-apple-red/5 transition-colors">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-red-100 rounded-apple flex items-center justify-center">
              <font-awesome-icon icon="sign-out-alt" class="text-red-600" />
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-apple-red">退出登录</h4>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import BottomNavigation from '../components/BottomNavigation.vue'
import { notification } from '../services/notification'

const router = useRouter()

// 用户信息
const userInfo = ref({
  name: localStorage.getItem('username') || '瑜伽爱好者',
  email: '<EMAIL>',
  level: '瑜伽新手',
  joinDays: 30,
  totalCourses: 12,
  totalHours: 15.5,
  streak: 7
})

// 退出登录
const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    localStorage.removeItem('isAuthenticated')
    localStorage.removeItem('username')
    localStorage.removeItem('access_token')
    notification.info('已退出登录')
    
    setTimeout(() => {
      router.push('/login')
    }, 1000)
  }
}
</script> 