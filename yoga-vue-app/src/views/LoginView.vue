<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800 flex items-center justify-center p-4">
    <!-- 瑜伽加载动画 -->
    <YogaLoading 
      :show="loading" 
      type="lotus"
      text="正在登录..." 
    />
    
    <!-- 浮动装饰元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full floating-animation"></div>
      <div class="absolute top-40 right-20 w-16 h-16 bg-white/10 rounded-full floating-animation" style="animation-delay: 1s;"></div>
      <div class="absolute bottom-32 left-20 w-12 h-12 bg-white/10 rounded-full floating-animation" style="animation-delay: 2s;"></div>
    </div>
    
    <div class="w-full max-w-md relative z-10">
      <!-- Logo区域 -->
      <div class="text-center mb-8">
        <div class="w-20 h-20 bg-white/20 rounded-apple-xl mx-auto mb-4 flex items-center justify-center backdrop-blur-sm">
          <font-awesome-icon icon="heart" class="text-white text-3xl" />
        </div>
        <h1 class="text-3xl font-bold text-white mb-2">瑜伽空间</h1>
        <p class="text-white/80">找到内心的平静</p>
      </div>
      
      <!-- 登录表单 -->
      <div class="glass-morphism rounded-apple-xl p-8 shadow-apple-float">
        <form @submit.prevent="handleLogin">
          <div class="space-y-6">
            <!-- 用户名输入 -->
            <div>
              <label class="block text-sm font-medium text-apple-gray-700 mb-2">用户名或邮箱</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <font-awesome-icon icon="user" class="text-apple-gray-400" />
                </div>
                <input 
                  type="text" 
                  v-model="loginForm.username"
                  class="apple-input pl-10" 
                  placeholder="请输入用户名或邮箱"
                  required
                />
              </div>
            </div>
            
            <!-- 密码输入 -->
            <div>
              <label class="block text-sm font-medium text-apple-gray-700 mb-2">密码</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <font-awesome-icon icon="lock" class="text-apple-gray-400" />
                </div>
                <input 
                  :type="showPassword ? 'text' : 'password'" 
                  v-model="loginForm.password"
                  class="apple-input pl-10 pr-10" 
                  placeholder="请输入密码"
                  required
                />
                <button 
                  type="button"
                  @click="showPassword = !showPassword"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <font-awesome-icon 
                    :icon="showPassword ? 'eye-slash' : 'eye'" 
                    class="text-apple-gray-400 hover:text-apple-gray-600 transition-colors" 
                  />
                </button>
              </div>
            </div>
            
            <!-- 记住密码 -->
            <div class="flex items-center justify-between">
              <label class="flex items-center">
                <input 
                  type="checkbox" 
                  v-model="loginForm.remember"
                  class="w-4 h-4 text-apple-blue bg-white border-apple-gray-300 rounded focus:ring-apple-blue focus:ring-2"
                />
                <span class="ml-2 text-sm text-apple-gray-600">记住密码</span>
              </label>
              <button 
                type="button"
                class="text-sm text-apple-blue hover:text-apple-blue/80 transition-colors"
                @click="showForgotPasswordModal = true"
              >
                忘记密码？
              </button>
            </div>
            
            <!-- 登录按钮 -->
            <button 
              type="submit"
              :disabled="loading"
              class="apple-btn-primary w-full text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ loading ? '登录中...' : '登录' }}
            </button>
          </div>
        </form>
        
        <!-- 登录链接 -->
        <div class="mt-8 text-center">
          <span class="text-sm text-apple-gray-600">还没有账户？</span>
          <router-link 
            to="/register"
            class="text-sm text-apple-blue font-medium hover:text-apple-blue/80 transition-colors ml-1"
          >
            立即注册
          </router-link>
        </div>
      </div>
    </div>
    
    <!-- 忘记密码模态框 -->
    <div v-if="showForgotPasswordModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="glass-morphism rounded-apple-xl p-8 shadow-apple-float max-w-md w-full mx-4">
        <h3 class="text-xl font-semibold mb-4">重置密码</h3>
        <p class="text-apple-gray-600 mb-6">请输入您的邮箱地址，我们将向您发送重置密码的链接</p>
        
        <form @submit.prevent="handleResetPassword">
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-apple-gray-700 mb-2">邮箱地址</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <font-awesome-icon icon="envelope" class="text-apple-gray-400" />
                </div>
                <input 
                  type="email" 
                  v-model="resetForm.email"
                  class="apple-input pl-10" 
                  placeholder="请输入邮箱地址"
                  required
                />
              </div>
            </div>
            
            <div class="flex gap-3">
              <button 
                type="button"
                @click="showForgotPasswordModal = false"
                class="flex-1 py-2 px-4 border border-apple-gray-300 rounded-apple-lg text-apple-gray-700 hover:bg-apple-gray-50 transition-colors"
              >
                取消
              </button>
              <button 
                type="submit"
                :disabled="resetLoading"
                class="flex-1 py-2 px-4 bg-apple-blue text-white rounded-apple-lg hover:bg-apple-blue/90 transition-colors disabled:opacity-50"
              >
                {{ resetLoading ? '提交中...' : '提交' }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { authAPI, handleApiError } from '../services/api'
import YogaLoading from '@/components/YogaLoading.vue'
import { notification } from '@/services/notification'

const router = useRouter()

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
  remember: false
})

const showPassword = ref(false)
const loading = ref(false)

// 忘记密码相关
const showForgotPasswordModal = ref(false)
const resetForm = ref({ email: '' })
const resetLoading = ref(false)

// 登录处理
const handleLogin = async () => {
  if (!loginForm.value.username || !loginForm.value.password) {
    notification.warning('请输入用户名和密码')
    return
  }

  loading.value = true
  
  try {
    const response = await authAPI.login(
      loginForm.value.username, 
      loginForm.value.password
    )
    
    if (response.success) {
      // 登录成功
      notification.loginSuccess(response.data?.user?.username)
      
      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        router.push('/')
      }, 1000)
    } else {
      notification.error(response.message || '登录失败')
    }
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 根据错误类型显示不同的通知
    if (error.message?.includes('网络') || error.code === 'NETWORK_ERROR') {
      notification.networkError()
    } else {
      notification.error(error.message || '登录失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 处理重置密码
const handleResetPassword = async () => {
  if (!resetForm.value.email) {
    notification.warning('请输入邮箱地址')
    return
  }
  
  resetLoading.value = true
  
  try {
    const response = await fetch('http://localhost:3000/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email: resetForm.value.email })
    })
    
    const data = await response.json()
    
    if (data.code === 200) {
      notification.success(data.msg || '重置密码邮件已发送')
      showForgotPasswordModal.value = false
      resetForm.value.email = ''
    } else {
      notification.error(data.msg || '发送失败，请稍后重试')
    }
  } catch (error: any) {
    console.error('重置密码失败:', error)
    notification.error('网络错误，请检查您的网络连接')
  } finally {
    resetLoading.value = false
  }
}
</script>
