// API服务层 - WebApp版本（用户端）
import { ref } from 'vue'

// API配置
const API_BASE_URL = 'http://localhost:3000'
const BOOKING_API_BASE_URL = 'http://localhost:3001' // 专门的预约API服务器
const API_PREFIX = '/api'

// 认证状态
export const isAuthenticated = ref(localStorage.getItem('isAuthenticated') === 'true')
export const userToken = ref(localStorage.getItem('access_token') || '')
export const currentUser = ref(JSON.parse(localStorage.getItem('currentUser') || 'null'))

// 设置认证令牌
export const setAuthToken = (token: string) => {
  localStorage.setItem('access_token', token)
  localStorage.setItem('isAuthenticated', 'true')
  userToken.value = token
  isAuthenticated.value = true
}

// 清除认证令牌
export const clearAuthToken = () => {
  localStorage.removeItem('access_token')
  localStorage.removeItem('isAuthenticated')
  userToken.value = ''
  isAuthenticated.value = false
}

// 获取当前用户
export const getCurrentUser = () => currentUser.value

// API响应类型
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  timestamp: string
}

interface AuthResponse {
  access_token: string
  refresh_token: string
  user: {
    id: string
    username: string
    email: string
    role: string
    displayName?: string
  }
}

interface UserProfile {
  id: string
  username: string
  email: string
  displayName?: string
  role: string
  isEmailVerified: boolean
  createdAt: string
  updatedAt: string
}

// 发送POST请求的通用方法
async function post(url: string, data: any, headers: Record<string, string> = {}): Promise<ApiResponse> {
  try {
    const requestHeaders = {
      'Content-Type': 'application/json',
      ...headers
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: requestHeaders,
      body: JSON.stringify(data),
      mode: 'cors',
      credentials: 'omit'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    return result
  } catch (error: any) {
    console.error('POST request failed:', error)
    throw new Error(error.message || '请求失败')
  }
}

// 简化版认证API（WebApp用户端）
export const authAPI = {
  // 用户登录 - 使用真实后端API
  async login(emailOrUsername: string, password: string): Promise<ApiResponse<AuthResponse>> {
    const cleanEndpoint = '/login'
    const url = `${API_BASE_URL}${cleanEndpoint}`
    
    const config: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: emailOrUsername,
        password
      })
    }
    
    console.log(`API请求: POST ${url}`)
    const response = await fetch(url, config)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const rawData = await response.json()
    
    // 适配不同的响应格式
    const data: ApiResponse<AuthResponse> = {
      success: rawData.code === 200 || rawData.success,
      message: rawData.msg || rawData.message || (rawData.code === 200 ? '登录成功' : '登录失败'),
      data: {
        access_token: rawData.token || rawData.data?.access_token,
        refresh_token: rawData.refreshToken || rawData.data?.refresh_token || 'mock_refresh_' + Date.now(),
        user: {
          id: String(rawData.user?.userId || rawData.user?.id || '1'),
          username: rawData.user?.userName || rawData.user?.username || emailOrUsername,
          email: rawData.user?.email || `${emailOrUsername}@example.com`,
          role: rawData.user?.role || 'student',
          displayName: rawData.user?.nickName || rawData.user?.displayName || emailOrUsername
        }
      },
      timestamp: new Date().toISOString()
    }
    
    if (data.success) {
      // 保存认证信息
      setAuthToken(data.data!.access_token)
      localStorage.setItem('refresh_token', data.data!.refresh_token)
      localStorage.setItem('currentUser', JSON.stringify(data.data!.user))
      currentUser.value = data.data!.user
    }
    
    return data
  },

  // 用户注册
  async register(userData: {
    username: string
    email: string
    password: string
    fullName?: string
  }): Promise<ApiResponse> {
    const cleanEndpoint = '/register'
    const url = `${API_BASE_URL}${cleanEndpoint}`
    
    const config: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData)
    }
    
    const response = await fetch(url, config)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const rawData = await response.json()
    
    // 适配不同的响应格式
    const data: ApiResponse = {
      success: rawData.code === 200 || rawData.success,
      message: rawData.msg || rawData.message || (rawData.code === 200 ? '注册成功' : '注册失败'),
      data: rawData.data,
      timestamp: new Date().toISOString()
    }
    
    return data
  },

  // 获取用户信息
  async getProfile(): Promise<ApiResponse<UserProfile>> {
    if (currentUser.value) {
      return {
        success: true,
        message: '获取成功',
        data: currentUser.value as UserProfile,
        timestamp: new Date().toISOString()
      }
    }
    
    throw new Error('用户未登录')
  },

  // 退出登录
  async logout(): Promise<ApiResponse> {
    // 清理本地认证信息
    clearAuthToken()
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('currentUser')
    currentUser.value = null
    
    return { 
      success: true, 
      message: '退出登录成功', 
      timestamp: new Date().toISOString() 
    }
  }
}

// 预约API
export const bookingAPI = {
  // 创建预约
  async createBooking(bookingData: {
    scheduleId: string
    notes?: string
    specialRequests?: string
  }): Promise<ApiResponse> {
    const url = `${BOOKING_API_BASE_URL}/api/bookings`
    
    const config: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken.value}`
      },
      body: JSON.stringify({
        scheduleId: bookingData.scheduleId,
        notes: bookingData.notes
      })
    }
    
    console.log(`API请求: POST ${url}`)
    const response = await fetch(url, config)
    
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data
  },

  // 获取用户预约列表
  async getUserBookings(params?: { 
    page?: number
    limit?: number
    status?: string 
  }): Promise<ApiResponse> {
    const queryParams = new URLSearchParams()
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.status) queryParams.append('status', params.status)
    
    const url = `${API_BASE_URL}/api/bookings${queryParams.toString() ? '?' + queryParams.toString() : ''}`
    
    const config: RequestInit = {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${userToken.value}`
      }
    }
    
    console.log(`API请求: GET ${url}`)
    const response = await fetch(url, config)
    
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data
  },

  // 取消预约
  async cancelBooking(bookingId: string, reason?: string): Promise<ApiResponse> {
    console.log(`调用取消预约API: bookingId=${bookingId}, 类型=${typeof bookingId}`);
    
    // 确保bookingId是数字类型
    const numericId = parseInt(bookingId);
    const url = `${API_BASE_URL}/api/bookings/${numericId}/cancel`
    
    console.log(`取消预约请求URL: ${url}`);
    
    const config: RequestInit = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken.value}`
      },
      body: JSON.stringify({ reason: reason || '用户取消预约' })
    }
    
    try {
      const response = await fetch(url, config)
      console.log(`取消预约API状态码: ${response.status}`);
      
      // 无论状态码如何，都尝试获取响应数据
      let rawData;
      try {
        rawData = await response.json()
        console.log(`取消预约API响应:`, rawData);
      } catch (e) {
        console.error('解析响应JSON失败:', e);
        throw new Error(`无法解析服务器响应: ${e}`);
      }
      
      // 如果API返回成功响应
      if (response.ok) {
        return {
          success: true,
          message: rawData.message || '取消预约成功',
          data: rawData.data,
          timestamp: new Date().toISOString()
        };
      }
      
      // 特殊处理：即使返回错误状态码，但消息中包含"已取消"，我们也视为成功
      if (rawData && rawData.message && (
          rawData.message.includes('已取消') || 
          rawData.message.includes('不存在')
      )) {
        return {
          success: true,
          message: rawData.message,
          data: null,
          timestamp: new Date().toISOString()
        };
      }
      
      // 其他错误情况
      return {
        success: false,
        message: rawData.message || `HTTP ${response.status}: ${response.statusText}`,
        data: null,
        timestamp: new Date().toISOString()
      };
    } catch (error: any) {
      console.error('取消预约失败:', error);
      return {
        success: false,
        message: error.message || '取消预约失败，请稍后重试',
        data: null,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// 课程时间表API
export const scheduleAPI = {
  // 获取每日课程时间表
  async getDailySchedules(date?: string): Promise<ApiResponse> {
    const today = date || new Date().toISOString().split('T')[0]
    console.log(`获取日期 ${today} 的课程表`)
    
    // 提供内置的每日课程表数据
    const mockSchedules = [
      {
        id: "s1",
        courseId: "1",
        courseName: "初级哈他瑜伽",
        instructorId: "1",
        instructorName: "李老师",
        date: today,
        startTime: "10:00",
        endTime: "11:00",
        duration: 60,
        room: "阳光瑜伽室",
        capacity: 15,
        enrolledCount: 8,
        status: "active",
        level: "初级",
        imageUrl: "https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
      },
      {
        id: "s2",
        courseId: "2",
        courseName: "流瑜伽进阶",
        instructorId: "2",
        instructorName: "王老师",
        date: today,
        startTime: "12:00",
        endTime: "13:15",
        duration: 75,
        room: "森林瑜伽室",
        capacity: 12,
        enrolledCount: 10,
        status: "active",
        level: "中级",
        imageUrl: "https://images.unsplash.com/photo-1599447421416-3414500d18a5?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
      },
      {
        id: "s3",
        courseId: "3",
        courseName: "阴瑜伽放松",
        instructorId: "3",
        instructorName: "张老师",
        date: today,
        startTime: "17:30",
        endTime: "19:00",
        duration: 90,
        room: "静心瑜伽室",
        capacity: 10,
        enrolledCount: 7,
        status: "active",
        level: "不限",
        imageUrl: "https://images.unsplash.com/photo-1593811167562-9cef47bfc4d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
      },
      {
        id: "s4",
        courseId: "1",
        courseName: "初级哈他瑜伽",
        instructorId: "1",
        instructorName: "李老师",
        date: today,
        startTime: "19:30",
        endTime: "20:30",
        duration: 60,
        room: "阳光瑜伽室",
        capacity: 15,
        enrolledCount: 12,
        status: "active",
        level: "初级",
        imageUrl: "https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
      }
    ]
    
    return {
      success: true,
      message: "获取课程表成功",
      data: {
        schedules: mockSchedules,
        date: today
      },
      timestamp: new Date().toISOString()
    }
  },

  // 获取可用时间段
  async getAvailableSchedules(params?: { 
    startDate?: string
    endDate?: string
    courseId?: string
    instructorId?: string
  }): Promise<ApiResponse> {
    const queryParams = new URLSearchParams()
    if (params?.startDate) queryParams.append('startDate', params.startDate)
    if (params?.endDate) queryParams.append('endDate', params.endDate)
    if (params?.courseId) queryParams.append('courseId', params.courseId)
    if (params?.instructorId) queryParams.append('instructorId', params.instructorId)
    
    const cleanEndpoint = `/schedules/available${queryParams.toString() ? '?' + queryParams.toString() : ''}`
    const url = `${API_BASE_URL}${API_PREFIX}${cleanEndpoint}`
    
    const config: RequestInit = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken.value}`
      }
    }
    
    const response = await fetch(url, config)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data
  },

  // 获取时间段详情
  async getScheduleById(scheduleId: string): Promise<ApiResponse> {
    const cleanEndpoint = `/schedules/${scheduleId}`
    const url = `${API_BASE_URL}${API_PREFIX}${cleanEndpoint}`
    
    const config: RequestInit = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken.value}`
      }
    }
    
    const response = await fetch(url, config)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    return data
  }
}

// 简化版课程API
export const courseAPI = {
  // 获取课程列表
  async getCourses(params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse> {
    // 提供内置的课程数据，无需调用API
    const mockCourses = [
      {
        id: "1",
        title: "初级哈他瑜伽",
        description: "适合初学者的基础瑜伽课程，包含基础体式和呼吸技巧",
        level: "初级",
        duration: 60,
        price: 99,
        imageUrl: "https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
        instructor: {
          id: "1",
          name: "李老师",
          avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
        }
      },
      {
        id: "2",
        title: "流瑜伽进阶",
        description: "通过连贯流畅的体式转换提高力量与灵活性",
        level: "中级",
        duration: 75,
        price: 129,
        imageUrl: "https://images.unsplash.com/photo-1599447421416-3414500d18a5?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
        instructor: {
          id: "2",
          name: "王老师",
          avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
        }
      },
      {
        id: "3",
        title: "阴瑜伽放松",
        description: "通过长时间保持体式来深度放松身心，改善柔韧性",
        level: "不限",
        duration: 90,
        price: 149,
        imageUrl: "https://images.unsplash.com/photo-1593811167562-9cef47bfc4d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
        instructor: {
          id: "3",
          name: "张老师",
          avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b1e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80"
        }
      }
    ]
    
    console.log(`模拟课程数据: 返回${mockCourses.length}个课程`)
    
    return {
      success: true,
      message: "获取课程成功",
      data: {
        courses: mockCourses,
        total: mockCourses.length,
        page: params?.page || 1,
        limit: params?.limit || 10,
        totalPages: 1
      },
      timestamp: new Date().toISOString()
    }
  },

  // 获取课程详情
  async getCourseById(courseId: string): Promise<ApiResponse> {
    // 使用内置的课程详情数据
    interface CourseDetail {
      id: string
      title: string
      description: string
      longDescription: string
      level: string
      duration: number
      price: number
      imageUrl: string
      instructor: {
        id: string
        name: string
        bio: string
        avatar: string
        specialties: string[]
      }
      schedule: Array<{
        id: string
        dayOfWeek: string
        startTime: string
        endTime: string
        availableSpots: number
      }>
      benefits: string[]
      reviews: Array<{
        id: string
        user: string
        rating: number
        comment: string
        date: string
      }>
    }
    
    const mockCourseDetails: Record<string, CourseDetail> = {
      "1": {
        id: "1",
        title: "初级哈他瑜伽",
        description: "适合初学者的基础瑜伽课程，包含基础体式和呼吸技巧",
        longDescription: "这是一门专为初学者设计的哈他瑜伽课程。课程内容包括基础的瑜伽体式、正确的呼吸技巧以及简单的冥想练习。通过系统学习，帮助学员打好瑜伽基础，提高身体灵活性，舒缓压力，改善睡眠质量。适合任何年龄段、任何体能水平的人群。",
        level: "初级",
        duration: 60,
        price: 99,
        imageUrl: "https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        instructor: {
          id: "1",
          name: "李老师",
          bio: "李老师拥有10年瑜伽教学经验，专注于初学者培训。她的教学风格温和且细致，特别关注学生的正确体式和安全。",
          avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
          specialties: ["哈他瑜伽", "初学者指导", "修复瑜伽"]
        },
        schedule: [
          {
            id: "s1",
            dayOfWeek: "周一",
            startTime: "10:00",
            endTime: "11:00",
            availableSpots: 15
          },
          {
            id: "s2",
            dayOfWeek: "周三",
            startTime: "19:00",
            endTime: "20:00",
            availableSpots: 12
          },
          {
            id: "s3",
            dayOfWeek: "周六",
            startTime: "09:00",
            endTime: "10:00",
            availableSpots: 8
          }
        ],
        benefits: [
          "增强身体柔韧性",
          "改善姿势",
          "减轻压力",
          "增强核心力量",
          "提高身体平衡"
        ],
        reviews: [
          {
            id: "r1",
            user: "张三",
            rating: 5,
            comment: "非常适合初学者的课程，老师讲解很细致！",
            date: "2023-11-15"
          },
          {
            id: "r2",
            user: "李四",
            rating: 4,
            comment: "课程内容很充实，每次上完课都感觉很放松。",
            date: "2023-12-03"
          }
        ]
      },
      "2": {
        id: "2",
        title: "流瑜伽进阶",
        description: "通过连贯流畅的体式转换提高力量与灵活性",
        longDescription: "这门流瑜伽课程专为有一定瑜伽基础的学员设计。课程强调动作与呼吸的协调，通过一系列流畅连贯的体式转换，提高学员的力量、耐力和灵活性。每节课都会有不同的练习序列，让学员全面提升瑜伽技能。",
        level: "中级",
        duration: 75,
        price: 129,
        imageUrl: "https://images.unsplash.com/photo-1599447421416-3414500d18a5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        instructor: {
          id: "2",
          name: "王老师",
          bio: "王老师专注于流瑜伽和力量训练，擅长设计具有挑战性且流畅的瑜伽序列。他的课程充满活力，能激发学员突破自我极限。",
          avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
          specialties: ["流瑜伽", "力量训练", "倒立练习"]
        },
        schedule: [
          {
            id: "s4",
            dayOfWeek: "周二",
            startTime: "18:30",
            endTime: "19:45",
            availableSpots: 10
          },
          {
            id: "s5",
            dayOfWeek: "周四",
            startTime: "18:30",
            endTime: "19:45",
            availableSpots: 5
          },
          {
            id: "s6",
            dayOfWeek: "周日",
            startTime: "10:30",
            endTime: "11:45",
            availableSpots: 7
          }
        ],
        benefits: [
          "提高心肺耐力",
          "增强全身肌肉力量",
          "提升身体协调性",
          "改善体态平衡",
          "增强心理专注力"
        ],
        reviews: [
          {
            id: "r3",
            user: "王五",
            rating: 5,
            comment: "非常有挑战性的课程，每次都有新的收获！",
            date: "2023-10-25"
          },
          {
            id: "r4",
            user: "赵六",
            rating: 4,
            comment: "王老师的引导很专业，课程编排很合理。",
            date: "2023-11-18"
          }
        ]
      },
      "3": {
        id: "3",
        title: "阴瑜伽放松",
        description: "通过长时间保持体式来深度放松身心，改善柔韧性",
        longDescription: "阴瑜伽是一种缓慢而深入的练习方式，通过长时间保持体式（通常3-5分钟），深度拉伸连接肌肉的筋膜和深层结缔组织。这门课程帮助学员释放身体紧张，改善关节活动度，平衡身心能量。特别适合压力大、身体紧张或需要恢复训练的人群。",
        level: "不限",
        duration: 90,
        price: 149,
        imageUrl: "https://images.unsplash.com/photo-1593811167562-9cef47bfc4d7?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        instructor: {
          id: "3",
          name: "张老师",
          bio: "张老师专注于阴瑜伽和恢复性练习，拥有深厚的解剖学知识背景。她的教学注重身心平衡，帮助学员找到内心的宁静与和谐。",
          avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b1e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
          specialties: ["阴瑜伽", "冥想指导", "身心疗愈"]
        },
        schedule: [
          {
            id: "s7",
            dayOfWeek: "周二",
            startTime: "10:00",
            endTime: "11:30",
            availableSpots: 12
          },
          {
            id: "s8",
            dayOfWeek: "周五",
            startTime: "19:30",
            endTime: "21:00",
            availableSpots: 8
          },
          {
            id: "s9",
            dayOfWeek: "周日",
            startTime: "16:00",
            endTime: "17:30",
            availableSpots: 6
          }
        ],
        benefits: [
          "深度放松身心",
          "改善关节灵活性",
          "舒缓压力和焦虑",
          "平衡身体能量",
          "促进深层组织恢复"
        ],
        reviews: [
          {
            id: "r5",
            user: "钱七",
            rating: 5,
            comment: "张老师的阴瑜伽课让我找到了内心的平静，很治愈！",
            date: "2023-09-30"
          },
          {
            id: "r6",
            user: "孙八",
            rating: 5,
            comment: "课后感觉整个人都放松了，对改善我的睡眠有很大帮助。",
            date: "2023-11-05"
          }
        ]
      }
    }
    
    if (mockCourseDetails[courseId]) {
      console.log(`获取课程详情成功: ${courseId}`, mockCourseDetails[courseId]);
      return {
        success: true,
        message: '获取课程详情成功',
        data: mockCourseDetails[courseId],
        timestamp: new Date().toISOString()
      }
    } else {
      console.log(`课程详情不存在: ${courseId}`);
      return {
        success: false,
        message: '课程不存在',
        error: '未找到对应的课程信息',
        timestamp: new Date().toISOString()
      }
    }
  }
}

// 导出API实例
export const authAPI = new AuthAPI()
export const courseAPI = new CourseAPI()
export const bookingAPI = new BookingAPI()
export const scheduleAPI = new ScheduleAPI()