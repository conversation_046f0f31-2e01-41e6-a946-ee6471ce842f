// API服务层 - WebApp版本（用户端）
import { ref } from 'vue'

// API基础配置
const API_BASE_URL = 'http://localhost:3000'
const API_PREFIX = '/api/v1'

// 认证状态
export const isAuthenticated = ref(localStorage.getItem('isAuthenticated') === 'true')
export const userToken = ref(localStorage.getItem('access_token') || '')
export const currentUser = ref(JSON.parse(localStorage.getItem('currentUser') || 'null'))

// 设置认证令牌
export const setAuthToken = (token: string) => {
  localStorage.setItem('access_token', token)
  localStorage.setItem('isAuthenticated', 'true')
  userToken.value = token
  isAuthenticated.value = true
}

// 清除认证令牌
export const clearAuthToken = () => {
  localStorage.removeItem('access_token')
  localStorage.removeItem('isAuthenticated')
  userToken.value = ''
  isAuthenticated.value = false
}

// 获取当前用户
export const getCurrentUser = () => currentUser.value

// API响应类型
interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  timestamp: string
}

interface AuthResponse {
  access_token: string
  refresh_token: string
  user: {
    id: string
    username: string
    email: string
    role: string
    displayName?: string
  }
}

interface UserProfile {
  id: string
  username: string
  email: string
  displayName?: string
  role: string
  isEmailVerified: boolean
  createdAt: string
  updatedAt: string
}

// 简化版认证API（WebApp用户端）
export const authAPI = {
  // 用户登录 - 简化版本，不依赖复杂的后端权限系统
  async login(emailOrUsername: string, password: string): Promise<ApiResponse<AuthResponse>> {
    try {
      // 尝试真实API调用
      const cleanEndpoint = '/auth/login'
      const url = `${API_BASE_URL}${API_PREFIX}${cleanEndpoint}`
      
      const config: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: emailOrUsername.includes('@') ? emailOrUsername : undefined,
          username: !emailOrUsername.includes('@') ? emailOrUsername : undefined,
          password
        })
      }
      
      console.log(`API请求: POST ${url}`)
      const response = await fetch(url, config)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      if (data.success && data.data) {
        // 保存认证信息
        setAuthToken(data.data.access_token)
        localStorage.setItem('refresh_token', data.data.refresh_token)
        localStorage.setItem('currentUser', JSON.stringify(data.data.user))
        currentUser.value = data.data.user
      }
      
      return data
    } catch (error: any) {
      // 如果后端不可用，提供模拟登录
      console.warn('后端登录失败，使用模拟登录:', error)
      return this.mockLogin(emailOrUsername, password)
    }
  },

  // 模拟登录（用于开发和演示）
  async mockLogin(emailOrUsername: string, password: string): Promise<ApiResponse<AuthResponse>> {
    // 简单验证（在实际应用中不要这样做）
    if (password.length < 6) {
      return {
        success: false,
        message: '密码至少6位',
        timestamp: new Date().toISOString()
      }
    }

    const mockUser = {
      id: '1',
      username: emailOrUsername.includes('@') ? emailOrUsername.split('@')[0] : emailOrUsername,
      email: emailOrUsername.includes('@') ? emailOrUsername : `${emailOrUsername}@example.com`,
      role: 'student',
      displayName: emailOrUsername.includes('@') ? emailOrUsername.split('@')[0] : emailOrUsername
    }

    const mockResponse: ApiResponse<AuthResponse> = {
      success: true,
      message: '登录成功',
      data: {
        access_token: 'mock_token_' + Date.now(),
        refresh_token: 'mock_refresh_' + Date.now(),
        user: mockUser
      },
      timestamp: new Date().toISOString()
    }

    // 保存模拟认证信息
    setAuthToken(mockResponse.data!.access_token)
    localStorage.setItem('refresh_token', mockResponse.data!.refresh_token)
    localStorage.setItem('currentUser', JSON.stringify(mockUser))
    currentUser.value = mockUser

    return mockResponse
  },

  // 用户注册
  async register(userData: {
    username: string
    email: string
    password: string
    fullName?: string
  }): Promise<ApiResponse> {
    try {
      const cleanEndpoint = '/auth/register'
      const url = `${API_BASE_URL}${API_PREFIX}${cleanEndpoint}`
      
      const config: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData)
      }
      
      const response = await fetch(url, config)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      return data
    } catch (error: any) {
      // 模拟注册成功
      console.warn('后端注册失败，使用模拟注册:', error)
      return {
        success: true,
        message: '注册成功（模拟）',
        timestamp: new Date().toISOString()
      }
    }
  },

  // 获取用户信息
  async getProfile(): Promise<ApiResponse<UserProfile>> {
    if (currentUser.value) {
      return {
        success: true,
        message: '获取成功',
        data: currentUser.value as UserProfile,
        timestamp: new Date().toISOString()
      }
    }
    
    throw new Error('用户未登录')
  },

  // 退出登录
  async logout(): Promise<ApiResponse> {
    // 清理本地认证信息
    clearAuthToken()
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('currentUser')
    currentUser.value = null
    
    return { 
      success: true, 
      message: '退出登录成功', 
      timestamp: new Date().toISOString() 
    }
  }
}

// 简化版课程API
export const courseAPI = {
  // 获取课程列表
  async getCourses(params?: { page?: number; limit?: number; search?: string }): Promise<ApiResponse> {
    // 返回模拟数据，因为这是webapp版本
    const mockCourses = [
      {
        id: 1,
        title: '晨间瑜伽唤醒',
        description: '温和的晨间瑜伽序列，帮助你开启活力的一天',
        instructor: '李老师',
        duration: '20分钟',
        rating: '4.8',
        students: 1250,
        lessons: 12,
        level: '初级',
        category: 'beginner',
        isFavorite: true,
        image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },
      {
        id: 2,
        title: '办公室减压瑜伽',
        description: '专为上班族设计，缓解肩颈疲劳和工作压力',
        instructor: '王老师',
        duration: '15分钟',
        rating: '4.9',
        students: 2150,
        lessons: 8,
        level: '初级',
        category: 'relaxation',
        isFavorite: false,
        image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },
      {
        id: 3,
        title: '睡前放松瑜伽',
        description: '舒缓的瑜伽练习，帮助你获得更好的睡眠质量',
        instructor: '张老师',
        duration: '25分钟',
        rating: '4.7',
        students: 980,
        lessons: 10,
        level: '初级',
        category: 'relaxation',
        isFavorite: false,
        image: 'https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },
      {
        id: 4,
        title: '力量瑜伽挑战',
        description: '通过动态瑜伽序列增强核心力量和肌肉耐力',
        instructor: '陈老师',
        duration: '45分钟',
        rating: '4.6',
        students: 756,
        lessons: 15,
        level: '中级',
        category: 'strength',
        isFavorite: true,
        image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },
      {
        id: 5,
        title: '高级流瑜伽',
        description: '流畅的瑜伽序列，适合有经验的练习者',
        instructor: '刘老师',
        duration: '60分钟',
        rating: '4.9',
        students: 432,
        lessons: 20,
        level: '高级',
        category: 'advanced',
        isFavorite: false,
        image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      }
    ]

    // 应用搜索过滤
    let filteredCourses = mockCourses
    if (params?.search) {
      filteredCourses = mockCourses.filter(course =>
        course.title.toLowerCase().includes(params.search!.toLowerCase()) ||
        course.description.toLowerCase().includes(params.search!.toLowerCase()) ||
        course.instructor.toLowerCase().includes(params.search!.toLowerCase())
      )
    }

    // 应用分页
    const limit = params?.limit || 10
    const page = params?.page || 1
    const start = (page - 1) * limit
    const paginatedCourses = filteredCourses.slice(start, start + limit)

    return {
      success: true,
      message: '获取成功',
      data: {
        courses: paginatedCourses,
        total: filteredCourses.length,
        page,
        limit
      },
      timestamp: new Date().toISOString()
    }
  },

  // 获取课程详情
  async getCourseById(courseId: string): Promise<ApiResponse> {
    // 模拟课程详情
    const mockCourse = {
      id: parseInt(courseId),
      title: '晨间瑜伽唤醒',
      description: '温和的晨间瑜伽序列，帮助你开启活力的一天。这个课程专为初学者设计，通过简单的体式和呼吸练习，帮助你唤醒身体，准备迎接新的一天。',
      instructor: '李老师',
      duration: '20分钟',
      rating: '4.8',
      students: 1250,
      lessons: 12,
      level: '初级',
      category: 'beginner',
      price: 99,
      image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      curriculum: [
        { title: '热身与呼吸', duration: '3分钟' },
        { title: '脊柱唤醒序列', duration: '5分钟' },
        { title: '核心激活', duration: '4分钟' },
        { title: '平衡体式', duration: '5分钟' },
        { title: '放松冥想', duration: '3分钟' }
      ]
    }

    return {
      success: true,
      message: '获取成功',
      data: { course: mockCourse },
      timestamp: new Date().toISOString()
    }
  }
}

// 简化版教练员API
export const instructorAPI = {
  // 获取活跃教练员列表
  async getActiveInstructors(): Promise<ApiResponse> {
    const mockInstructors = [
      {
        id: 1,
        name: '李老师',
        specialties: ['哈他瑜伽', '阴瑜伽'],
        experience: 5,
        rating: 4.8,
        students: 1250,
        avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'
      },
      {
        id: 2,
        name: '王老师', 
        specialties: ['流瑜伽', '减压瑜伽'],
        experience: 8,
        rating: 4.9,
        students: 2150,
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'
      },
      {
        id: 3,
        name: '张老师',
        specialties: ['修复瑜伽', '冥想'],
        experience: 6,
        rating: 4.7,
        students: 980,
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b1e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80'
      }
    ]

    return {
      success: true,
      message: '获取成功',
      data: { instructors: mockInstructors },
      timestamp: new Date().toISOString()
    }
  }
}

// 错误处理函数
export const handleApiError = (error: any) => {
  console.error('API错误:', error)
  
  if (error.message?.includes('401') || error.message?.includes('未认证')) {
    // 认证失败，清理本地状态
    authAPI.logout()
    return
  }
  
  console.warn('API错误:', error.message || '网络请求失败，请稍后重试')
} 