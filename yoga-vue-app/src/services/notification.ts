import { createApp, h } from 'vue'
import YogaNotification from '../components/YogaNotification.vue'

interface NotificationOptions {
  type?: 'success' | 'error' | 'warning' | 'info' | 'yoga'
  title?: string
  message: string
  duration?: number
  closable?: boolean
  floating?: boolean
  action?: {
    text: string
    handler: () => void
  }
}

class NotificationService {
  private notifications: any[] = []

  private createNotification(options: NotificationOptions) {
    const container = document.createElement('div')
    document.body.appendChild(container)

    // 保存this引用
    const self = this

    const app = createApp({
      render() {
        return h(YogaNotification, {
          ...options,
          onClose: () => {
            self.removeNotification(app, container)
          }
        })
      }
    })

    app.mount(container)
    this.notifications.push(app)

    return app
  }

  private removeNotification(app: any, container: Element) {
    const index = this.notifications.indexOf(app)
    if (index > -1) {
      this.notifications.splice(index, 1)
    }
    
    app.unmount()
    if (container.parentNode) {
      container.parentNode.removeChild(container)
    }
  }

  // 成功通知
  success(message: string, options?: Partial<NotificationOptions>) {
    return this.createNotification({
      type: 'success',
      message,
      ...options
    })
  }

  // 错误通知
  error(message: string, options?: Partial<NotificationOptions>) {
    return this.createNotification({
      type: 'error',
      message,
      duration: 5000, // 错误信息显示更久
      ...options
    })
  }

  // 警告通知
  warning(message: string, options?: Partial<NotificationOptions>) {
    return this.createNotification({
      type: 'warning',
      message,
      duration: 4000,
      ...options
    })
  }

  // 信息通知
  info(message: string, options?: Partial<NotificationOptions>) {
    return this.createNotification({
      type: 'info',
      message,
      ...options
    })
  }

  // 瑜伽主题通知
  yoga(message: string, options?: Partial<NotificationOptions>) {
    return this.createNotification({
      type: 'yoga',
      message,
      floating: true,
      ...options
    })
  }

  // 预约成功通知
  bookingSuccess(message: string = '预约成功！') {
    return this.yoga(message, {
      title: '预约成功',
      duration: 3000,
      action: {
        text: '查看预约',
        handler: () => {
          // 可以在这里添加跳转到预约页面的逻辑
          window.location.hash = '#/bookings'
        }
      }
    })
  }

  // 登录成功通知
  loginSuccess(username?: string) {
    const message = username ? `欢迎回来，${username}！` : '登录成功！'
    return this.yoga(message, {
      title: '登录成功',
      duration: 2000
    })
  }

  // 网络错误通知
  networkError(message: string = '网络连接异常，请检查网络设置') {
    return this.error(message, {
      title: '网络错误',
      duration: 6000,
      action: {
        text: '重试',
        handler: () => {
          window.location.reload()
        }
      }
    })
  }

  // 清除所有通知
  clear() {
    this.notifications.forEach(app => {
      app.unmount()
    })
    this.notifications = []
    
    // 清理所有通知容器
    const containers = document.querySelectorAll('[data-v-notification]')
    containers.forEach(container => {
      if (container.parentNode) {
        container.parentNode.removeChild(container)
      }
    })
  }
}

// 创建单例实例
export const notification = new NotificationService()

// 默认导出
export default notification

// 为了方便使用，也导出各种快捷方法
export const {
  success,
  error,
  warning,
  info,
  yoga,
  bookingSuccess,
  loginSuccess,
  networkError,
  clear
} = notification 